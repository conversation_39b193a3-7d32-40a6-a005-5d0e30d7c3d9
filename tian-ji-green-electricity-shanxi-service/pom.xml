<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.fx</groupId>
        <artifactId>tian-ji-green-electricity-shanxi</artifactId>
        <version>1.0.0.RELEASE</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>tian-ji-green-electricity-shanxi-service</artifactId>
    <version>${parent.version}</version>
    <name>>${project.artifactId}</name>
    <description>山西绿电直连平台业务服务</description>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <!--sentinel-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--Spring Cache-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>

        <!--db-->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.2.22</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-common-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.deepoove</groupId>
                    <artifactId>poi-tl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-common-file</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.3</version>
        </dependency>
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.12.2</version>
        </dependency>

        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-green-electricity-shanxi-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-operation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-data-gather-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-influx-db-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-websocket-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-common-file</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-auxiliary-gather-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-public-data-shanxi-api</artifactId>
            <classifier>jdk1.8</classifier>
        </dependency>

        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
        </dependency>

    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
    </build>
</project>