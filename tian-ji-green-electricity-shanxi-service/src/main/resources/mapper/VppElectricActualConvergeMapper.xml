<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fx.green.electricity.shanxi.service.mapper.electric.VppElectricActualConvergeMapper">


    <select id="queryElectricByUserIdLists" resultType="com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge">
        SELECT
        t1.ID,
        t1.NAME,
        t1.tenant_id,
        t1.electric_id,
        t1.date_day,
        t1.time_frame,
        t1.electricity,
        t1.user_id,
        t1.is_delete,
        t2.user_code
        FROM
        vpp_electric_actual_converge t1
        left join vpp_user t2 on t1.user_id=t2.id
        WHERE
            t1.is_delete = 0
        AND ( t1.user_id IN
        <foreach collection='userIdList' item='id' open='(' separator=',' close=')'>
            #{id}
        </foreach>

        AND t1.date_day &gt;=#{startTime} AND t1.date_day &lt;=#{endTime} AND t1.tenant_id = #{tenantId} )
    </select>
    <select id="queryElectricByUserIdListNew"
            resultType="com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge">
        WITH user_ids AS (
        SELECT id
        FROM unnest(
        ARRAY[
        <foreach collection='userIdList' item='id' open='' separator=',' close=''>
            #{id}
        </foreach>
        ]
        ) AS id
        )
        SELECT
        t2.user_code,
        e.date_day,
        e.time_frame,
        e.electricity
        FROM vpp_electric_actual_converge e
        left join vpp_user t2 on e.user_id=t2.id
        JOIN user_ids u ON e.user_id = u.id
        WHERE e.is_delete = 0
        AND e.date_day &gt;=#{startTime}
        AND e.date_day &lt;=#{endTime}
        AND e.tenant_id = #{tenantId};
    </select>


    <select id="getElectricByDate"
            resultType="com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge">
        SELECT
            date_day,
            time_frame,
            electricity
        FROM vpp_electric_actual_24
        WHERE date_day &gt;= #{startTime}
          AND date_day &lt;= #{endTime}
          and tenant_id = #{tenantId}
        ORDER BY date_day, time_frame
    </select>

    <delete id="removeByParam">
        delete
        from vpp_electric_actual_converge
        where tenant_id = #{tenantId}
          and date_day = #{dateDay}
    </delete>
</mapper>
