<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fx.green.electricity.shanxi.service.mapper.user.VppLoadUserMapper">

    <select id="getLoadUserNumberCount" resultType="Integer">
        select count(1) from vpp_user where cons_no=#{consNo} AND is_delete = 0
        <if test="userId != null">
            AND id != #{userId}
        </if>
    </select>

    <select id="getUserIds" resultType="String">
        SELECT id
        FROM vpp_user
        WHERE is_delete = 0
          AND vpp_id = (SELECT ID FROM vpp_base WHERE tenant_id = #{tenantId} AND is_delete = 0)
    </select>

    <select id="getAllUserCode" resultType="com.fx.green.electricity.shanxi.service.entity.user.VppUser">
        SELECT id,
               user_code,
               name,
               vpp_id,
               tenant_id
        FROM vpp_user
        WHERE is_delete = 0
    </select>

    <select id="getAllUserList" resultType="com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDTO">
        select id,
               name,
               vpp_id,
               tenant_id,
               open_id
        from vpp_user
        WHERE is_delete = 0
          AND open_id IS NOT NULL
    </select>


    <select id="getAllUser" resultType="Long">
        SELECT id
        FROM vpp_user
        WHERE is_delete = 0
          AND vpp_id = #{vppId}
    </select>


    <select id="getLoadUserInfo" resultType="com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDTO">
        select id, user_code
        from vpp_user
        where is_delete = 0
          and name = #{name}
          and tenant_id = #{tenantId}
    </select>

    <select id="getUserByOpenId" resultType="com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO">
        select *
        from vpp_user
        where is_delete = 0
          and open_id = #{openId}
    </select>

    <update id="updateAllOpenId">
        update vpp_user
        set open_id = NULL
        where open_id = #{openId}
    </update>

</mapper>
