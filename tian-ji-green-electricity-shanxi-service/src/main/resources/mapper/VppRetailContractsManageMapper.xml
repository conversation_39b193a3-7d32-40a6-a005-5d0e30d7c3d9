<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fx.green.electricity.shanxi.service.mapper.retail.VppRetailContractsManageMapper">

    <select id="getList" resultType="com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageListVO">
        SELECT signing_month,
        "name",
        round(dividend_shar_coefficient,2) dividendSharCoefficient,
        user_id,
        retail_contracts_id,
        trade_name
        FROM vpp_retail_contracts_manage
        where signing_month &gt;= #{param.startDate}
        AND signing_month &lt;= #{param.endDate}
        AND is_delete = 0
        AND tenant_id = #{param.tenantId}
        <if test="param.name!= null and param.name!=''">
            AND name like concat('%',#{param.name},'%')
        </if>
        <if test="param.tradeName!= null and param.tradeName!=''">
            AND trade_name like concat('%',#{param.tradeName},'%')
        </if>
        <if test="param.dividendSharCoefficient!= null and param.dividendSharCoefficient!=''">
            AND dividend_shar_coefficient = #{param.dividendSharCoefficient}
        </if>
        GROUP BY signing_month,
        trade_name,
        "name",
        dividend_shar_coefficient,
        user_id,
        retail_contracts_id
        order by signing_month
    </select>

    <select id="retailContractsManageDetail" resultType="com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContractsManage">
        SELECT ID
                ,
               signing_month,
               retail_contracts_id,
               NAME,
               round(dividend_shar_coefficient,3) dividendSharCoefficient,
               CYCLE,
               TYPE,
               time_frame,
               round(val,3) val,
               user_id,
               is_delete,
               create_time,
               create_user,
               update_time,
               update_user
        FROM
            vpp_retail_contracts_manage
                ${ew.customSqlSegment}
    </select>

    <delete id="removeData" >
        delete from   vpp_retail_contracts_manage where signing_month=#{signingMonth} and retail_contracts_id=#{retailContractsId}
        and tenant_id=#{tenantId}
    </delete>

    <delete id="removeDataList" >
        delete from   vpp_retail_contracts_manage where signing_month=#{signingMonth}  and tenant_id=#{tenantId} and retail_contracts_id=#{retailContractsId}
    </delete>

    <select id="getUserDividendList" resultType="com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContractsManage">
        SELECT
            user_id,
            dividend_shar_coefficient
        FROM
            vpp_retail_contracts_manage
        WHERE
                retail_contracts_id = ( SELECT ID FROM vpp_retail_contracts WHERE status = 1  and tenant_id =#{param.tenantId} ORDER BY run_month DESC LIMIT 1 )
          AND TYPE = 2
        GROUP BY
            user_id,
            dividend_shar_coefficient
    </select>

</mapper>
