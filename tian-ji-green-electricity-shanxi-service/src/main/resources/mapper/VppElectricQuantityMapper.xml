<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fx.green.electricity.shanxi.service.mapper.electric.VppElectricQuantityMapper">

    <select id="findByWrapper" resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricQuantityVO">
        SELECT veq.*
        FROM vpp_electric_quantity veq
            ${ew.customSqlSegment}
    </select>

    <select id="getAllReportElectricity"
            resultType="com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricDeclareVO">
        select date_day,
               time_frame,
               electricity,
               vpp_id,
               tenant_id
        from
            se_electric_declare
        where tenant_id = #{param.tenantId}
          AND vpp_id = #{param.vppId}
          AND date_day &gt;= #{param.startTime}
          AND date_day &lt;= #{param.endTime}
          AND is_delete = 0
    </select>

</mapper>