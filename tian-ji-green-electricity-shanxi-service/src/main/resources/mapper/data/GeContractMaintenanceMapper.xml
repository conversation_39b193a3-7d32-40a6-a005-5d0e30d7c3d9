<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fx.green.electricity.shanxi.service.mapper.data.GeContractMaintenanceMapper">

    <select id="contractList" resultType="com.fx.green.electricity.shanxi.api.pojo.vo.data.GeContractMaintenanceVO">
        SELECT gc.*,
               gt.tenant_name,
               gu.unit_name
        FROM ge_contract_maintenance gc
                 LEFT JOIN ge_tenant gt ON gc.tenant_id = gt."id"
                 LEFT JOIN ge_unit_basic gu ON gu."id" = gc.unit_id
            ${ew.customSqlSegment}
    </select>
</mapper>
