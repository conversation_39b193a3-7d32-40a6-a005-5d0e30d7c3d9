<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fx.green.electricity.shanxi.service.mapper.other.VppPartitionTableMapper">

    <select id="countPartitionTable" resultType="Integer">
        select count(1) from pg_statio_user_tables where relname = #{tableName}
    </select>

    <select id="countPartitionTableOG" resultType="Integer">
        select count(1) from PG_PARTITION where relname = #{tableName}
    </select>

    <update id="createPartitionTable">
        create table ${tableName} partition of ${masterTable} for values from ('${month}') to ('${nextMonth}')
    </update>

    <update id="createPartitionTableOG">
        ALTER TABLE ${masterTable} ADD PARTITION ${tableName} VALUES LESS THAN ('${nextMonth}')
    </update>
</mapper>