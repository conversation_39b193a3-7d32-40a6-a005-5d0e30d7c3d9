package com.fx.green.electricity.shanxi.service.entity.predicted;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
*
**/
@Data
@ApiModel("预测电量确认表")
public class VppPredictedElectricityConfirm implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;
    @ApiModelProperty("日期")
    private Object dateDay;
    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("用户名字")
    private String name;
    @ApiModelProperty("时点")
    private String timeFrame;
    @ApiModelProperty("电量")
    private BigDecimal electricity;
    @ApiModelProperty("预测维度 1.日 2.上旬 3.中旬 4.下旬 5.月")
    private Integer dimension;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改时间")
    private Date updateTime;

}