package com.fx.green.electricity.shanxi.service.mapper.other;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 分区表数据库操作
 *
 * <AUTHOR>
 **/
public interface VppPartitionTableMapper extends BaseMapper<Void> {
    /**
     * 查看是否存在分区表
     *
     * @param tableName 分区表名
     * @return 表数量
     */
    Integer countPartitionTable(@Param("tableName") String tableName);

    /**
     * 查看是否存在分区表(opengauss)
     *
     * @param tableName 分区表名
     * @return 表数量
     */
    Integer countPartitionTableOG(@Param("tableName") String tableName);

    /**
     * 按月创建分区表
     *
     * @param tableName   分区表名
     * @param masterTable 主表
     * @param month       开始月
     * @param nextMonth   结束月
     */
    void createPartitionTable(@Param("tableName") String tableName, @Param("masterTable") String masterTable,
                              @Param("month") String month, @Param("nextMonth") String nextMonth);

    /**
     * 按月创建分区表 (opengauss)
     *
     * @param tableName   分区表名
     * @param masterTable 主表
     * @param month       开始月
     * @param nextMonth   结束月
     */
    void createPartitionTableOG(@Param("tableName") String tableName, @Param("masterTable") String masterTable,
                                @Param("month") String month, @Param("nextMonth") String nextMonth);
}
