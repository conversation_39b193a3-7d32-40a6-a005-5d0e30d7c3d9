package com.fx.green.electricity.shanxi.service.entity.user;

import com.fx.green.electricity.shanxi.service.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 负荷用户电表
 *
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("负荷用户电表")
public class VppUserMeter extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备id")
    private Long id;

    @ApiModelProperty("负荷用户id")
    private Long userId;

    @ApiModelProperty("户号id")
    private Long accountId;

    @ApiModelProperty("设备id")
    private Long deviceId;

    @ApiModelProperty("用户户号")
    private String consNo;

    @ApiModelProperty("计量点")
    private String meterPoint;

    @ApiModelProperty("电表名称")
    private String meterName;

    @ApiModelProperty("电表表号")
    private String meterNo;

    @ApiModelProperty("租户id")
    private Long tenantId;

}