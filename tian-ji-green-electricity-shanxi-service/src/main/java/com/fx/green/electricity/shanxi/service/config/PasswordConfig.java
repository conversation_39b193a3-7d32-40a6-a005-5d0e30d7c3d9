package com.fx.green.electricity.shanxi.service.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * <AUTHOR>
 */

@Configuration
public class PasswordConfig {

    public static final String DEFAULT_PASSWORD = "sxtjy@202388";

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

}
