package com.fx.green.electricity.shanxi.service.mapper.data;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeFileRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO;
import com.fx.green.electricity.shanxi.service.entity.data.GeFileRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 文件导入记录
 */
@Component
public interface GeFileRecordMapper extends BaseMapper<GeFileRecord> {

    /**
     * 文件导入记录  所属机组信息
     *
     * @param wrapper
     * @return
     */
    List<GeFileRecordVO> getFileRecordByUnit(@Param(Constants.WRAPPER) Wrapper<GeFileRecordDTO> wrapper);


    /**
     * 文件导入记录  所属租户信息
     *
     * @param wrapper
     * @return
     */
    List<GeFileRecordVO> getFileRecordByTenant(@Param(Constants.WRAPPER) Wrapper<GeFileRecordDTO> wrapper);
}
