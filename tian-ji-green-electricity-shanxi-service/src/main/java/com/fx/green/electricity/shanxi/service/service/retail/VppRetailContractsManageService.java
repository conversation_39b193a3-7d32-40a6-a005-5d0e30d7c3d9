package com.fx.green.electricity.shanxi.service.service.retail;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryImportRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.RetailContractsManageDetailDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.UpdateRetailContractsManageDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageVO;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContractsManage;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
public interface VppRetailContractsManageService extends IService<VppRetailContractsManage> {

    /**
     * 添加导入数据
     *
     * @param resultList
     * @param queryImportRecordDTO
     * @return
     */
    DataResult<Void> addList(List<VppRetailContractsManageVO> resultList, QueryImportRecordDTO queryImportRecordDTO);

    /**
     * 零售合同管理列表
     *
     * @param param
     * @return
     */
    DataResult<FxPage<VppRetailContractsManageListVO>> getList(QueryVppRetailContractsDTO param);

    /**
     * 获取详情
     *
     * @param retailContractsManageDetailDTO
     * @return
     */
    DataResult<VppRetailContractsManageDetailVO> retailContractsManageDetail(RetailContractsManageDetailDTO retailContractsManageDetailDTO);

    /**
     * 通过日期 用户id获取信息
     *
     * @param tenantId
     * @param id
     * @param date
     * @return
     */
    List<VppRetailContractsManage> getUserPrice(Long tenantId, Long id, Date date);

    /**
     * 获取全部用户的价格
     *
     * @param tenantId
     * @param userList
     * @param date
     * @return
     */
    List<VppRetailContractsManage> getUserListPrice(Long tenantId, List<VppUserDTO> userList, Date date);

    /**
     * 获取用户零售价格
     *
     * @param userIdList 用户列表
     * @param date       日期
     * @return 用户零售价格列表
     */
    List<VppRetailContractsManage> getUserListPrice(List<Long> userIdList, Date date);


    List<VppRetailContractsManage> getUserDividendList(VppUserDTO.SearchDTO param);

    /**
     * 获取全部的零售合同信息
     *
     * @param queryVppRetailContractsDTO
     * @return
     */
    List<VppRetailContractsManageListVO> getAllList(QueryVppRetailContractsDTO queryVppRetailContractsDTO);

    /**
     * 修改红利系数
     *
     * @param param
     * @return
     */
    DataResult<Void> updateDividendCoefficient(UpdateRetailContractsManageDTO param);

    /**
     * 导出用户列表
     *
     * @param param
     * @return
     */
    DataResult<List<VppRetailContractsManageListVO>> exportManageList(QueryVppRetailContractsDTO param);

}
