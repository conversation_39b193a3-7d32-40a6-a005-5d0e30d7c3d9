package com.fx.green.electricity.shanxi.service.mapper.electric;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.WholesaleAnalysisDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricQuantityVO;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricQuantity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 **/
@Component
public interface VppElectricQuantityMapper extends BaseMapper<VppElectricQuantity> {


    /**
     * 根据时间和类型查询详情
     *
     * @param wrapper 时间、类型
     * @return 查询结果
     */
    VppElectricQuantityVO findByWrapper(@Param(Constants.WRAPPER) Wrapper<VppElectricQuantityVO> wrapper);

    /**
     * 获取申报电量多天的数据
     *
     * @return
     */
    List<VppElectricDeclareVO> getAllReportElectricity(@Param("param") WholesaleAnalysisDTO param);
}
