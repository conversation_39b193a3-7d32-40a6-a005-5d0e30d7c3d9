package com.fx.green.electricity.shanxi.service.service.data.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fx.common.constant.DataResult;
import com.fx.common.exception.FxServiceException;
import com.fx.data.gather.api.api.DataGatherVppApi;
import com.fx.data.gather.api.dto.vpp.TenantWithUserAssocDTO;
import com.fx.data.gather.api.dto.vpp.VppGroupDataByTenantDTO;
import com.fx.green.electricity.shanxi.service.service.data.DataGatherService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import com.fx.green.electricity.shanxi.service.utils.MonthDaysUtil;
import com.fx.green.electricity.shanxi.service.utils.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 关口表数据相关
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class DataGatherServiceImpl implements DataGatherService {

    @Autowired
    private VppLoadUserService vppLoadUserService;

    @Autowired
    private DataGatherVppApi dataGatherVppApi;

    @Override
    public void minuteGroupData() {
        DateTime endTime = TimeUtils.date();
        DateTime startTime = DateUtil.offsetMinute(endTime, -5);
        // 调用数据计算
        this.dateGroupData(startTime, endTime);
    }

    /**
     * 一定是第二天执行当天的数据计算
     */
    @Override
    public void dayGroupData() {
        DateTime endTime = DateUtil.beginOfDay(TimeUtils.date());
        DateTime startTime = DateUtil.offsetDay(endTime, -1);
        // 需要往后便宜1分钟，否则不会计算24点的数据
        endTime = DateUtil.offsetMinute(endTime, 1);
        // 调用数据计算
        this.dateGroupData(startTime, endTime);
    }

    /**
     * 一定要按月进行计算，不能直接全计算，每个月的代理关联关系是不同的。
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @Override
    public void dateGroupData(Date startTime, Date endTime) {
        // 获取月份列表
        LinkedHashMap<Date, List<Date>> dateListLinkedHashMap = MonthDaysUtil.divideTimeByMonth(startTime, endTime);
        for (Map.Entry<Date, List<Date>> dateList : dateListLinkedHashMap.entrySet()) {
            Date month = dateList.getKey();
            List<Date> startWithEndTime = dateList.getValue();
            // 查询所有的租户和用电用户关联
            List<TenantWithUserAssocDTO> tenantWithUserAssocByMonth = vppLoadUserService.getTenantWithUserAssocByMonth(month, null);

            // 按日进行计算
            List<DateTime> dateTimes = DateUtil.rangeToList(startWithEndTime.get(0), startWithEndTime.get(1), DateField.DAY_OF_YEAR);
            if (dateTimes.size() == 1) {
                // 如果只有一天，则为15分钟的数据计算
                VppGroupDataByTenantDTO vppGroupDataByTenantDTO = new VppGroupDataByTenantDTO();
                vppGroupDataByTenantDTO.setAssocList(tenantWithUserAssocByMonth);
                vppGroupDataByTenantDTO.setStartTime(startWithEndTime.get(0));
                vppGroupDataByTenantDTO.setEndTime(startWithEndTime.get(1));
                DataResult<Void> result = dataGatherVppApi.groupData(vppGroupDataByTenantDTO);
                if (!result.isSuccess()) {
                    throw new FxServiceException(result);
                }
                log.info("{} 的关口表数据计算 完成", startWithEndTime.get(0));
            } else {
                // 这个是多天的数据计算
                for (int i = 0; i < dateTimes.size() - 1; i++) {
                    DateTime dateTime1 = dateTimes.get(i);
                    DateTime dateTime2 = dateTimes.get(i + 1);

                    dateTime2 = DateUtil.offsetMinute(dateTime2, 1);

                    log.info("{} 的关口表数据计算 开始", dateTime1);

                    // 请求data-gather，让data-gather进行计算
                    // 如果虚拟电厂程序停了会影响data-gather的计算，补救措施是每天凌晨的昨日重新计算，否则只能是手动补
                    VppGroupDataByTenantDTO vppGroupDataByTenantDTO = new VppGroupDataByTenantDTO();
                    vppGroupDataByTenantDTO.setAssocList(tenantWithUserAssocByMonth);
                    vppGroupDataByTenantDTO.setStartTime(dateTime1);
                    vppGroupDataByTenantDTO.setEndTime(dateTime2);
                    DataResult<Void> result = dataGatherVppApi.groupData(vppGroupDataByTenantDTO);
                    if (!result.isSuccess()) {
                        throw new FxServiceException(result);
                    }
                    log.info("{} 的关口表数据计算 完成", dateTime1);
                }
            }
        }
    }

    @Override
    public void updateGroupData(Date dateTime, String userCode) {

        Date month = DateUtil.beginOfMonth(dateTime);

        // 查询所有的租户和用电用户关联
        List<TenantWithUserAssocDTO> tenantWithUserAssocByMonth = vppLoadUserService.getTenantWithUserAssocByMonth(month, userCode);

        // 按日进行计算
        Date startTime = DateUtil.offsetMinute(dateTime, 1);
        Date endTime = DateUtil.offsetDay(startTime, 1);

        log.info("{} 按日聚合计算，仅计算某个虚拟电厂 开始", startTime);

        // 请求data-gather，让data-gather进行计算
        // 如果虚拟电厂程序停了会影响data-gather的计算，补救措施是每天凌晨的昨日重新计算，否则只能是手动补
        VppGroupDataByTenantDTO vppGroupDataByTenantDTO = new VppGroupDataByTenantDTO();
        vppGroupDataByTenantDTO.setAssocList(tenantWithUserAssocByMonth);
        vppGroupDataByTenantDTO.setStartTime(startTime);
        vppGroupDataByTenantDTO.setEndTime(endTime);
        DataResult<Void> result = dataGatherVppApi.updateGroupData(vppGroupDataByTenantDTO);
        if (!result.isSuccess()) {
            throw new FxServiceException(result);
        }
        log.info("{} 按日聚合计算，仅计算某个虚拟电厂 完成", startTime);
    }


}
