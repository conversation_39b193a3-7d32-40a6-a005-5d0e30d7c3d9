package com.fx.green.electricity.shanxi.service.enums.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 绑定状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BindStatusEnum {
    /**
     * 绑定状态定义
     */
    BOUND(1, "绑定中", "用户当前处于绑定周期内"),
    UNBOUND(2, "未绑定", "用户当前未处于绑定周期内");

    /**
     * 状态代码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据代码获取绑定状态
     *
     * @param code 状态代码
     * @return 绑定状态枚举，如果未找到返回null
     */
    public static BindStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BindStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据代码获取绑定状态名称
     *
     * @param code 状态代码
     * @return 绑定状态名称，如果未找到返回"未知状态"
     */
    public static String getNameByCode(Integer code) {
        BindStatusEnum status = getByCode(code);
        return status != null ? status.getName() : "未知状态";
    }

    /**
     * 判断是否为绑定状态
     *
     * @param code 状态代码
     * @return true-绑定中，false-未绑定或未知状态
     */
    public static boolean isBound(Integer code) {
        return BOUND.code.equals(code);
    }

    /**
     * 判断是否为未绑定状态
     *
     * @param code 状态代码
     * @return true-未绑定，false-绑定中或未知状态
     */
    public static boolean isUnbound(Integer code) {
        return UNBOUND.code.equals(code);
    }
}
