package com.fx.green.electricity.shanxi.service.entity.predicted;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 关口表数据存在问题的日期
 **/
@Data
@ApiModel("关口表数据存在问题的日期")
public class VppPredictedExcludeDay implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;
    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("用户名字")
    private String userName;
    @ApiModelProperty("统一社会信用代码")
    private String userCode;
    @ApiModelProperty("日期")
    private Date dateDay;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}