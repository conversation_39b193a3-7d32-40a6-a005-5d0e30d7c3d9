package com.fx.green.electricity.shanxi.service.entity.unit;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备表
 */
@Data
@ApiModel("设备表")
public class GeDevice implements Serializable {
    private static final long serialVersionUID = -525886433686176836L;

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("机组id")
    private Long unitId;
    @ApiModelProperty("设备名称")
    private String deviceName;
    @ApiModelProperty("设备编号")
    private String deviceCode;
    @ApiModelProperty("详细地址")
    private String address;
    @ApiModelProperty("经纬度")
    private String coordinate;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateUser;
}
