package com.fx.green.electricity.shanxi.service.utils;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fx.common.constant.TimeConstant;
import com.fx.common.util.MyDateUtil;
import com.fx.green.electricity.shanxi.service.enums.DecimalPlaceEnum;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 点数转换,96转24点
 *
 * <AUTHOR>
 **/
public class PointConvertUtil {

    public static final String AVG = "AVG";
    public static final String SUM = "SUM";

    public static final int TWENTY_FOUR = 24;

    public static final int NINETY_SIX = 96;

    public static List<String> make96To24Str(List<String> ninetySixData, String model) {
        List<BigDecimal> toBigdecimalList = ninetySixData.stream().map(o -> {
            if (CharSequenceUtil.isNotBlank(o)) {
                return new BigDecimal(o);
            }
            return null;
        }).collect(Collectors.toList());
        List<BigDecimal> bigDecimals = make96To24(toBigdecimalList, model);
        if (CollUtil.isEmpty(bigDecimals)) {
            return new ArrayList<>();
        }
        return bigDecimals.stream().map(o -> {
            if (ObjectUtil.isNull(o)) {
                return "";
            } else {
                return o.toPlainString();
            }
        }).collect(Collectors.toList());
    }

    /**
     * 96点bigDecimal集合转为24点BigDecimal集合
     *
     * @param ninetySixData 96点数据
     * @param model         每四点求和或平均
     * @return 24点BigDecimal集合
     */
    public static List<BigDecimal> make96To24(List<BigDecimal> ninetySixData, String model) {
        if (CollUtil.isEmpty(ninetySixData)) {
            return new ArrayList<>();
        }
        if (ninetySixData.size() % NINETY_SIX != 0) {
            return new ArrayList<>();
        }
        List<BigDecimal> newList = new ArrayList<>();
        int fourNullNum = 0;
        BigDecimal temp = BigDecimal.ZERO;
        for (int i = 1; i <= ninetySixData.size(); i++) {
            // 如果为空标记+1
            if (ObjectUtil.isNull(ninetySixData.get(i - 1))) {
                fourNullNum++;
                temp = temp.add(BigDecimal.ZERO);
            } else {
                temp = temp.add(ninetySixData.get(i - 1));
            }

            // 每四个一组,求和或求平均
            if (i % 4 == 0) {
                if (fourNullNum == 4) {
                    newList.add(null);
                } else {
                    if (model.equalsIgnoreCase(AVG)) {
                        newList.add(DecimalPlaceUtil.divide(temp, new BigDecimal(4), DecimalPlaceEnum.ELECTRIC_QUANTITY));
                    }
                    if (model.equalsIgnoreCase(SUM)) {
                        newList.add(temp);
                    }
                }
                fourNullNum = 0;
                temp = BigDecimal.ZERO;
            }
        }
        return newList;
    }


    public static List<String> buildDateList(Date startTime, Date endTime, int model) {
        String[] timeNode = {};
        if (model == TWENTY_FOUR) {
            timeNode = TimeConstant.TWENTY_FOUR_TIMES;
        }
        if (model == NINETY_SIX) {
            timeNode = TimeConstant.NINETY_SIX_TIMES;
        }
        List<String> dateList = new ArrayList<>();
        List<String> dateRange = MyDateUtil.getDateRange(startTime, endTime);
        for (String date : dateRange) {
            for (String timeFrame : timeNode) {
                dateList.add(date + " " + timeFrame);
            }
        }
        return dateList;
    }

    public static void main(String[] args) {
        //List<BigDecimal> testList = new ArrayList<>();
        //for (int i = 0; i < 192; i++) {
        //    if (i < 8) {
        //        testList.add(null);
        //    } else {
        //        testList.add(new BigDecimal(i));
        //    }
        //}
        //System.out.println(testList);
        //List<BigDecimal> bigDecimals = make96To24(testList, AVG);
        //System.out.println(bigDecimals.size());
        //System.out.println(bigDecimals);

        HashMap<String, Object> map = new HashMap<>();
        map.put("00:15", 1);
        map.put("00:30", 1);
        map.put("00:45", 1);
        Map<String, Object> stringObjectMap = ninetySixTo288(map);
        System.out.println(stringObjectMap);
        System.out.println(stringObjectMap.size());
        for (Map.Entry<String, Object> stringObjectEntry : stringObjectMap.entrySet()) {
            if (ObjectUtil.isNotNull(stringObjectEntry.getValue())) {
                System.out.println(stringObjectEntry.getKey() + ":::" + stringObjectEntry.getValue());
            }
        }

    }

    /**
     * 96点Map转为24点
     * key 必须为 01:00-03:00 格式
     *
     * @param param 96点map
     * @param <T>   数据
     * @param num   96or24
     * @return 24点map
     */
    public static <T> Map<String, T> to24Or96(Map<String, T> param, Integer num) {
        if (CollUtil.isEmpty(param)) {
            return param;
        }
        String[] timeArray = null;
        if (num == 24) {
            timeArray = TimeConstant.TWENTY_FOUR_TIMES;
        } else {
            timeArray = TimeConstant.NINETY_SIX_TIMES;
        }
        Map<String, T> resultMap = new HashMap<>();
        for (String twentyFourTime : timeArray) {
            for (Map.Entry<String, T> mapEntry : param.entrySet()) {
                String[] times = mapEntry.getKey().split("-");
                Date startTime = MyDateUtil.parse(times[0], "HH:mm");
                Date endTime = MyDateUtil.parse(times[1], "HH:mm");
                Date compareTime = MyDateUtil.parse(twentyFourTime, "HH:mm");
                if (DateUtil.compare(startTime, compareTime) <= 0 && DateUtil.compare(endTime, compareTime) >= 0) {
                    resultMap.put(twentyFourTime, mapEntry.getValue());
                    break;
                } else {
                    resultMap.put(twentyFourTime, null);
                }
            }
        }
        return resultMap;
    }

    /**
     * 96点Map转为384点
     * key 必须为 00:15 格式
     *
     * @param ninetySixMap 96点map
     * @param <T>          数据
     * @return 384点map
     */
    public static <T> Map<String, T> ninetySixTo288(Map<String, T> ninetySixMap) {
        if (CollUtil.isEmpty(ninetySixMap)) {
            return ninetySixMap;
        }

        List<String> dateListOf1444 = new ArrayList<>();
        for (int i = 0; i < 1440; i++) {
            dateListOf1444.add(String.format("%02d:%02d", (i + 1) / 60, (i + 1) % 60));
        }

        LinkedHashMap<String, String> convertMap = new LinkedHashMap<>();
        for (String twoHundredEightyEightTime : dateListOf1444) {
            for (String ninetySixTime : TimeConstant.NINETY_SIX_TIMES) {
                if (DateUtil.compare(MyDateUtil.parse(twoHundredEightyEightTime, "HH:mm"), MyDateUtil.parse(ninetySixTime, "HH:mm")) <= 0) {
                    convertMap.put(twoHundredEightyEightTime, ninetySixTime);
                    break;
                }
            }
        }

        Map<String, T> resultMap = new HashMap<>();
        for (Map.Entry<String, String> convertEntry : convertMap.entrySet()) {
            resultMap.put(convertEntry.getKey(), ninetySixMap.getOrDefault(convertEntry.getValue(), null));
        }
        return resultMap;
    }
}
