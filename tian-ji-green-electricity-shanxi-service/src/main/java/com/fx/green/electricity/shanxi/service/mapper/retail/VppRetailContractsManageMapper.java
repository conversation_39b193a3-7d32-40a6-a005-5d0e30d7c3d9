package com.fx.green.electricity.shanxi.service.mapper.retail;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageListVO;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContractsManage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Component
public interface VppRetailContractsManageMapper extends BaseMapper<VppRetailContractsManage> {

    /**
     * 获取零售合同管理列表
     *
     * @param param
     * @return
     */
    List<VppRetailContractsManageListVO> getList(@Param("param") QueryVppRetailContractsDTO param);

    /**
     * 获取零售合同管理详情
     *
     * @param wrapper
     * @return
     */
    List<VppRetailContractsManage> retailContractsManageDetail(@Param(Constants.WRAPPER) Wrapper<VppRetailContractsManage> wrapper);

    /**
     * 删除之前的数据
     *
     * @param signingMonth
     * @param retailContractsId
     */
    void removeData(@Param("signingMonth") Date signingMonth, @Param("retailContractsId") Long retailContractsId, @Param("tenantId") Long tenantId);

    /**
     * 获取红利系数
     * @param param
     * @return
     */
    List<VppRetailContractsManage> getUserDividendList(@Param("param") VppUserDTO.SearchDTO param);

    void removeDataList(@Param("signingMonth") Date signingMonth,@Param("tenantId") Long tenantId,@Param("retailContractsId") Long retailContractsId);
}
