package com.fx.green.electricity.shanxi.service.service.electric;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.ExcessMLDeclareCalDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.ResultAnalysisDTO;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge;

import java.util.Date;
import java.util.List;

/**
 * 实际用电量合并 Service 接口
 *
 * <AUTHOR>
 */
public interface VppElectricActualConvergeService extends IService<VppElectricActualConverge> {

    /**
     * 根据用户id列表查询实际电量列表
     *
     * @param userCodeList 用户code列表
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param tenantId     租户id
     * @return 实际电量列表
     * @return
     */
    List<VppElectricActualConverge> queryElectricByUserIdList(List<Long> userCodeList,
                                                              Date startTime,
                                                              Date endTime,
                                                              Long tenantId);

    /**
     * 根据用户id列表查询实际电量列表
     *
     * @param userIdList 用户code列表
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param tenantId   租户id
     * @return 实际电量列表
     * @return
     */
    List<VppElectricActualConverge> queryElectricByUserIdLists(List<Long> userIdList, Date startTime, Date endTime, Long tenantId);

    /**
     * 根据用户id列表查询实际电量列表
     *
     * @param userIdList 用户code列表
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param tenantId   租户id
     * @return 实际电量列表
     * @return
     */
    List<VppElectricActualConverge> queryElectricByUserIdListNew(List<Long> userIdList, Date startTime, Date endTime, Long tenantId);

    void removeByParam(Date dateDay, Long tenantId);

    /**
     * 获取24小时的实际用电量
     *
     * @param param
     * @return
     */
    List<VppElectricActualConverge> getElectricByDate(ExcessMLDeclareCalDTO param);

    List<VppElectricActualConverge> getElecList(ResultAnalysisDTO param, String startTime, String endTime);
}

