package com.fx.green.electricity.shanxi.service.service.production;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryUserProductionDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.production.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionDataDeviceListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatisticsListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatisticsVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatusVO;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionStatistics;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户生产统计 - 主表 Service 接口
 *
 * <AUTHOR>
 */
public interface VppUserProductionStatisticsService extends IService<VppUserProductionStatistics> {


    /**
     * 用户生产统计新增/编辑
     * @param param 用户生产计划DTO
     */
    void saveUserProductionPlan(VppUserProductionStatisticsDTO param);

    /**
     * 批量新增
     * @param param 用户生产计划DTO列表
     */
    void saveList(List<VppUserProductionStatisticsDTO> param);

    /**
     * 根据日期查询用户生产计划列表
     * @param param 用户生产计划查询DTO
     * @return 用户生产计划列表
     */
    VppUserProductionStatisticsListVO getProductionStatisticsByDay(VppUserProductionStatisticsQueryDTO param);

    /**
     * 根据用户id查询用户生产计划列表
     * @param param 用户生产计划查询DTO
     * @return 用户生产计划列表
     */
    VppUserProductionStatisticsVO getProductionStatisticsByUserId(VppUserProductionStatisticsQueryDetailDTO param);

    /**
     * 查询所有的用户生产计划列表
     * @param param 用户生产计划查询DTO
     * @return
     */
    List<VppUserProductionStatisticsVO> getProductionStatisticsList(VppUserProductionStatisticsQueryDetailDTO param);


    /**
     * 根据用户id和设备类型获取设备列表
     * @param param 用户生产计划查询DTO
     * @return 设备列表
     */
    List<VppUserProductionDataDeviceListVO> getDeviceListByType(VppUserProductionDataDeviceQueryDTO param);

    /**
     * 获取用户的生产状态列表
     * @param param 用户生产计划查询DTO
     * @return 生产状态列表
     */
    List<VppUserProductionStatusVO> getUserProductionStatus(@Valid VppUserProductionStatusQueryDTO param);

    /**
     * 获取所有用户的生产状态列表
     * @param param 用户生产计划查询DTO
     * @return 生产状态列表
     */
    List<VppUserProductionStatusVO> getUserProductionStatusByTenantId(IdDTO param);

    /**
     * 获取某个用户某个月的生产计划
     * @param param 用户生产计划查询DTO
     * @return 生产计划列表
     */
    List<VppUserProductionStatistics> getProductionStatisticsByUserIdList(QueryUserProductionDTO param);
}
