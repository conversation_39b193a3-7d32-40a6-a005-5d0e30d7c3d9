package com.fx.green.electricity.shanxi.service.controller.production;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.production.VppUserProductionDataDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionDataVO;
import com.fx.green.electricity.shanxi.service.service.production.VppUserProductionDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户生产数据 Controller
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "用户生产统计 - 用户生产数据")
@RequestMapping("/VppUserProductionData")
public class VppUserProductionDataController {

    @Resource
    private VppUserProductionDataService vppUserProductionDataService;

    @ApiOperation("根据用户id查询用户生产数据")
    @PostMapping("/findByUserId")
    public DataResult<VppUserProductionDataVO> findByUserId(@RequestBody IdDTO param) {
        return DataResult.success(vppUserProductionDataService.getProductionDataByUserId(param));
    }

    @CommonNoRepeat
    @ApiOperation("添加/修改用户生产数据")
    @PostMapping("/saveOrUpdate")
    public DataResult<Void> saveOrUpdate(@RequestBody VppUserProductionDataDTO param) {
        vppUserProductionDataService.saveOrUpdate(param);
        return DataResult.success();
    }

}