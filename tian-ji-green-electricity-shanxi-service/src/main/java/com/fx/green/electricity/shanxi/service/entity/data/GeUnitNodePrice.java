package com.fx.green.electricity.shanxi.service.entity.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 机组节点价格
 */
@Data
@ApiModel("机组节点价格")
public class GeUnitNodePrice implements Serializable {
    private static final long serialVersionUID = -2472832447570473788L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("机组id")
    private Long unitId;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("导入时间")
    private Date infoDate;

    @ApiModelProperty("时点")
    private String timePoint;

    @ApiModelProperty("数据值")
    private BigDecimal value;
}
