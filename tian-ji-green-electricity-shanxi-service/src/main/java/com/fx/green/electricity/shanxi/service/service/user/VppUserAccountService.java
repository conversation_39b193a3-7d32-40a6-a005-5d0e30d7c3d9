package com.fx.green.electricity.shanxi.service.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppLoadUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserAccountVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserAccount;

import java.util.List;

/**
* 用户户号 Service 接口
*
* <AUTHOR>
*/
public interface VppUserAccountService extends IService<VppUserAccount> {

    /**
     * 保存或更新用户户号信息
     *
     * @param param 用户户号信息
     */
    void saveOrUpdateData(VppLoadUserDTO param);

    /**
     * 获取用户户号列表
     *
     * @param tenantId 租户ID
     * @return 用户户号列表
     */
    List<VppUserAccount> getList(Long tenantId);

    /**
     * 删除用户户号
     *
     * @param vppUserId 用户户号ID
     */
    void deleteByUserId(Long vppUserId);

    /**
     * 获取用户户号
     *
     * @param id 用户ID
     * @return 用户户号
     */
    List<VppUserAccountVO> getUserAccount(Long id);
}
