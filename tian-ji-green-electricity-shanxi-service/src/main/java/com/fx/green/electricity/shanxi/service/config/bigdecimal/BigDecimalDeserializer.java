package com.fx.green.electricity.shanxi.service.config.bigdecimal;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.fx.green.electricity.shanxi.api.annotation.BigDecimalFormat;

import java.io.IOException;
import java.math.BigDecimal;

public class BigDecimalDeserializer extends JsonDeserializer<BigDecimal> implements ContextualDeserializer {

    private BigDecimalFormat formatAnnotation;

    // 无参构造用于Jackson实例化
    public BigDecimalDeserializer() {
    }

    // 带注解的构造
    public BigDecimalDeserializer(BigDecimalFormat formatAnnotation) {
        this.formatAnnotation = formatAnnotation;
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property)
            throws JsonMappingException {

        if (property != null) {
            BigDecimalFormat ann = property.getAnnotation(BigDecimalFormat.class);
            if (ann != null) {
                return new BigDecimalDeserializer(ann);
            }
        }
        return this;
    }

    @Override
    public BigDecimal deserialize(JsonParser p, DeserializationContext ctxt)
            throws IOException {

        String value = p.getValueAsString();
        if (value == null || value.isEmpty()) {
            return null;
        }

        try {
            BigDecimal decimal = new BigDecimal(value);

            if (formatAnnotation != null) {
                return decimal.setScale(
                        formatAnnotation.scale(),
                        formatAnnotation.roundingMode()
                );
            }
            return decimal;
        } catch (NumberFormatException e) {
            throw new IOException("Invalid BigDecimal value: " + value, e);
        }
    }
}