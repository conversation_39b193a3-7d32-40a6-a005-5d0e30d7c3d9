package com.fx.green.electricity.shanxi.service.service.unit.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.dto.IdDTO;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeUnitBasicDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeDeviceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeUnitBasicVO;
import com.fx.green.electricity.shanxi.service.entity.unit.GeDevice;
import com.fx.green.electricity.shanxi.service.entity.unit.GeUnitBasic;
import com.fx.green.electricity.shanxi.service.mapper.unit.GeUnitBasicMapper;
import com.fx.green.electricity.shanxi.service.service.unit.GeDeviceService;
import com.fx.green.electricity.shanxi.service.service.unit.GeUnitBasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 机组信息维护
 */
@Service
public class GeUnitBasicServiceImpl extends ServiceImpl<GeUnitBasicMapper, GeUnitBasic> implements GeUnitBasicService {

    @Autowired
    private GeDeviceService geDeviceService;

    /**
     * 新增机组信息
     *
     * @param param
     */
    @Override
    public void insertUnitBase(GeUnitBasicDTO param) {
        LambdaQueryWrapper<GeUnitBasic> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GeUnitBasic::getUnitName, param.getUnitName());
        Integer integer = baseMapper.selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.UnitBasicInertRepeatError.getMessage());
        }
        GeUnitBasic geUnitBasic = new GeUnitBasic();
        BeanUtil.copyProperties(param, geUnitBasic);
        baseMapper.insert(geUnitBasic);
    }

    /**
     * 删除机组信息
     *
     * @param param
     */
    public void deleteUnitBase(IdDTO param) {
        LambdaQueryWrapper<GeDevice> wrapper = new LambdaQueryWrapper<GeDevice>();
        wrapper.eq(GeDevice::getUnitId, param.getId());
        Integer integer = geDeviceService.getBaseMapper().selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.UnitBasicDeleteError.getMessage());
        }
        baseMapper.deleteById(param.getId());
    }

    /**
     * 根据租户id查询对应机组
     *
     * @param param
     * @return
     */
    @Override
    public List<GeUnitBasicVO> unitBaseList(IdDTO param) {
        LambdaQueryWrapper<GeUnitBasic> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GeUnitBasic::getTenantId, param.getId());
        List<GeUnitBasic> geUnitBasics = baseMapper.selectList(wrapper);
        if (ObjectUtil.isEmpty(geUnitBasics)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<GeDevice> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.in(GeDevice::getUnitId, geUnitBasics.stream().map(GeUnitBasic::getId).collect(Collectors.toList()));
        List<GeDevice> geDevices = geDeviceService.getBaseMapper().selectList(deviceWrapper);
        Map<Long, List<GeDeviceVO>> deviceMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(geDevices)) {
            deviceMap = geDevices.stream().map(t -> {
                GeDeviceVO geDeviceVO = new GeDeviceVO();
                BeanUtil.copyProperties(t, geDeviceVO);
                return geDeviceVO;
            }).collect(Collectors.groupingBy(GeDeviceVO::getUnitId));
        }
        List<GeUnitBasicVO> resultList = new ArrayList<>();
        for (GeUnitBasic geUnitBasic : geUnitBasics) {
            GeUnitBasicVO geUnitBasicVO = new GeUnitBasicVO();
            BeanUtil.copyProperties(geUnitBasic, geUnitBasicVO);
            geUnitBasicVO.setDeviceList(deviceMap.getOrDefault(geUnitBasic.getId(), Collections.emptyList()));
            resultList.add(geUnitBasicVO);
        }
        return resultList;
    }
}
