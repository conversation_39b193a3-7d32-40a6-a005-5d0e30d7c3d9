package com.fx.green.electricity.shanxi.service.service.unit;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeTenantDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeTenantVO;
import com.fx.green.electricity.shanxi.service.entity.unit.GeTenant;

import java.util.List;

/**
 * 绿电租户维护
 */
public interface GeTenantService extends IService<GeTenant> {

    /**
     * 新增绿电租户
     *
     * @param param
     */
    void insertTenant(GeTenantDTO param);

    /**
     * 删除绿电租户
     *
     * @param param
     */
    void deleteTenant(IdDTO param);

    /**
     * 绿电租户分页
     *
     * @param param
     * @return
     */
    List<GeTenantVO> tenantList(GeTenantDTO param);

    /**
     * 获取所有租户和机组对应信息
     *
     * @return
     */
    List<GeTenantVO> getTenantUnitDetail();
}
