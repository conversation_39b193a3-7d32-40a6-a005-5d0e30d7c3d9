package com.fx.green.electricity.shanxi.service.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserBindCycleDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserBindCycleVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserBindCycle;

import java.util.List;

/**
 * 绑定周期 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppBindCycleService extends IService<VppUserBindCycle> {

    /**
     * 用户绑定周期
     *
     * @param param 绑定周期DTO
     * @return 成功/失败
     */
    DataResult<Void> addVppBindCycle(VppUserBindCycleDTO param);

    /**
     * 获取绑定周期列表
     *
     * @param param 绑定周期id
     * @return 绑定周期列表
     */
    List<VppUserBindCycleVO> selectList(IdDTO param);

    /**
     * 删除
     *
     * @param param 绑定周期id
     * @return 成功/失败
     */
    DataResult<Void> delete(IdDTO param);

    /**
     * 根据负荷用户id查询绑定周期
     *
     * @param loadUserId 负荷用户id
     * @return 绑定周期
     */
    VppUserBindCycle findCycleByLoadUserId(Long loadUserId);

    /**
     * 获取绑定周期列表
     *
     * @param id 负荷用户id
     * @return 绑定周期列表
     */
    List<VppUserBindCycle> findCycleList(Long id);

    /**
     * 获取绑定周期列表
     * 没有负荷用户数在1000以上的虚拟电厂
     *
     * @param idList 负荷用户ID列表
     * @return 结果
     */
    List<VppUserBindCycle> findCycleList(List<Long> idList);

    /**
     * 获取最近的绑定周期
     *
     * @param id 负荷用户id
     * @return 绑定周期
     */
    VppUserBindCycle findBindStatus(Long id);
}
