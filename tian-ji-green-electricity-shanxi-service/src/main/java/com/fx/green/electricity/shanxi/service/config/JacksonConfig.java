package com.fx.green.electricity.shanxi.service.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.NumberDeserializers;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fx.green.electricity.shanxi.service.config.bigdecimal.BigDecimalDeserializer;
import com.fx.green.electricity.shanxi.service.config.bigdecimal.BigDecimalSerializer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Configuration
public class JacksonConfig {

    /**
     * 完美解决后端返回超长Long数据类型给前端之后精度丢失问题,Bigdecimal的序列化问题
     * JSON Long  转 String
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(ObjectMapper.class)
    public ObjectMapper jacksonObjectMapper(Jackson2ObjectMapperBuilder builder) {
        ObjectMapper objectMapper = builder.createXmlMapper(false).build();
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        simpleModule.addDeserializer(Long.class, new NumberDeserializers.LongDeserializer(Long.class, null));
        simpleModule.addSerializer(BigDecimal.class, new BigDecimalSerializer());
        simpleModule.addDeserializer(Long.class, new NumberDeserializers.LongDeserializer(Long.class, null));
        simpleModule.addDeserializer(BigDecimal.class, new BigDecimalDeserializer());
        objectMapper.registerModule(simpleModule);
        return objectMapper;
    }

}
