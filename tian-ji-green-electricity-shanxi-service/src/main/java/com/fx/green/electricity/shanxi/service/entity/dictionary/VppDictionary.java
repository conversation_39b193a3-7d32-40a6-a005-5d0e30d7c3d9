package com.fx.green.electricity.shanxi.service.entity.dictionary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 **/
@Data
@ApiModel("字典表")
public class VppDictionary implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;
    @ApiModelProperty("父级id")
    private Long parentId;
    @ApiModelProperty("编号")
    private String value;
    @ApiModelProperty("编号名字")
    private String label;
    @ApiModelProperty("类型1.VPP资源类型 2.VPP电厂类型 3.VPP市场类型 4.VPP机组种类 5.VPP供电单位编码 6VPP 行业类型编码  7.VPP 分路类型  8.VPP 设备类型 9.VPP 供电电压 10.VPP 响应级别  11.VPP 投运状态  12.VPP 运行状态  13.VPP 交易品种 ")
    private String type;

}