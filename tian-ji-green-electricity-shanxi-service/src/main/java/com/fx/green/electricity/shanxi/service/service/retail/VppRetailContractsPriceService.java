package com.fx.green.electricity.shanxi.service.service.retail;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsPriceVO;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContractsPrice;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
*
**/
public interface VppRetailContractsPriceService extends IService<VppRetailContractsPrice> {

    List<VppRetailContractsPriceVO> getPriceList(Date signingMonth);

    void addData(Map<String, BigDecimal> priceAllMap, Date signingMonth);

    void addDataList(List<VppRetailContractsPrice> retailContractsPrices);
}
