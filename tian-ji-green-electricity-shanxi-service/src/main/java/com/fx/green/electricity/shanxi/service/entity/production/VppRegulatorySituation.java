package com.fx.green.electricity.shanxi.service.entity.production;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户调控情况表
 */
@Data
@ApiModel("用户调控情况表")
public class VppRegulatorySituation implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("负荷用户id")
    private Long userId;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("日期")
    private Date dateDay;

    @ApiModelProperty("沟通情况：1.沟通中，2.沟通完成")
    private Integer communicationSituation;

    @ApiModelProperty("调节情况：1.确认调节，2.确认不调节")
    private Integer adjustmentSituation;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    private Date updateTime;


}