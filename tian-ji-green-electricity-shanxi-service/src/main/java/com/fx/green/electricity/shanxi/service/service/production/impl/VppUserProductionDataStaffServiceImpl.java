package com.fx.green.electricity.shanxi.service.service.production.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionDataStaff;
import com.fx.green.electricity.shanxi.service.mapper.production.VppUserProductionDataStaffMapper;
import com.fx.green.electricity.shanxi.service.service.production.VppUserProductionDataStaffService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户生产数据-沟通人员表
 **/
@Slf4j
@Service
public class VppUserProductionDataStaffServiceImpl extends ServiceImpl<VppUserProductionDataStaffMapper, VppUserProductionDataStaff> implements VppUserProductionDataStaffService {

    private LambdaQueryWrapper<VppUserProductionDataStaff> buildQueryWrapper(Long id) {
        LambdaQueryWrapper<VppUserProductionDataStaff> lqw = new LambdaQueryWrapper<>();
        lqw.eq(VppUserProductionDataStaff::getUserProductionDataId, id);
        return lqw;
    }

    @Override
    public void removeByDataId(Long id) {
        remove(buildQueryWrapper(id));
    }

    @Override
    public List<VppUserProductionDataStaff> getByDataId(Long id) {
        return this.list(buildQueryWrapper(id));
    }
}
