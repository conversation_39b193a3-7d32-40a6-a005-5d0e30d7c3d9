package com.fx.green.electricity.shanxi.service.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserBindCycleDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserBindCycleVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserBindCycle;
import com.fx.green.electricity.shanxi.service.mapper.user.VppBindCycleMapper;
import com.fx.green.electricity.shanxi.service.service.user.VppBindCycleService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 绑定周期 Service 实现类
 *
 * <AUTHOR>
 **/
@Service
public class VppBindCycleServiceImpl extends ServiceImpl<VppBindCycleMapper, VppUserBindCycle> implements VppBindCycleService {

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataResult<Void> addVppBindCycle(VppUserBindCycleDTO param) {
        //校验绑定周期
//        int monthsDiff = getMonthsDifference(param.getBindCycleStart(), param.getBindCycleEnd());
//        if (monthsDiff < 5) {
//            return DataResult.failed("绑定周期不得小于6个月");
//        }
        //判断当前绑定的时间是否存在
        //获取当前用户的绑定周期
        LambdaQueryWrapper<VppUserBindCycle> vppBindCycleLqw = new LambdaQueryWrapper<>();
        vppBindCycleLqw.eq(VppUserBindCycle::getUserId, param.getUserId());
        List<VppUserBindCycle> list = baseMapper.selectList(vppBindCycleLqw);
        if (ObjectUtil.isNotEmpty(list)) {
            //绑定周期判断
            for (VppUserBindCycle cycle : list) {
                Date bindCycleStart = cycle.getBindCycleStart();
                Date bindCycleEnd = cycle.getBindCycleEnd();
                // 判断日期是否在范围内
                boolean start = DateUtil.isIn(param.getBindCycleStart(), bindCycleStart, bindCycleEnd);
                boolean end = DateUtil.isIn(param.getBindCycleEnd(), bindCycleStart, bindCycleEnd);
                if (start || end) {
                    return DataResult.failed("绑定周期已存在,不得重复绑定");
                }
            }
        }
        //获取用户信息
        VppLoadUserVO userInfo = vppLoadUserService.getUserInfo(param.getUserId());
        //添加
        VppUserBindCycle vppUserBindCycle = new VppUserBindCycle();
        vppUserBindCycle.setBindCycleStart(DateUtil.beginOfMonth(param.getBindCycleStart()));
        vppUserBindCycle.setBindCycleEnd(DateUtil.endOfMonth(param.getBindCycleEnd()));
        vppUserBindCycle.setUserId(param.getUserId());
        vppUserBindCycle.setTenantId(userInfo.getTenantId());
        boolean save = this.save(vppUserBindCycle);
        if (save) {
            return DataResult.success();
        } else {
            return DataResult.failed("操作失败");
        }
    }

    @Override
    public List<VppUserBindCycleVO> selectList(IdDTO param) {
        LambdaQueryWrapper<VppUserBindCycle> vppBindCycleLqw = new LambdaQueryWrapper<>();
        vppBindCycleLqw.eq(VppUserBindCycle::getUserId, param.getId());
        return BeanUtil.copyToList(baseMapper.selectList(vppBindCycleLqw), VppUserBindCycleVO.class);
    }

    @Override
    public DataResult<Void> delete(IdDTO param) {
        int i = baseMapper.deleteById(param.getId());
        if (i > 0) {
            return DataResult.success();
        } else {
            return DataResult.failed("操作失败");
        }

    }

    private static int getMonthsDifference(Date startDate, Date endDate) {
        return DateUtils.toCalendar(endDate).get(Calendar.MONTH) - DateUtils.toCalendar(startDate).get(Calendar.MONTH)
                + (DateUtils.toCalendar(endDate).get(Calendar.YEAR) - DateUtils.toCalendar(startDate).get(Calendar.YEAR)) * 12;
    }

    @Override
    public VppUserBindCycle findCycleByLoadUserId(Long loadUserId) {
        LambdaQueryWrapper<VppUserBindCycle> vppBindCycleLqw = new LambdaQueryWrapper<>();
        vppBindCycleLqw.eq(VppUserBindCycle::getUserId, loadUserId);
        return this.getOne(vppBindCycleLqw);
    }

    @Override
    public List<VppUserBindCycle> findCycleList(Long id) {
        LambdaQueryWrapper<VppUserBindCycle> vppBindCycleLqw = new LambdaQueryWrapper<>();
        vppBindCycleLqw.eq(VppUserBindCycle::getUserId, id);
        vppBindCycleLqw.eq(VppUserBindCycle::getIsDelete, 0);
        return baseMapper.selectList(vppBindCycleLqw);
    }

    @Override
    public List<VppUserBindCycle> findCycleList(List<Long> idList) {
        LambdaQueryWrapper<VppUserBindCycle> vppBindCycleLqw = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(idList)) {
            vppBindCycleLqw.in(ObjectUtil.isNotEmpty(idList), VppUserBindCycle::getUserId, idList);
            vppBindCycleLqw.eq(VppUserBindCycle::getIsDelete, 0);
            return baseMapper.selectList(vppBindCycleLqw);
        } else {
            return new ArrayList<>();
        }

    }

    @Override
    public VppUserBindCycle findBindStatus(Long id) {
        LambdaQueryWrapper<VppUserBindCycle> vppBindCycleLqw = new LambdaQueryWrapper<>();
        vppBindCycleLqw.eq(VppUserBindCycle::getUserId, id);
        vppBindCycleLqw.orderByDesc(VppUserBindCycle::getBindCycleEnd);
        vppBindCycleLqw.last("LIMIT 1");
        return baseMapper.selectOne(vppBindCycleLqw);
    }

}
