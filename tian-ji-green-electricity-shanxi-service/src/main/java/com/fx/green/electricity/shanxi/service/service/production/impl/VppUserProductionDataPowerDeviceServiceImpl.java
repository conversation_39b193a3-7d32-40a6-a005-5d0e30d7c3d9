package com.fx.green.electricity.shanxi.service.service.production.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionDataPowerDevice;
import com.fx.green.electricity.shanxi.service.mapper.production.VppUserProductionDataPowerDeviceMapper;
import com.fx.green.electricity.shanxi.service.service.production.VppUserProductionDataPowerDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户生产数据-发电设备表
 **/
@Slf4j
@Service
public class VppUserProductionDataPowerDeviceServiceImpl extends ServiceImpl<VppUserProductionDataPowerDeviceMapper, VppUserProductionDataPowerDevice> implements VppUserProductionDataPowerDeviceService {

    private LambdaQueryWrapper<VppUserProductionDataPowerDevice> buildQueryWrapper(Long id) {
        LambdaQueryWrapper<VppUserProductionDataPowerDevice> lqw = new LambdaQueryWrapper<>();
        lqw.eq(VppUserProductionDataPowerDevice::getUserProductionDataId, id);
        return lqw;
    }

    @Override
    public void removeByDataId(Long id) {
        remove(buildQueryWrapper(id));
    }

    @Override
    public List<VppUserProductionDataPowerDevice> getByDataId(Long id) {
        return this.list(buildQueryWrapper(id));
    }

    @Override
    public List<VppUserProductionDataPowerDevice> getAdjustByIdList(List<Long> deviceIdList) {
        LambdaQueryWrapper<VppUserProductionDataPowerDevice> lqw = new LambdaQueryWrapper<>();
        lqw.in(VppUserProductionDataPowerDevice::getId, deviceIdList);
        return list(lqw);
    }
}
