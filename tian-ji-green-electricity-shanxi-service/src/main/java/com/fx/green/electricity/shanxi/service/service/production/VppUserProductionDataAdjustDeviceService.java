package com.fx.green.electricity.shanxi.service.service.production;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionDataAdjustDevice;

import java.util.List;

/**
 * 用户生产数据-主要调节设备/主要生产工艺段表
 **/
public interface VppUserProductionDataAdjustDeviceService extends IService<VppUserProductionDataAdjustDevice> {


    void removeByDataId(Long id);

    List<VppUserProductionDataAdjustDevice> getByDataId(Long id);

    List<VppUserProductionDataAdjustDevice> getPowerByIdList(List<Long> deviceIdList);

}
