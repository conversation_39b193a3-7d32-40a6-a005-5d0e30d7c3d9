package com.fx.green.electricity.shanxi.service.service.data;

import java.util.Date;

/**
 * 关口表数据相关
 *
 * <AUTHOR>
 **/
public interface DataGatherService {

    /**
     * 每1分钟计算5分钟以内的电量聚合计算至虚拟电厂表中
     */
    void minuteGroupData();

    /**
     * 每天计算昨天的负荷电量聚合计算至虚拟电厂表中
     */
    void dayGroupData();

    /**
     * 开始时间结束时间进行聚合计算
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    void dateGroupData(Date startTime, Date endTime);

    /**
     * 按日聚合计算，仅计算某个虚拟电厂
     *
     * @param startTime 计算日期
     * @param userCode  用户企业社会信用代码
     */
    void updateGroupData(Date startTime, String userCode);
}
