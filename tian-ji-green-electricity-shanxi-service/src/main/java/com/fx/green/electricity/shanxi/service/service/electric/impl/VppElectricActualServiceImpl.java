package com.fx.green.electricity.shanxi.service.service.electric.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.TimeConstant;
import com.fx.common.enums.BooleanEnum;
import com.fx.common.excel.vo.ImportExcelDetailVO;
import com.fx.common.util.MyDateUtil;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.*;
import com.fx.green.electricity.shanxi.service.constant.PartitionTableConstant;
import com.fx.green.electricity.shanxi.service.constant.VppConstant;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActual;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricQuantity;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.enums.VirtualPowerPlantServiceCodeEnum;
import com.fx.green.electricity.shanxi.service.mapper.electric.VppElectricActualMapper;
import com.fx.green.electricity.shanxi.service.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualConvergeService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualService;
import com.fx.green.electricity.shanxi.service.service.other.VppPartitionTableService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 实际用电量数据 Service 实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppElectricActualServiceImpl extends ServiceImpl<VppElectricActualMapper, VppElectricActual> implements VppElectricActualService {

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Resource
    private VppPartitionTableService vppPartitionTableService;

    @Resource
    private DataMaintenanceService dataMaintenanceService;

    @Resource
    private VppElectricActualConvergeService vppElectricActualConvergeService;

    private static final Integer INSERT_SIZE = 1000;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportExcelDetailVO importVppElectricActualList(List<VppElectricActualDTO> vppElectricActualDTOList, VppElectricQuantity param) {
        ImportExcelDetailVO importExcelDetail = new ImportExcelDetailVO();
        importExcelDetail.setInfoList(new ArrayList<>());

        int success = 0;
        int failed = 0;

        List<VppUser> userList = vppLoadUserService.getListByTenantId(param.getTenantId(), "");
        Map<String, VppUser> userNameMap = userList.stream().collect(Collectors.toMap(VppUser::getName, u -> u));
        List<VppElectricActual> electricActualList = new ArrayList<>();

        List<BigDecimal> electricList = new ArrayList<>();
        List<String> userNameList = new ArrayList<>();
        for (VppElectricActualDTO vppElectricActualDTO : vppElectricActualDTOList) {
            String userName = vppElectricActualDTO.getName();
            if (!userNameMap.containsKey(userName)) {
                continue;
            }
            VppUser vppUser = userNameMap.get(userName);
            vppElectricActualDTO.setUserCode(vppUser.getUserCode());
            vppElectricActualDTO.setUserId(vppUser.getId());

            VppElectricActual vppElectricActual = new VppElectricActual();
            BeanUtils.copyProperties(vppElectricActualDTO, vppElectricActual);
            vppElectricActual.setId(IdWorker.getId());
            vppElectricActual.setIsDelete(0);
            electricActualList.add(vppElectricActual);
            electricList.add(vppElectricActual.getElectricity());
            // 所有导入的名称整理
            if (!userNameList.contains(userName)) {
                userNameList.add(userName);
            }
        }
        success += electricActualList.size();

        if (electricList.isEmpty()) {
            importExcelDetail.getInfoList().add("异常原因：" +
                    VirtualPowerPlantServiceCodeEnum.IMPORT_NO_BIND_USER.getMessage());
            failed += 1;
        }

        if (electricList.size() % VppConstant.NINETY_SIX_NUMBER != 0) {
            importExcelDetail.getInfoList().add("异常原因：" +
                    VirtualPowerPlantServiceCodeEnum.IMPORT_QUANTITY_ERROR.getMessage());
            failed += 1;
        }
        if (failed > 0) {
            // 如果有异常的 手动回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        } else {
            //分表需要tenantId
            vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL, param.getRunningDate());

            // 判断是否已经存在，已经存在进行修改操作，如果没有的话在直接新增
            LambdaQueryWrapper<VppElectricActual> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VppElectricActual::getElectricId, param.getId());
            List<VppElectricActual> oldElectricActualList = this.list(queryWrapper);

            // 获取所有的旧数据
            Map<String, List<VppElectricActual>> oldElectricActualMap = oldElectricActualList.stream()
                    .collect(Collectors.groupingBy(VppElectricActual::getName));

            // 获取所有的新数据
            Map<String, List<VppElectricActual>> electricActualMap = electricActualList.stream()
                    .collect(Collectors.groupingBy(VppElectricActual::getName));

            List<VppElectricActual> insertList = new ArrayList<>();
            List<VppElectricActual> updateList = new ArrayList<>();
            electricActualMap.forEach((name, dataList) -> {
                if (oldElectricActualMap.containsKey(name)) {
                    // 旧数据进行更新
                    List<VppElectricActual> electricActualDataList = oldElectricActualMap.get(name);
                    Map<String, List<VppElectricActual>> electricActualDataMap = electricActualDataList.stream()
                            .collect(Collectors.groupingBy(VppElectricActual::getTimeFrame));

                    // 遍历新数据 将旧数据的ID赋过去
                    for (VppElectricActual vppElectricActual : dataList) {
                        String timeFrame = vppElectricActual.getTimeFrame();
                        // 判断之前是否有上传的
                        List<VppElectricActual> list = electricActualDataMap.get(timeFrame);
                        VppElectricActual oldData = list.get(0);
                        vppElectricActual.setId(oldData.getId());
                    }
                    updateList.addAll(dataList);

                } else {
                    insertList.addAll(dataList);
                }
            });

            // 批量新增数据
            this.insertBatch(insertList);
            // 批量修改数据
            this.updateBatchById(updateList);
        }

        importExcelDetail.setSuccessNum(success);
        importExcelDetail.setFailedNum(failed);

        if (failed > 0) {
            importExcelDetail.setMessage("部分数据内容异常");
            importExcelDetail.setStatus(false);
        } else {
            importExcelDetail.setStatus(true);

            // 相关分区表创建
            vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL_24, param.getRunningDate());
            vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL_CONVERGE, param.getRunningDate());
            // 直接创建连后三个月的 如果日多了27号 将下下个月的表也创建出来
            // 预测数据是要生成这个月以及下个月的表
            int months = DateUtil.dayOfMonth(param.getRunningDate()) > 27 ? 3 : 2;
            for (int i = 0; i < months; i++) {
                vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL_MIDDLE, DateUtil.offsetMonth(param.getRunningDate(), i));
            }

            // 存储过程调用
            baseMapper.importElectricCalculate(param.getTenantId(), param.getRunningDate(), param.getId());
        }

        return importExcelDetail;
    }


    @Override
    public void deleteByElectricId(Long electricId) {
        baseMapper.deleteByElectricId(electricId);
    }


    @Override
    public void insertBatch(List<VppElectricActual> eleList) {
        int size = eleList.size();
        int c = size / INSERT_SIZE;
        int r = size % INSERT_SIZE;
        for (int i = 0; i < c; i++) {
            baseMapper.insertBatch(eleList.subList(i * INSERT_SIZE, (i + 1) * INSERT_SIZE));
        }
        if (r > 0) {
            baseMapper.insertBatch(eleList.subList(c * INSERT_SIZE, size));
        }
    }

    @Override
    public List<VppBiddingClearVO.ResolveLoad> getList(ResultAnalysisDTO param, Date dateDay, String startTime, String endTime) {
        return baseMapper.getList(param, dateDay, startTime, endTime);
    }

    @Override
    public List<VppElectricActualVO> getUserRealityPower(ResultAnalysisDTO param, String startTime, String endTime) {
        String month = DateUtil.format(param.getStartDate(), "yyyy-MM");
        return baseMapper.getUserRealityPower(param, startTime, endTime, month);
    }

    private QueryWrapper<VppElectricActualDTO> querySearch(VppBatteryInformationDTO param) {
        QueryWrapper<VppElectricActualDTO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sea.tenant_id", param.getTenantId());
        queryWrapper.eq("sea.is_delete", BooleanEnum.FALSE.getCode());
        if (param.getUserCodeList() != null && !param.getUserCodeList().isEmpty()) {
            queryWrapper.in("sea.user_code", param.getUserCodeList());
        }
        if (param.getSearchDate() != null && param.getSearchDate().size() == VppConstant.TWO_NUMBER) {
            queryWrapper.ge("sea.date_day", param.getSearchDate().get(0));
            queryWrapper.le("sea.date_day", param.getSearchDate().get(1));
        }
        if (param.getSearchTime() != null && param.getSearchTime().size() == VppConstant.TWO_NUMBER) {
            switch (param.getType()) {
                case 96:
                    queryWrapper.ge("sea.time_frame", param.getSearchTime().get(0));
                    queryWrapper.le("sea.time_frame", param.getSearchTime().get(1));
                    break;
                case 24:
                    queryWrapper.ge("stc.twenty_four", param.getSearchTime().get(0));
                    queryWrapper.le("stc.twenty_four", param.getSearchTime().get(1));
                    break;
                default:
                    queryWrapper.ge("sea.time_frame", param.getSearchTime().get(0));
                    queryWrapper.le("sea.time_frame", param.getSearchTime().get(1));
                    break;
            }
        }
        if (param.getSelectDate() != null && !param.getSelectDate().isEmpty()) {
            queryWrapper.in("sea.date_day", param.getSelectDate());
        }
        if (param.getSelectTime() != null && param.getSelectTime().size() == VppConstant.TWO_NUMBER) {
            queryWrapper.ge("sea.date_day", MyDateUtil.getMinMonthDate(param.getSelectTime().get(0)));
            queryWrapper.le("sea.date_day", MyDateUtil.getMaxMonthDate(param.getSelectTime().get(1)));
        }

        return queryWrapper;
    }


    @Override
    public List<VppElectricActualSimpleVO> electricListByDateTimeUser(VppBatteryInformationDTO param) {
        //获取绑定用户信息
        List<VppUser> listByTenantId = vppLoadUserService.getListByTenantId(param.getTenantId(), "");
        List<VppUser> userList = new ArrayList<>();
        if (CollUtil.isNotEmpty(listByTenantId)) {
            //绑定周期判断
            userList = vppLoadUserService.getUserListByTwoDate(param.getSearchDate().get(0), param.getSearchDate().get(1), listByTenantId);
        }


        QueryWrapper<VppElectricActualDTO> queryWrapper = this.querySearch(param);
        if (ObjectUtil.isNotEmpty(userList)) {
            List<Long> userIdList = userList.stream().map(VppUser::getId).collect(Collectors.toList());
            queryWrapper.in("user_id", userIdList);
        }
        Date minMonthDate = null;
        Date maxMonthDate = null;
        if (param.getSearchDate() != null && param.getSearchDate().size() == 2) {
            minMonthDate = MyDateUtil.getMinMonthDate(param.getSearchDate().get(0));
            maxMonthDate = MyDateUtil.getMaxMonthDate(param.getSearchDate().get(1));
        }
        if (TimeConstant.NINETY_SIX_NUMBER.equals(param.getType())) {
            // 96个点
            queryWrapper.groupBy("sea.user_code, sea.date_day", "sea.time_frame");
            queryWrapper.orderByAsc("sea.user_code, sea.date_day", "sea.time_frame");
            return baseMapper.selectElectricListByDateTimeUser(queryWrapper, param.getTenantId(), param.getTradingUnit(), minMonthDate, maxMonthDate);
        } else {
            // 24个点
            queryWrapper.groupBy("sea.user_code, sea.date_day", "stc.twenty_four");
            queryWrapper.orderByAsc("sea.user_code, sea.date_day", "stc.twenty_four");
            return baseMapper.select24ElectricListByDateTimeUser(queryWrapper, param.getTenantId(), param.getTradingUnit(), minMonthDate, maxMonthDate);
        }
    }

    @Override
    public List<VppElectricActualVO> electricActualList(ResultAnalysisDTO param, String startTime, String endTime) {
        return baseMapper.electricActualList(param, startTime, endTime);
    }

    @Override
    public List<PieChartOfUserVO.RatioVO> getAllInfo(ControllableLoadDTO param, Long userId, List<Long> userIdList) {
        LambdaQueryWrapper<VppElectricActualConverge> electricConvergeLqw = new LambdaQueryWrapper<>();
        electricConvergeLqw.in(VppElectricActualConverge::getUserId, userIdList);
        return baseMapper.getAllInfo(param, userId, electricConvergeLqw);
    }

    @Override
    public BigDecimal getAllPower(ControllableLoadDTO param, List<Long> userIdList) {
        LambdaQueryWrapper<VppElectricActual> electricConvergeLqw = new LambdaQueryWrapper<>();
        electricConvergeLqw.in(VppElectricActual::getUserId, userIdList);
        return baseMapper.getAllPower(param, electricConvergeLqw);
    }

    @Override
    public List<VppElectricActualForUserVO> getElectricActual(Long userId, Date monthStart, Date monthEnd) {
        return baseMapper.getElectricActual(userId, monthStart, monthEnd);
    }

    @Override
    public List<VppElectricActualVO> electricActualAllList(ResultAnalysisDTO param, List<Long> userId, String startTime, String endTime) {
        return baseMapper.electricActualAllList(param, userId, startTime, endTime);
    }

    @Override
    public List<DividendVO.UserElectricSqlVO> electricActualAll(ResultAnalysisDTO resultAnalysisDTO) {
        return baseMapper.electricActualAll(resultAnalysisDTO);
    }

    @Override
    public String getUpdateTime(GetUpdateTimeDTO param) {
        return baseMapper.getUpdateTime(param);
    }

    @Override
    public List<VppBiddingClearVO.ResolveLoad> getElecList(ResultAnalysisDTO param, String startTime, String endTime) {
        List<VppBiddingClearVO.ResolveLoad> list = new ArrayList<>();
        List<VppElectricActualConverge> vppElectricActuals = vppElectricActualConvergeService.getElecList(param, startTime, endTime);
        for (VppElectricActualConverge actual : vppElectricActuals) {
            VppBiddingClearVO.ResolveLoad load = new VppBiddingClearVO.ResolveLoad();
            load.setPeriod(actual.getTimeFrame());
            load.setVal(actual.getElectricity());
            load.setDateDay(DateUtil.formatDate(actual.getDateDay()));
            list.add(load);
        }
        return list;
    }

    @Override
    public List<VppBiddingClearVO.ResolveLoad> getElecAllList(ResultAnalysisDTO param, String startTime,
                                                              String endTime, List<Long> userIdList, List<String> periodDetails) {
        long t = System.currentTimeMillis();

        List<VppBiddingClearVO.ResolveLoad> list = new ArrayList<>();
        LambdaQueryWrapper<VppElectricActual> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ObjectUtil.isNotEmpty(userIdList), VppElectricActual::getUserId, userIdList);
        queryWrapper.ge(VppElectricActual::getDateDay, param.getStartDate());
        queryWrapper.le(VppElectricActual::getDateDay, param.getEndDate());
        queryWrapper.ge(ObjectUtil.isNotNull(startTime), VppElectricActual::getTimeFrame, startTime);
        queryWrapper.le(ObjectUtil.isNotNull(startTime), VppElectricActual::getTimeFrame, endTime);
        queryWrapper.eq(VppElectricActual::getIsDelete, 0);
        if (ObjectUtil.isNull(startTime) && ObjectUtil.isNull(endTime) && ObjectUtil.isNotEmpty(periodDetails) && ObjectUtil.isNotNull(periodDetails)) {
            queryWrapper.in(ObjectUtil.isNotEmpty(periodDetails), VppElectricActual::getTimeFrame, periodDetails);
        }
        List<VppElectricActual> vppElectricActuals = baseMapper.selectList(queryWrapper);
        long l = System.currentTimeMillis() - t;
        System.out.println("获取全部用户的实际用电量" + l);
        for (VppElectricActual actual : vppElectricActuals) {
            VppBiddingClearVO.ResolveLoad load = new VppBiddingClearVO.ResolveLoad();
            load.setPeriod(actual.getTimeFrame());
            load.setVal(actual.getElectricity());
            load.setUserId(actual.getUserId());
            load.setDateDay(DateUtil.formatDate(actual.getDateDay()));
            list.add(load);
        }
        long l1 = System.currentTimeMillis() - t;
        System.out.println("组装数据" + l1);
        return list;
    }

    @Override
    public List<VppBiddingClearVO.ResolveLoad> getListByTenantId(Long tenantId, Date dateDay) {
        return baseMapper.getListByTenantId(tenantId, dateDay);
    }

    @Override
    public void removeByParam(Date dateDay, Long tenantId) {
        baseMapper.removeByParam(dateDay, tenantId);
        baseMapper.remove24ByParam(dateDay, tenantId);
    }

    @Override
    public Map<String, List<BigDecimal>> getActualTwentyFourForUser(Date startDay, Date endDay, Long tenantId, List<String> userCodes) {
        //获取用户信息
        List<VppUser> userINfoByUserCode = vppLoadUserService.getUserINfoByUserCode(userCodes, tenantId);
        Map<Long, String> userMap = userINfoByUserCode.stream().collect(Collectors.toMap(VppUser::getId, VppUser::getUserCode));
        List<Long> userIdList = userINfoByUserCode.stream().map(VppUser::getId).collect(Collectors.toList());
        //获取实际用电量
        List<VppElectricActualVO> actualTwentyFourForUser = baseMapper.getActualTwentyFourForUser(startDay, endDay, tenantId, userIdList);

        Map<String, List<BigDecimal>> resultMap = new HashMap<>();
        Map<Long, List<VppElectricActualVO>> collect = actualTwentyFourForUser.stream().collect(Collectors.groupingBy(VppElectricActualVO::getUserId));
        for (Map.Entry<Long, List<VppElectricActualVO>> entry : collect.entrySet()) {
            Long key = entry.getKey();
            String s = userMap.get(key);
            List<BigDecimal> list = new ArrayList<>();
            List<VppElectricActualVO> vppElectricActualVOS = collect.get(key);
            vppElectricActualVOS = vppElectricActualVOS.stream().sorted(Comparator.comparing(VppElectricActualVO::getTimeFrame)).collect(Collectors.toList());
            for (VppElectricActualVO electricActualVO : vppElectricActualVOS) {
                BigDecimal electricity = electricActualVO.getElectricity();
                list.add(electricity);
            }
            resultMap.put(s, list);
        }
        System.out.println(resultMap);
        return resultMap;
    }


    @Override
    public ImportExcelDetailVO importVppElectricActualListBatch(List<VppElectricActualDTO> allActualDTOs, List<VppElectricQuantityDTO> params, List<DataMaintenanceUpdateDTO> updateDTOs) {
        log.info("开始批量导入实际用电量数据，数据量: {}, 参数量: {}", allActualDTOs.size(), params.size());

        ImportExcelDetailVO result = initializeBatchResult();

        try {
            // 1. 数据预处理和分组
            BatchProcessContext context = prepareBatchData(allActualDTOs, params, updateDTOs);

            // 2. 按租户处理数据
            BatchProcessResult processResult = processByTenant(context);

            // 3. 批量保存数据
            saveBatchData(processResult.getElectricActualList());

            // 4. 异步处理后续操作
            executeAsyncTasks(params, context.getUpdateMaps());

            // 5. 设置结果
            result.setSuccessNum(processResult.getSuccessCount());
            result.setFailedNum(processResult.getFailedCount());
            result.setStatus(processResult.getFailedCount() == 0);

            if (processResult.getFailedCount() > 0) {
                result.setMessage("部分数据处理失败");
                result.setInfoList(processResult.getErrorMessages());
            }

            log.info("批量导入完成，成功: {}, 失败: {}", processResult.getSuccessCount(), processResult.getFailedCount());

        } catch (Exception e) {
            log.error("批量导入异常", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            result.setStatus(false);
            result.setMessage("批量导入异常: " + e.getMessage());
            result.setFailedNum(1);
        }

        return result;
    }

    /**
     * 初始化批量处理结果
     */
    private ImportExcelDetailVO initializeBatchResult() {
        ImportExcelDetailVO result = new ImportExcelDetailVO();
        result.setInfoList(new ArrayList<>());
        result.setSuccessNum(0);
        result.setFailedNum(0);
        return result;
    }

    /**
     * 数据预处理和分组
     */
    private BatchProcessContext prepareBatchData(List<VppElectricActualDTO> allActualDTOs,
                                                 List<VppElectricQuantityDTO> params,
                                                 List<DataMaintenanceUpdateDTO> updateDTOs) {
        // 按日期分组更新DTO
        Map<String, List<DataMaintenanceUpdateDTO>> updateMaps = updateDTOs.stream()
                .collect(Collectors.groupingBy(dto -> DateUtil.formatDate(dto.getDateDay())));

        // 按日期分组实际数据
        Map<String, List<VppElectricActualDTO>> actualDataByDate = allActualDTOs.stream()
                .collect(Collectors.groupingBy(dto -> DateUtil.formatDate(dto.getDateDay())));

        // 按租户分组参数
        Map<Long, List<VppElectricQuantityDTO>> paramsByTenant = params.stream()
                .collect(Collectors.groupingBy(VppElectricQuantityDTO::getTenantId));

        return new BatchProcessContext(updateMaps, actualDataByDate, paramsByTenant);
    }

    /**
     * 按租户处理数据
     */
    private BatchProcessResult processByTenant(BatchProcessContext context) {
        BatchProcessResult result = new BatchProcessResult();

        for (Map.Entry<Long, List<VppElectricQuantityDTO>> tenantEntry : context.getParamsByTenant().entrySet()) {
            Long tenantId = tenantEntry.getKey();
            List<VppElectricQuantityDTO> tenantParams = tenantEntry.getValue();

            log.info("处理租户 {} 的数据，参数数量: {}", tenantId, tenantParams.size());

            // 获取租户用户信息
            TenantUserInfo userInfo = getTenantUserInfo(tenantId);

            // 按日期处理租户数据
            processTenantDataByDate(tenantParams, userInfo, context, result);
        }

        return result;
    }

    /**
     * 获取租户用户信息
     */
    private TenantUserInfo getTenantUserInfo(Long tenantId) {
        List<VppUser> userList = vppLoadUserService.getListByTenantId(tenantId, "");
        Map<String, VppUser> userNameMap = userList.stream()
                .collect(Collectors.toMap(VppUser::getName, u -> u));
        Set<String> validUserNames = userList.stream()
                .map(VppUser::getName)
                .collect(Collectors.toSet());

        return new TenantUserInfo(userNameMap, validUserNames);
    }

    /**
     * 按日期处理租户数据
     */
    private void processTenantDataByDate(List<VppElectricQuantityDTO> tenantParams,
                                         TenantUserInfo userInfo,
                                         BatchProcessContext context,
                                         BatchProcessResult result) {
        // 按日期分组处理
        Map<Date, List<VppElectricQuantityDTO>> paramsByDate = tenantParams.stream()
                .collect(Collectors.groupingBy(VppElectricQuantityDTO::getRunningDate));

        for (Map.Entry<Date, List<VppElectricQuantityDTO>> dateEntry : paramsByDate.entrySet()) {
            Date runningDate = dateEntry.getKey();
            List<VppElectricQuantityDTO> dateParams = dateEntry.getValue();

            // 创建分区表
            createPartitionTable(runningDate);

            // 处理单日数据
            processSingleDateData(runningDate, dateParams, userInfo, context, result);
        }
    }

    /**
     * 处理单日数据
     */
    private void processSingleDateData(Date runningDate,
                                       List<VppElectricQuantityDTO> dateParams,
                                       TenantUserInfo userInfo,
                                       BatchProcessContext context,
                                       BatchProcessResult result) {
        String dateStr = DateUtil.formatDate(runningDate);
        List<VppElectricActualDTO> dayActualData = context.getActualDataByDate().get(dateStr);

        if (dayActualData == null || dayActualData.isEmpty()) {
            result.addError("日期 " + dateStr + " 没有找到对应的实际数据");
            return;
        }

        // 过滤有效用户数据
        List<VppElectricActualDTO> validActualData = dayActualData.stream()
                .filter(dto -> userInfo.getValidUserNames().contains(dto.getName()))
                .collect(Collectors.toList());

        if (validActualData.isEmpty()) {
            result.addError("日期 " + dateStr + " 没有有效的用户数据");
            return;
        }

        // 数据校验
        if (!validateElectricData(validActualData, result)) {
            return;
        }

        // 转换并添加到结果中
        List<VppElectricActual> electricActualList = convertToElectricActual(validActualData, dateParams, userInfo);
        result.addElectricActualList(electricActualList);
        result.addSuccessCount(electricActualList.size());

        log.debug("处理日期 {} 完成，数据量: {}", dateStr, electricActualList.size());
    }

    /**
     * 校验电量数据
     */
    private boolean validateElectricData(List<VppElectricActualDTO> actualData, BatchProcessResult result) {
        if (actualData.isEmpty()) {
            result.addError("没有绑定的用户数据");
            return false;
        }

        if (actualData.size() % VppConstant.NINETY_SIX_NUMBER != 0) {
            result.addError("数据量错误，期望为96的倍数，实际为: " + actualData.size());
            return false;
        }

        return true;
    }

    /**
     * 转换DTO为实体对象
     */
    private List<VppElectricActual> convertToElectricActual(List<VppElectricActualDTO> actualData,
                                                            List<VppElectricQuantityDTO> dateParams,
                                                            TenantUserInfo userInfo) {
        List<VppElectricActual> result = new ArrayList<>();

        for (VppElectricQuantityDTO param : dateParams) {
            for (VppElectricActualDTO dto : actualData) {
                VppUser user = userInfo.getUserNameMap().get(dto.getName());
                if (user != null) {
                    VppElectricActual actual = createElectricActual(dto, param, user);
                    result.add(actual);
                }
            }
        }

        return result;
    }

    /**
     * 创建电量实体对象
     */
    private VppElectricActual createElectricActual(VppElectricActualDTO dto,
                                                   VppElectricQuantityDTO param,
                                                   VppUser user) {
        VppElectricActual actual = new VppElectricActual();
        BeanCopyUtils.copy(dto, actual);
        actual.setId(IdWorker.getId());
        actual.setIsDelete(0);
        actual.setElectricId(param.getId());
        actual.setUserCode(user.getUserCode());
        actual.setUserId(user.getId());
        return actual;
    }

    /**
     * 创建分区表
     */
    private void createPartitionTable(Date runningDate) {
        try {
            vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL, runningDate);
        } catch (Exception e) {
            log.warn("创建分区表失败，日期: " + runningDate, e);
        }
    }

    /**
     * 批量保存数据
     */
    private void saveBatchData(List<VppElectricActual> electricActualList) {
        if (!electricActualList.isEmpty()) {
            log.info("开始批量保存数据，数量: " + electricActualList.size());
            saveBatch(electricActualList);
            log.info("批量保存完成");
        }
    }

    /**
     * 异步执行后续任务
     */
    private void executeAsyncTasks(List<VppElectricQuantityDTO> params,
                                   Map<String, List<DataMaintenanceUpdateDTO>> updateMaps) {
        // 使用合理的线程池配置
        int threadCount = Math.min(params.size(), Runtime.getRuntime().availableProcessors());
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        try {
            List<CompletableFuture<Void>> futures = params.stream()
                    .map(param -> CompletableFuture.runAsync(() ->
                            processAsyncTask(param, updateMaps), executor))
                    .collect(Collectors.toList());

            // 等待所有任务完成，设置超时时间
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(30, TimeUnit.SECONDS);

        } catch (Exception e) {
            log.error("异步任务执行异常", e);
        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 处理异步任务
     */
    private void processAsyncTask(VppElectricQuantityDTO param,
                                  Map<String, List<DataMaintenanceUpdateDTO>> updateMaps) {
        try {
            // 创建相关分区表
            createRelatedPartitionTables(param);

            // 调用存储过程
            baseMapper.importElectricCalculate(param.getTenantId(), param.getRunningDate(), param.getId());

            // 更新数据维护状态
            updateMaintenanceStatus(param, updateMaps);

        } catch (Exception e) {
            log.error("异步任务处理失败，参数: " + param, e);
        }
    }

    /**
     * 创建相关分区表
     */
    private void createRelatedPartitionTables(VppElectricQuantityDTO param) {
        vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL_24, param.getRunningDate());
        vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL_CONVERGE, param.getRunningDate());

        int months = DateUtil.dayOfMonth(param.getRunningDate()) > 27 ? 3 : 2;
        for (int i = 0; i < months; i++) {
            vppPartitionTableService.createPartitionTable(
                    PartitionTableConstant.VPP_ELECTRIC_ACTUAL_MIDDLE,
                    DateUtil.offsetMonth(param.getRunningDate(), i));
        }
    }

    /**
     * 更新数据维护状态
     */
    private void updateMaintenanceStatus(VppElectricQuantityDTO param,
                                         Map<String, List<DataMaintenanceUpdateDTO>> updateMaps) {
        String dateStr = DateUtil.formatDate(param.getRunningDate());
        List<DataMaintenanceUpdateDTO> updateDTOs = updateMaps.get(dateStr);

        if (ObjectUtil.isNotEmpty(updateDTOs)) {
            dataMaintenanceService.updateStatus(updateDTOs.get(0));
        }
    }

    // ==================== 内部辅助类 ====================

    /**
     * 批量处理上下文
     */
    @Data
    @AllArgsConstructor
    private static class BatchProcessContext {
        private final Map<String, List<DataMaintenanceUpdateDTO>> updateMaps;
        private final Map<String, List<VppElectricActualDTO>> actualDataByDate;
        private final Map<Long, List<VppElectricQuantityDTO>> paramsByTenant;
    }

    /**
     * 租户用户信息
     */
    @Data
    @AllArgsConstructor
    private static class TenantUserInfo {
        private final Map<String, VppUser> userNameMap;
        private final Set<String> validUserNames;
    }

    /**
     * 批量处理结果
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class BatchProcessResult {
        private final List<VppElectricActual> electricActualList = new ArrayList<>();
        private final List<String> errorMessages = new ArrayList<>();
        private int successCount = 0;
        private int failedCount = 0;

        public void addElectricActualList(List<VppElectricActual> list) {
            electricActualList.addAll(list);
        }

        public void addError(String message) {
            errorMessages.add(message);
            failedCount++;
        }

        public void addSuccessCount(int count) {
            successCount += count;
        }
    }

    @Override
    public List<VppElectricActual> getElectricByDateList(Date startTime, Date endTime, Long tenantId, String consNo) {
        LambdaQueryWrapper<VppElectricActual> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(consNo), VppElectricActual::getRegistered, consNo);
        queryWrapper.ge(VppElectricActual::getDateDay, startTime);
        queryWrapper.le(VppElectricActual::getDateDay, endTime);
        queryWrapper.le(VppElectricActual::getTenantId, tenantId);
        return baseMapper.selectList(queryWrapper);
    }

}
