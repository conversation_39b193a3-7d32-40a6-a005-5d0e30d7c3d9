package com.fx.green.electricity.shanxi.service.service.unit;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeDeviceSaveDTO;
import com.fx.green.electricity.shanxi.service.entity.unit.GeDevice;

/**
 * 设备信息
 */
public interface GeDeviceService extends IService<GeDevice> {

    /**
     * 新增设备信息
     *
     * @param param
     */
    void insertDevice(GeDeviceSaveDTO param);

    /**
     * 删除设备
     *
     * @param param
     */
    void deleteDevice(IdDTO param);
}
