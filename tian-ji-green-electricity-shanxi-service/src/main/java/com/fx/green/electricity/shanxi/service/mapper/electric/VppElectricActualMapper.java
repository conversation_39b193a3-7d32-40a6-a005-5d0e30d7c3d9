package com.fx.green.electricity.shanxi.service.mapper.electric;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.ControllableLoadDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.GetUpdateTimeDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.ResultAnalysisDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricActualDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.*;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActual;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Component
public interface VppElectricActualMapper extends BaseMapper<VppElectricActual> {

    /**
     * 批量插入实际用电量
     *
     * @param subList 实际电量数据
     */
    void insertBatch(@Param("eleList") List<VppElectricActual> subList);

    /**
     * 删除旧数据
     *
     * @param electricId 电量
     */
    void deleteByElectricId(@Param("electricId") Long electricId);

    /**
     * 导入实际用电量时数据分开保存
     *
     * @param tenantId   租户ID
     * @param dateDay    日期
     * @param electricId 导入数据ID
     */
    void importElectricCalculate(@Param("tenantId") Long tenantId, @Param("dateDay") Date dateDay,
                                 @Param("electricId") Long electricId);

    /**
     * 实际用电量
     *
     * @param param
     * @param startTime
     * @param endTime
     * @param dateDay
     * @return
     */
    List<VppBiddingClearVO.ResolveLoad> getList(@Param("param") ResultAnalysisDTO param,
                                                @Param("dateDay") Date dateDay,
                                                @Param("startTime") String startTime,
                                                @Param("endTime") String endTime);

    /**
     * 获取用户某一段时间内的电量总和
     *
     * @param param
     * @param startTime
     * @param endTime
     * @param month
     * @return
     */
    List<VppElectricActualVO> getUserRealityPower(@Param("param") ResultAnalysisDTO param,
                                                  @Param("startTime") String startTime,
                                                  @Param("endTime") String endTime,
                                                  @Param("month") String month);

    /**
     * 根据条件获取电量数据 通过时点统计
     *
     * @param wrapper      查询条件
     * @param tenantId
     * @param tradingUnit
     * @param minMonthDate
     * @param maxMonthDate
     * @return 每日聚合数据
     */
    List<VppElectricActualSimpleVO> selectElectricListByDateTimeUser(@Param(Constants.WRAPPER) Wrapper<VppElectricActualDTO> wrapper, @Param("tenantId") Long tenantId, @Param("tradingUnit") Integer tradingUnit, @Param("minMonthDate") Date minMonthDate, @Param("maxMonthDate") Date maxMonthDate);

    /**
     * 根据条件获取24点电量数据 通过时点统计
     *
     * @param wrapper      查询条件
     * @param tenantId
     * @param tradingUnit
     * @param minMonthDate
     * @param maxMonthDate
     * @return 每日聚合数据
     */
    List<VppElectricActualSimpleVO> select24ElectricListByDateTimeUser(@Param(Constants.WRAPPER) Wrapper<VppElectricActualDTO> wrapper, @Param("tenantId") Long tenantId, @Param("tradingUnit") Integer tradingUnit, @Param("minMonthDate") Date minMonthDate, @Param("maxMonthDate") Date maxMonthDate);


    /**
     * 获取时间段内的全部数据
     *
     * @param param
     * @param startTime
     * @param endTime
     * @return
     */
    List<VppElectricActualVO> electricActualList(@Param("param") ResultAnalysisDTO param,
                                                 @Param("startTime") String startTime,
                                                 @Param("endTime") String endTime);

    /**
     * 获取时间段内的全部数据
     *
     * @param param
     * @param userId
     * @return
     */
    List<PieChartOfUserVO.RatioVO> getAllInfo(@Param("param") ControllableLoadDTO param,
                                              @Param("userId") Long userId,
                                              @Param(Constants.WRAPPER) LambdaQueryWrapper<VppElectricActualConverge> electricConvergeLqw);


    /**
     * 获取时段的电量总和
     *
     * @param param
     * @return
     */
    BigDecimal getAllPower(@Param("param") ControllableLoadDTO param,
                           @Param(Constants.WRAPPER) LambdaQueryWrapper<VppElectricActual> electricConvergeLqw);

    /**
     * 获取某个月用户的实际用电量（平均）
     *
     * @param userId
     * @param monthStart
     * @param monthEnd
     * @return
     */
    List<VppElectricActualForUserVO> getElectricActual(@Param("userId") Long userId,
                                                       @Param("monthStart") Date monthStart,
                                                       @Param("monthEnd") Date monthEnd);

    /**
     * 获取全部用户的全部用电量列表
     *
     * @param param
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    List<VppElectricActualVO> electricActualAllList(@Param("param") ResultAnalysisDTO param,
                                                    @Param("userId") List<Long> userId,
                                                    @Param("startTime") String startTime,
                                                    @Param("endTime") String endTime);

    List<DividendVO.UserElectricSqlVO> electricActualAll(@Param("param") ResultAnalysisDTO param);

    /**
     * 获取实际用电量更新时间
     *
     * @return
     */
    String getUpdateTime(@Param("param") GetUpdateTimeDTO param);

    List<VppBiddingClearVO.ResolveLoad> getListByTenantId(@Param("tenantId") Long tenantId, @Param("dateDay") Date dateDay);

    void removeByParam(@Param("dateDay") Date dateDay, @Param("tenantId") Long tenantId);


    void remove24ByParam(@Param("dateDay") Date dateDay, @Param("tenantId") Long tenantId);

    List<VppElectricActualVO> getActualTwentyFourForUser(@Param("startDay") Date startDay,
                                    @Param("endDay") Date endDay,
                                    @Param("tenantId") Long tenantId,
                                    @Param("userId") List<Long> userIdList);

    void removeByIdList(@Param("idList") List<Long> updateList);
}
