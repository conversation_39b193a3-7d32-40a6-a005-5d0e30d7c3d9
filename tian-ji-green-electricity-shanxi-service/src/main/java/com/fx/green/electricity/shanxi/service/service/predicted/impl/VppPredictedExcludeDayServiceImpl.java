package com.fx.green.electricity.shanxi.service.service.predicted.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryDateOneDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.VppPredictedExcludeDayDTO;
import com.fx.green.electricity.shanxi.service.entity.predicted.VppPredictedExcludeDay;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.mapper.predicted.VppPredictedExcludeDayMapper;
import com.fx.green.electricity.shanxi.service.service.predicted.VppPredictedExcludeDayService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 **/
@Service
public class VppPredictedExcludeDayServiceImpl extends ServiceImpl<VppPredictedExcludeDayMapper, VppPredictedExcludeDay> implements VppPredictedExcludeDayService {

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataResult<Void> add(VppPredictedExcludeDayDTO param) {
        //用户有问题的日期数据
        List<VppPredictedExcludeDayDTO.DataInfo> list = param.getList();
        List<VppPredictedExcludeDay> saveList = new ArrayList<>();
        for (VppPredictedExcludeDayDTO.DataInfo dataInfo : list) {
            Long tenantId = dataInfo.getTenantId();
            String userCode = dataInfo.getUserCode();
            VppUser vppUser = vppLoadUserService.queryByUserCode(userCode, tenantId);
            VppPredictedExcludeDay vppPredictedExcludeDay = new VppPredictedExcludeDay();
            vppPredictedExcludeDay.setDateDay(dataInfo.getDateDay());
            vppPredictedExcludeDay.setCreateTime(new Date());
            vppPredictedExcludeDay.setUpdateTime(new Date());
            vppPredictedExcludeDay.setUserCode(userCode);
            vppPredictedExcludeDay.setTenantId(tenantId);
            vppPredictedExcludeDay.setUserName(vppUser.getName());
            vppPredictedExcludeDay.setUserId(vppUser.getId());
            saveList.add(vppPredictedExcludeDay);
            //删除某个用户存在的日期
            LambdaQueryWrapper<VppPredictedExcludeDay> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VppPredictedExcludeDay::getUserCode, userCode);
            queryWrapper.eq(VppPredictedExcludeDay::getDateDay, dataInfo.getDateDay());
            queryWrapper.eq(VppPredictedExcludeDay::getTenantId, tenantId);
            baseMapper.delete(queryWrapper);
        }
        saveBatch(saveList);
        return DataResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveListByDay(Date day, List<VppPredictedExcludeDay> vppPredictedExcludeDayList) {
        // 1. 删除该日所有数据
        LambdaQueryWrapper<VppPredictedExcludeDay> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppPredictedExcludeDay::getDateDay, day);
        baseMapper.delete(queryWrapper);

        // 2. 批量新增数据
        if (ObjectUtil.isNotEmpty(vppPredictedExcludeDayList)) {
            return this.saveBatch(vppPredictedExcludeDayList);
        } else {
            return true;
        }
    }

    @Override
    public List<VppPredictedExcludeDay> getDataList(Long tenantId) {
        LambdaQueryWrapper<VppPredictedExcludeDay> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppPredictedExcludeDay::getTenantId, tenantId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<VppPredictedExcludeDay> getDataListByUser(QueryDateOneDTO param) {
        LambdaQueryWrapper<VppPredictedExcludeDay> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppPredictedExcludeDay::getTenantId, param.getTenantId());
        queryWrapper.eq(VppPredictedExcludeDay::getUserId, param.getUserId());
        return baseMapper.selectList(queryWrapper);
    }
}
