package com.fx.green.electricity.shanxi.service.entity.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 户号表
 *
 * <AUTHOR>
**/
@Data
@ApiModel("户号表")
public class VppUserAccount implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("户号id")
    private Long id;

    @ApiModelProperty("负荷用户id")
    private Long userId;

    @ApiModelProperty("用户户号")
    private String consNo;

    @ApiModelProperty("户号名称")
    private String consName;

    @ApiModelProperty("额定功率")
    private BigDecimal ratedPower;

    @ApiModelProperty("电压等级(详见VoltageLevelEnum枚举: 1.220Kv 2.110Kv 3.35Kv 4.10Kv 5.6Kv 6.380V 7.220V)")
    private Integer volLevel;

    @ApiModelProperty("聚合容量")
    private BigDecimal ratedCapacity;

    @ApiModelProperty("地区编号")
    private String areaCode;

    @ApiModelProperty("所在区县")
    private String districtAddr;

    @ApiModelProperty("地区详细信息")
    private String areaCodeDetail;

    @ApiModelProperty("详细地址(地理位置)")
    private String deptAddress;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String lat;

    @ApiModelProperty("并网馈线")
    private String conFeeder;

    @ApiModelProperty("并网变电站")
    private String conTransSub;

    @ApiModelProperty("供电单位编码")
    private String powerSupplyOrgNo;

    @ApiModelProperty("供电电压编码")
    private String voltType;

    @ApiModelProperty("行业类型编码")
    private String tradeType;

    @ApiModelProperty("资源类型编码")
    private String resourceType;

    @ApiModelProperty("响应时间级别")
    private String responseLevel;

    @ApiModelProperty("用电地址")
    private String elecAddr;

    @ApiModelProperty("是否删除(0-否，1-是)")
    private Integer isDelete;

    @ApiModelProperty("创建用户")
    private Long createUser;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改用户")
    private Long updateUser;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("租户id")
    private Long tenantId;

}