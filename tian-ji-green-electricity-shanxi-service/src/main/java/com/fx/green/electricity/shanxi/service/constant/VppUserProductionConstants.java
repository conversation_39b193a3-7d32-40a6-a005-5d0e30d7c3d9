package com.fx.green.electricity.shanxi.service.constant;

/**
 * 生产情况/生产数据的常量
 **/
public class VppUserProductionConstants {
    /**
     * 生产计划
     */
    public static final int PLAN_TYPE_PRODUCTION = 1;

    /**
     * 调节设备运行计划
     */
    public static final int PLAN_TYPE_ADJUST = 2;

    /**
     * 发电设备运行计划
     */
    public static final int PLAN_TYPE_POWER = 3;

    /**
     * 预计生产计划
     */
    public static final int EXPECTED_PLAN = 1;


    /**
     * 实际生产情况
     */
    public static final int ACTUAL_PLAN = 2;


    /**
     * 已填写
     */
    public static final int NO_WRITE = 1;

    /**
     * 未填写
     */
    public static final int IS_WRITE = 2;

    /**
     * 设备类型-调节设备
     */
    public static final String DEVICE_TYPE_ADJUST = "adjust";


    /**
     * 设备类型-发电设备
     */
    public static final String DEVICE_TYPE_POWER = "power";

    /**
     * 虚拟电厂提前计算虚拟电厂和用户的电量
     */
    public static final String DATA_GATHER_VPP_CALCULATION_INFORM = "data-gather-vpp-calculation-inform";

}
