package com.fx.green.electricity.shanxi.service.config.kafka;

import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class KafkaClient {

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMsg(String topic, String value) {
        kafkaTemplate.send(topic, value);
    }

    @Async
    public void sendMsgAsync(String topic, String value) {
        kafkaTemplate.send(topic, value);
    }

}
