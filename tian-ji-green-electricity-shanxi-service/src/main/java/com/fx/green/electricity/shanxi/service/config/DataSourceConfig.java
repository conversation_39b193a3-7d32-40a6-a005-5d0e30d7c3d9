package com.fx.green.electricity.shanxi.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "spring.datasource")
public class DataSourceConfig {

    private String dbType;

    /**
     * opengauss返回true，其他数据源返回false
     * @return
     */
    public Boolean isOG() {
        if (dbType == null) {
            return false;
        }
        return dbType.equals("opengauss");
    }
}
