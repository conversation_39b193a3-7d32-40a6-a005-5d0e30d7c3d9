package com.fx.green.electricity.shanxi.service.mapper.electric;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualMiddle;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * <AUTHOR>
 **/
@Repository
public interface VppElectricActualMiddleMapper extends BaseMapper<VppElectricActualMiddle> {

    void removeByParam(@Param("dateDay") Date dateDay, @Param("tenantId") Long tenantId);
}
