package com.fx.green.electricity.shanxi.service.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.ExcessMLDeclareDateNumVO;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class MonthDaysUtil {


    public static ExcessMLDeclareDateNumVO getDecadeDays(Date dateDay) {
        ExcessMLDeclareDateNumVO num = new ExcessMLDeclareDateNumVO();

        String day = DateUtil.formatDate(dateDay);
        int year = Integer.parseInt(day.split("-")[0]);
        int month = Integer.parseInt(day.split("-")[1]);

        int daysInMonth = getDaysInMonth(year, month);
        int firstDecadeEndDay = 10;
        int secondDecadeEndDay = 20;

        int firstDecadeDays = firstDecadeEndDay;
        if (daysInMonth < firstDecadeEndDay) {
            firstDecadeDays = daysInMonth;
        }

        int secondDecadeDays = secondDecadeEndDay - firstDecadeEndDay;
        if (daysInMonth < secondDecadeEndDay) {
            secondDecadeDays = daysInMonth - firstDecadeEndDay;
            if (secondDecadeDays < 0) {
                secondDecadeDays = 0;
            }
        }

        int thirdDecadeDays = daysInMonth - secondDecadeEndDay;
        if (thirdDecadeDays < 0) {
            thirdDecadeDays = 0;
        }

        List<String> firstDecadeRange = new ArrayList<>();
        for (int i = 1; i <= firstDecadeDays; i++) {
            firstDecadeRange.add(formatDate(year, month, i));
        }

        List<String> secondDecadeRange = new ArrayList<>();
        for (int i = firstDecadeEndDay + 1; i <= firstDecadeEndDay + secondDecadeDays; i++) {
            secondDecadeRange.add(formatDate(year, month, i));
        }

        List<String> thirdDecadeRange = new ArrayList<>();
        for (int i = secondDecadeEndDay + 1; i <= daysInMonth; i++) {
            thirdDecadeRange.add(formatDate(year, month, i));
        }


        num.setTotalDays(daysInMonth);
        num.setFirstDecadeDays(firstDecadeDays);
        num.setSecondDecadeDays(secondDecadeDays);
        num.setThirdDecadeDays(thirdDecadeDays);
        num.setFirstDecadeRange(firstDecadeRange);
        num.setSecondDecadeRange(secondDecadeRange);
        num.setThirdDecadeRange(thirdDecadeRange);
        return num;
    }

    private static int getDaysInMonth(int year, int month) {
        switch (month) {
            case 2:
                return isLeapYear(year) ? 29 : 28;
            case 4:
            case 6:
            case 9:
            case 11:
                return 30;
            default:
                return 31;
        }
    }

    private static boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || year % 400 == 0;
    }


    private static String formatDate(int year, int month, int day) {
        return year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day);
    }

    /**
     * 通过开始时间 结束时间将时间按月进行分割
     * 结束时间的点不会被计算在内
     * 如果传入的是 2024-11-01 00:00:00 - 2024-12-01 00:00:00 只会返回2024-11
     * 会自动将endTime 00:00:00的数据过滤掉
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 结果Map 月份 -> [开始时间, 结束时间]
     */
    public static LinkedHashMap<Date, List<Date>> divideTimeByMonth(Date startTime, Date endTime) {
        if (startTime.after(endTime)) {
            log.error("传入的开始时间{} - 结束时间{} 有误", startTime, endTime);
            return new LinkedHashMap<>();
        }

        LinkedHashMap<Date, List<Date>> result = new LinkedHashMap<>();
        // 如果开始时间和结束时间相同
        if (startTime.equals(endTime)) {
            // 直接返回该日的月份及天
            DateTime dateTime = DateUtil.beginOfMonth(startTime);
            result.put(dateTime, new ArrayList<>(Arrays.asList(startTime, endTime)));
        } else {
            LinkedHashMap<Date, List<Date>> tempResult = new LinkedHashMap<>();
            while (!startTime.after(endTime) && (!startTime.equals(endTime))) {
                // 临时保存下个月第一天
                DateTime tempTime = DateUtil.beginOfMonth(DateUtil.offsetMonth(startTime, 1));
                // 如果当前的结束日期超过了临时的下个月第一天，使用下个月第一天
                Date finalEndDate = tempTime.after(endTime) ? endTime : tempTime;
                // 组装数据
                List<Date> dateList = new ArrayList<>(Arrays.asList(startTime, finalEndDate));
                tempResult.put(DateUtil.beginOfMonth(startTime), dateList);
                // 移动到下一个月
                startTime = tempTime;
            }
            // 将时间从新到旧排序
            ArrayList<Map.Entry<Date, List<Date>>> entryList = new ArrayList<>(tempResult.entrySet());
            CollUtil.reverse(entryList);

            for (Map.Entry<Date, List<Date>> entry : entryList) {
                result.put(entry.getKey(), entry.getValue());
            }
        }
        return result;
    }

    public static void main(String[] args) {
        DateTime startTime = DateUtil.parse("2024-11-05 23:59:00", "yyyy-MM-dd HH:mm:ss");
        DateTime endTime = DateUtil.parse("2025-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Map<Date, List<Date>> dateListMap = MonthDaysUtil.divideTimeByMonth(startTime, endTime);
        System.out.println(dateListMap);

        System.out.println(DateUtil.date());
        DateTime date = DateUtil.date();
        DateTime offset = date.offset(DateField.SECOND, -date.second());
        System.out.println(offset);
    }
}