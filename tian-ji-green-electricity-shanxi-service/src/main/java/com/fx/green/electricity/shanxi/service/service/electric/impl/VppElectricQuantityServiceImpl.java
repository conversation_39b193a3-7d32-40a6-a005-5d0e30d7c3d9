package com.fx.green.electricity.shanxi.service.service.electric.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.common.enums.BooleanEnum;
import com.fx.common.excel.vo.ImportExcelDetailVO;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.common.exception.FxServiceException;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.constant.VppConstant;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricActualDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricDeclareDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.WholesaleAnalysisDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.ImportExcelDetailPageListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricQuantityVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.file.VppFileUploadVO;
import com.fx.green.electricity.shanxi.api.utils.FileTypeUtil;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricQuantity;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.mapper.electric.VppElectricQuantityMapper;
import com.fx.green.electricity.shanxi.service.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.service.service.declare.SeElectricDeclareService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualConvergeService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricQuantityService;
import com.fx.green.electricity.shanxi.service.service.file.VppFileService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.fx.green.electricity.shanxi.service.enums.VirtualPowerPlantServiceCodeEnum.QUERY_PARAM_ERROR;


/**
 * 实际用电量记录 Service 实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppElectricQuantityServiceImpl extends ServiceImpl<VppElectricQuantityMapper, VppElectricQuantity> implements VppElectricQuantityService {

    @Resource
    private VppElectricActualService vppElectricActualService;

    @Resource
    private VppElectricActualConvergeService vppElectricActualConvergeService;

    @Resource
    private SeElectricDeclareService seElectricDeclareService;

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Resource
    private DataMaintenanceService dataMaintenanceService;

    @Resource
    private VppFileService vppFileService;

    @Override
    public ImportExcelVO importActualElectricityConsumption(MultipartFile file) {
        log.info("开始导入实际用电量数据，文件名: {}", file.getOriginalFilename());

        // 1. 文件校验
        if (!validateFile(file)) {
            return createErrorResult(file.getOriginalFilename(), "文件校验失败");
        }

        // 2. 上传文件并创建上下文
        ImportContext context = uploadFile(file);
        if (context == null) {
            return createErrorResult(file.getOriginalFilename(), "文件上传失败");
        }

        // 3. 读取Excel数据
        if (!readExcelData(context, file)) {
            return createErrorResult(file.getOriginalFilename(), "读取Excel数据失败");
        }

        // 4. 解析和校验数据
        if (!parseAndValidateData(context, file.getOriginalFilename())) {
            return createErrorResult(file.getOriginalFilename(), "数据解析或校验失败");
        }

        // 5. 数据聚合处理
        aggregateData(context, file.getOriginalFilename());

        // 6. 执行导入
        ImportExcelDetailVO dataResult = importElectricNew(context.getResultList(), 1);
        log.info("导入完成，结果: 成功{}条，失败{}条", dataResult.getSuccessNum(), dataResult.getFailedNum());

        // 7. 返回成功结果
        ImportExcelVO result = new ImportExcelVO();
        result.setDetailList(new ArrayList<>());
        result.setSuccessNum(dataResult.getSuccessNum());
        result.setFailedNum(dataResult.getFailedNum());
        return result;
    }

    /**
     * 批量导入实际数据
     */
    private ImportExcelDetailVO importElectricNew(List<VppElectricQuantityDTO> params, Integer type) {
        ImportExcelDetailVO result = new ImportExcelDetailVO();
        result.setInfoList(new ArrayList<>());
        result.setSuccessNum(0);
        result.setFailedNum(0);

        try {
            // 批量处理所有日期的数据
            List<VppElectricActualDTO> allActualDTOs = new ArrayList<>();
            List<DataMaintenanceUpdateDTO> updateDTOs = new ArrayList<>();

            for (VppElectricQuantityDTO param : params) {
                // 处理单日逻辑（原逻辑提取为方法）
                processSingleDay(param, type, allActualDTOs, updateDTOs);
            }
            // 批量导入实际数据
            ImportExcelDetailVO actualResult = vppElectricActualService.importVppElectricActualListBatch(allActualDTOs, params, updateDTOs);

            result.setSuccessNum(actualResult.getSuccessNum());
            result.setFailedNum(actualResult.getFailedNum());


            result.setStatus(true);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            result.setMessage("批量导入异常: " + e.getMessage());
            result.setStatus(false);
        }
        return result;
    }


    /**
     * 处理单日逻辑
     */
    private void processSingleDay(VppElectricQuantityDTO param, Integer type,
                                  List<VppElectricActualDTO> allActualDTOs,
                                  List<DataMaintenanceUpdateDTO> updateDTOs) {
        VppElectricQuantityVO byTimeAndType = findByTimeAndType(param);
        VppElectricQuantity vppElectricQuantity = new VppElectricQuantity();
        BeanUtil.copyProperties(param, vppElectricQuantity);

        if (ObjectUtil.isNotNull(byTimeAndType)) {
            vppElectricQuantity.setId(byTimeAndType.getId());
            baseMapper.updateById(vppElectricQuantity);
        } else {
            baseMapper.insert(vppElectricQuantity);
        }
        param.setId(vppElectricQuantity.getId());
        param.getElectricActualList().forEach(ea -> {
            ea.setElectricId(vppElectricQuantity.getId());
            ea.setTimeFrame(ea.getTimeFrame().substring(0, 5));
            ea.setTenantId(param.getTenantId());
        });
        allActualDTOs.addAll(param.getElectricActualList());

        // 准备批量更新状态
        DataMaintenanceUpdateDTO updateDTO = buildDataMaintenanceUpdate(param, type);
        updateDTOs.add(updateDTO);
        //删除这一天的实际用电量
        DataMaintenanceQueryDTO dto = new DataMaintenanceQueryDTO();
        dto.setTenantId(param.getTenantId());
        dto.setDateDay(param.getRunningDate());
        dataMaintenanceService.deleteActualElectricityConsumption(dto);
    }

    /**
     * 构建数据维护更新DTO
     */
    private DataMaintenanceUpdateDTO buildDataMaintenanceUpdate(VppElectricQuantityDTO param, Integer type) {
        DataMaintenanceUpdateDTO updateDTO = new DataMaintenanceUpdateDTO();
        updateDTO.setDateDay(param.getRunningDate());
        updateDTO.setTenantId(param.getTenantId());

        // 获取系统中该日期该租户下的所有用户
        List<VppUser> userList = vppLoadUserService.getListByTenantId(param.getTenantId(), "");
        //绑定周期判断
        List<VppUser> listByTenantId = vppLoadUserService.getUserList(param.getRunningDate(), userList);
        List<String> systemUserNames = listByTenantId.stream()
                .map(VppUser::getName)
                .distinct()
                .collect(Collectors.toList());

        // 获取导入数据中的用户列表
        List<String> importUserNames = param.getElectricActualList().stream()
                .map(VppElectricActualDTO::getName)
                .distinct()
                .collect(Collectors.toList());

        // 判断状态值
        if (importUserNames.isEmpty()) {
            updateDTO.setRealElctricityStatus(2);  // 无数据
        } else if (new HashSet<>(importUserNames).containsAll(systemUserNames)) {
            updateDTO.setRealElctricityStatus(1);  // 数据完整
        } else {
            updateDTO.setRealElctricityStatus(3);  // 数据部分缺失
        }

        updateDTO.setRealElectricitySources(type);
        return updateDTO;
    }

    @Override
    public VppElectricQuantityVO findByTimeAndType(VppElectricQuantityDTO param) {
        QueryWrapper<VppElectricQuantityVO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(param.getTenantId()), "veq.tenant_id", param.getTenantId());
        queryWrapper.eq("veq.running_date", param.getRunningDate());
        queryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
        queryWrapper.eq("veq.type", param.getType());
        return baseMapper.findByWrapper(queryWrapper);
    }

    @Override
    public void delete(IdDTO param) {
        baseMapper.deleteById(param.getId());
    }

    @Override
    public FxPage<ImportExcelDetailPageListVO> queryImportRecord(VppElectricQuantityDTO.QueryDTO param) {
        if (ObjectUtil.isNull(param.getStartTime()) || ObjectUtil.isNull(param.getEndTime())) {
            throw new FxServiceException(QUERY_PARAM_ERROR);
        }

        //获取用户信息
        List<VppUser> listByTenantId = vppLoadUserService.getListByTenantId(param.getTenantId(), "");
        List<VppUser> listByTenantIds = vppLoadUserService.getUserListByTwoDate(param.getStartTime(), param.getEndTime(), listByTenantId);
        List<Long> userIdList = listByTenantIds.stream().map(VppUser::getId).collect(Collectors.toList());
        Map<Long, String> userMaps = listByTenantId.stream().collect(Collectors.toMap(VppUser::getId, VppUser::getName));
        //获取实际用电量信息
        List<VppElectricActualConverge> vppElectricActualConverges = vppElectricActualConvergeService.queryElectricByUserIdLists(userIdList, param.getStartTime(), param.getEndTime(), param.getTenantId());
        Map<String, List<VppElectricActualConverge>> elecMap = vppElectricActualConverges.stream().collect(Collectors.groupingBy(o -> DateUtil.formatDate(o.getDateDay())));


        List<ImportExcelDetailPageListVO> list = new ArrayList<>();
        try {
            if (ObjectUtil.isNull(param.getStatus())) {
                //获取全部数据
                //获取两个日期之间的全部日期
                List<Date> everyDay = findEveryDay(param.getStartTime(), param.getEndTime());
                for (int i = 0; i < everyDay.size(); i++) {
                    ImportExcelDetailPageListVO importExcelDetailPageListVO = new ImportExcelDetailPageListVO();
                    Date date = everyDay.get(i);

                    List<VppElectricActualConverge> list1 = elecMap.get(DateUtil.formatDate(date));
                    StringBuilder notes = new StringBuilder();
                    if (ObjectUtil.isNotNull(list1)) {
                        List<Long> idList = list1.stream().map(VppElectricActualConverge::getUserId).distinct().collect(Collectors.toList());
                        List<Long> missingIds = userIdList.stream().filter(o -> !idList.contains(o)).collect(Collectors.toList());
                        for (Long userIds : missingIds) {
                            String userName = userMaps.get(userIds);
                            notes.append(",").append(userName);
                        }
                    } else {
                        for (Long userIds : userIdList) {
                            String userName = userMaps.get(userIds);
                            notes.append(",").append(userName);
                        }
                    }


                    QueryWrapper<VppElectricQuantityVO> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("veq.running_date", date);
                    queryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
                    queryWrapper.eq("veq.tenant_id", param.getTenantId());
                    queryWrapper.eq("veq.type", 2);
                    VppElectricQuantityVO vppElectricQuantity = baseMapper.findByWrapper(queryWrapper);
                    if (ObjectUtil.isNotNull(vppElectricQuantity)) {
                        importExcelDetailPageListVO.setRunningDate(DateUtil.format(vppElectricQuantity.getRunningDate(), "yyyy-MM-dd"));
                        importExcelDetailPageListVO.setDataName("实际用电量");
                        importExcelDetailPageListVO.setFilename(vppElectricQuantity.getName());
                        importExcelDetailPageListVO.setStatus(1);
                        importExcelDetailPageListVO.setType(2);
                        importExcelDetailPageListVO.setUrl(vppElectricQuantity.getUrl());
                        importExcelDetailPageListVO.setImportDate(vppElectricQuantity.getUploadTime());
                        if (ObjectUtil.isNotEmpty(notes)) {
                            String string = notes.substring(1, notes.length()).toString();
                            importExcelDetailPageListVO.setNotes("没有实际用电量的用户:" + string);
                        }
                        list.add(importExcelDetailPageListVO);
                    } else {
                        importExcelDetailPageListVO.setRunningDate(DateUtil.format(date, "yyyy-MM-dd"));
                        importExcelDetailPageListVO.setDataName("实际用电量");
                        importExcelDetailPageListVO.setFilename("");
                        importExcelDetailPageListVO.setStatus(0);
                        importExcelDetailPageListVO.setType(2);
                        list.add(importExcelDetailPageListVO);
                    }

                    ImportExcelDetailPageListVO excelDetailPageListVO = new ImportExcelDetailPageListVO();
                    //日前申报
                    QueryWrapper<VppElectricQuantityVO> quantityVOQueryWrapper = new QueryWrapper<>();
                    quantityVOQueryWrapper.eq("veq.running_date", date);
                    quantityVOQueryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
                    quantityVOQueryWrapper.eq("veq.tenant_id", param.getTenantId());
                    quantityVOQueryWrapper.eq("veq.type", 1);
                    VppElectricQuantityVO vppRecentlyDeclared = baseMapper.findByWrapper(quantityVOQueryWrapper);
                    if (ObjectUtil.isNotNull(vppRecentlyDeclared)) {
                        excelDetailPageListVO.setRunningDate(DateUtil.format(vppRecentlyDeclared.getRunningDate(), "yyyy-MM-dd"));
                        excelDetailPageListVO.setDataName("日前申报电量");
                        excelDetailPageListVO.setFilename(vppRecentlyDeclared.getName());
                        excelDetailPageListVO.setStatus(1);
                        excelDetailPageListVO.setUrl(vppRecentlyDeclared.getUrl());
                        excelDetailPageListVO.setType(1);
                        excelDetailPageListVO.setImportDate(vppRecentlyDeclared.getUploadTime());
                        list.add(excelDetailPageListVO);
                    } else {
                        excelDetailPageListVO.setRunningDate(DateUtil.format(date, "yyyy-MM-dd"));
                        excelDetailPageListVO.setDataName("日前申报电量");
                        excelDetailPageListVO.setFilename("");
                        excelDetailPageListVO.setStatus(0);
                        excelDetailPageListVO.setType(1);
                        list.add(excelDetailPageListVO);
                    }


                }
            } else if (param.getStatus() == 0) {
                //获取未上传数据
                //获取两个日期之间的全部日期
                List<Date> everyDay = findEveryDay(param.getStartTime(), param.getEndTime());
                for (int i = 0; i < everyDay.size(); i++) {
                    ImportExcelDetailPageListVO importExcelDetailPageListVO = new ImportExcelDetailPageListVO();
                    Date date = everyDay.get(i);
                    QueryWrapper<VppElectricQuantityVO> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("veq.running_date", date);
                    queryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
                    queryWrapper.eq("veq.tenant_id", param.getTenantId());
                    queryWrapper.eq("veq.type", 2);
                    VppElectricQuantityVO vppElectricQuantity = baseMapper.findByWrapper(queryWrapper);
                    if (ObjectUtil.isNull(vppElectricQuantity)) {
                        importExcelDetailPageListVO.setRunningDate(DateUtil.format(date, "yyyy-MM-dd"));
                        importExcelDetailPageListVO.setDataName("实际用电量");
                        importExcelDetailPageListVO.setFilename("");
                        importExcelDetailPageListVO.setStatus(0);
                        importExcelDetailPageListVO.setType(2);
                        list.add(importExcelDetailPageListVO);
                    }

                    ImportExcelDetailPageListVO excelDetailPageListVO = new ImportExcelDetailPageListVO();
                    QueryWrapper<VppElectricQuantityVO> quantityVOQueryWrapper = new QueryWrapper<>();
                    quantityVOQueryWrapper.eq("veq.running_date", date);
                    quantityVOQueryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
                    quantityVOQueryWrapper.eq("veq.tenant_id", param.getTenantId());
                    quantityVOQueryWrapper.eq("veq.type", 1);
                    VppElectricQuantityVO vppRecentlyDeclared = baseMapper.findByWrapper(quantityVOQueryWrapper);
                    if (ObjectUtil.isNull(vppRecentlyDeclared)) {
                        excelDetailPageListVO.setRunningDate(DateUtil.format(date, "yyyy-MM-dd"));
                        excelDetailPageListVO.setDataName("日前申报电量");
                        excelDetailPageListVO.setFilename("");
                        excelDetailPageListVO.setStatus(0);
                        excelDetailPageListVO.setType(1);
                        list.add(excelDetailPageListVO);
                    }

                }
            } else if (param.getStatus() == 1) {
                //获取上传数据
                //获取两个日期之间的全部日期
                List<Date> everyDay = findEveryDay(param.getStartTime(), param.getEndTime());
                for (int i = 0; i < everyDay.size(); i++) {
                    ImportExcelDetailPageListVO importExcelDetailPageListVO = new ImportExcelDetailPageListVO();
                    Date date = everyDay.get(i);
                    //绑定周期判断
                    List<VppElectricActualConverge> list1 = elecMap.get(DateUtil.formatDate(date));
                    StringBuilder notes = new StringBuilder();
                    if (ObjectUtil.isNotNull(list1)) {
                        List<Long> idList = list1.stream().map(VppElectricActualConverge::getUserId).distinct().collect(Collectors.toList());
                        List<Long> missingIds = userIdList.stream().filter(o -> !idList.contains(o)).collect(Collectors.toList());
                        for (Long userIds : missingIds) {
                            String userName = userMaps.get(userIds);
                            notes.append(",").append(userName);
                        }
                    } else {
                        for (Long userIds : userIdList) {
                            String userName = userMaps.get(userIds);
                            notes.append(",").append(userName);
                        }
                    }


                    QueryWrapper<VppElectricQuantityVO> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("veq.running_date", date);
                    queryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
                    queryWrapper.eq("veq.tenant_id", param.getTenantId());
                    queryWrapper.eq("veq.type", 2);
                    VppElectricQuantityVO vppElectricQuantity = baseMapper.findByWrapper(queryWrapper);
                    if (ObjectUtil.isNotNull(vppElectricQuantity)) {
                        importExcelDetailPageListVO.setRunningDate(DateUtil.format(vppElectricQuantity.getRunningDate(), "yyyy-MM-dd"));
                        importExcelDetailPageListVO.setDataName("实际用电量");
                        importExcelDetailPageListVO.setFilename(vppElectricQuantity.getName());
                        importExcelDetailPageListVO.setUrl(vppElectricQuantity.getUrl());
                        importExcelDetailPageListVO.setStatus(1);
                        importExcelDetailPageListVO.setType(2);
                        if (ObjectUtil.isNotEmpty(notes)) {
                            String string = notes.substring(1, notes.length());
                            importExcelDetailPageListVO.setNotes("没有实际用电量的用户:" + string);
                        }
                        importExcelDetailPageListVO.setImportDate(vppElectricQuantity.getUploadTime());
                        list.add(importExcelDetailPageListVO);
                    }

                    ImportExcelDetailPageListVO excelDetailPageListVO = new ImportExcelDetailPageListVO();
                    QueryWrapper<VppElectricQuantityVO> quantityVOQueryWrapper = new QueryWrapper<>();
                    quantityVOQueryWrapper.eq("veq.running_date", date);
                    quantityVOQueryWrapper.eq("veq.is_delete", BooleanEnum.FALSE.getCode());
                    quantityVOQueryWrapper.eq("veq.tenant_id", param.getTenantId());
                    quantityVOQueryWrapper.eq("veq.type", 1);
                    VppElectricQuantityVO vppRecentlyDeclared = baseMapper.findByWrapper(quantityVOQueryWrapper);
                    if (ObjectUtil.isNotNull(vppRecentlyDeclared)) {
                        excelDetailPageListVO.setRunningDate(DateUtil.format(vppRecentlyDeclared.getRunningDate(), "yyyy-MM-dd"));
                        excelDetailPageListVO.setDataName("日前申报电量");
                        excelDetailPageListVO.setFilename(vppRecentlyDeclared.getName());
                        importExcelDetailPageListVO.setUrl(vppRecentlyDeclared.getUrl());
                        excelDetailPageListVO.setStatus(1);
                        excelDetailPageListVO.setType(1);
                        excelDetailPageListVO.setImportDate(vppRecentlyDeclared.getUploadTime());
                        list.add(excelDetailPageListVO);
                    }
                }
            }
        } catch (ParseException e) {
            log.error("解析日期错误");
        }
        FxPage fxPage = this.ListPage(param, list);
        return fxPage;

    }

    @Override
    public DataResult<ImportExcelDetailVO> importRecord(VppElectricQuantityDTO param, Integer type) {

        // 删除旧数据
        VppElectricQuantityVO byTimeAndType = this.findByTimeAndType(param);
        if (ObjectUtil.isNotNull(byTimeAndType)) {
            this.delete(new IdDTO(byTimeAndType.getId()));
            seElectricDeclareService.deleteByElectricId(byTimeAndType.getId());
        }

        // 1. 添加电量数据
        VppElectricQuantity vppElectricQuantity = new VppElectricQuantity();
        BeanUtil.copyProperties(param, vppElectricQuantity);
        baseMapper.insert(vppElectricQuantity);

        //日前申报电量
        List<VppElectricDeclareDTO> electricDeclareList = param.getElectricDeclareList();
        for (VppElectricDeclareDTO electricDeclareDTO : electricDeclareList) {
            electricDeclareDTO.setElectricId(vppElectricQuantity.getId());
        }
        seElectricDeclareService.addElectricDeclareList(electricDeclareList);

        //维护日历数据
        DataMaintenanceUpdateDTO updateDTO = new DataMaintenanceUpdateDTO();
        updateDTO.setTenantId(param.getTenantId());
        updateDTO.setDateDay(param.getRunningDate());
        updateDTO.setDeclareStatus(1);
        updateDTO.setDeclareSources(type);
        dataMaintenanceService.updateStatus(updateDTO);

        return DataResult.success();
    }

    @Override
    public List<VppElectricDeclareVO> getAllReportElectricity(WholesaleAnalysisDTO param) {
        return baseMapper.getAllReportElectricity(param);
    }

    @Override
    public DataResult<Void> deleteRecordData(DeleteRecordDTO deleteRecordDTO) {
        return seElectricDeclareService.deleteRecordData(deleteRecordDTO);
    }

    @Override
    public DataResult<List<SeElectricDeclareVO>> downloadRecord(CommonDTO.DateDTO param) {
        LambdaQueryWrapper<VppElectricQuantity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppElectricQuantity::getTenantId, param.getTenantId());
        queryWrapper.eq(VppElectricQuantity::getRunningDate, param.getQueryDate());
        queryWrapper.eq(VppElectricQuantity::getIsDelete, 0);
        queryWrapper.eq(VppElectricQuantity::getType, 1);
        List<VppElectricQuantity> vppElectricQuantities = baseMapper.selectList(queryWrapper);
        if (ObjectUtil.isNotEmpty(vppElectricQuantities)) {
            Long id = vppElectricQuantities.get(0).getId();
            //获取日前申报数据详情
            List<SeElectricDeclareVO> list = seElectricDeclareService.getDataByElectricId(id);
            return DataResult.success(list);
        } else {
            return new DataResult<>();
        }
    }


    /**
     * 自定义分页分页
     */
    public FxPage ListPage(VppElectricQuantityDTO.QueryDTO param, List data) {
        Integer pageNum = param.getPage();
        Integer pageSize = param.getPageSize();
        // 记录总数
        Integer count = data.size();
        // 页数
        int pageCount;
        if (count % pageSize == 0) {
            pageCount = count / pageSize;
        } else {
            pageCount = count / pageSize + 1;
        }
        // 开始索引
        int fromIndex;
        // 结束索引
        int toIndex;
        if (!pageNum.equals(pageCount)) {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = fromIndex + pageSize;
        } else {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = count;
        }
        List pageList = new ArrayList<>();
        if (data.size() != 0) {
            pageList = data.subList(fromIndex, toIndex);
        }
        return FxPage.page(pageList, count, pageNum, pageSize);
    }

    /**
     * 传入两个时间范围，返回这两个时间范围内的所有日期，并保存在一个集合中
     *
     * @param beginTime
     * @param endTime
     * @return
     * @throws ParseException
     */
    public static List<Date> findEveryDay(Date beginTime, Date endTime) throws ParseException, ParseException {
        //创建一个放所有日期的集合
        List<Date> dates = new ArrayList();

        //将格式化后的第一天添加进集合
        dates.add(beginTime);
        //使用本地的时区和区域获取日历
        Calendar calBegin = Calendar.getInstance();
        //传入起始时间将此日历设置为起始日历
        calBegin.setTime(beginTime);
        //判断结束日期是否在起始日历的日期之后
        while (endTime.after(calBegin.getTime())) {
            //根据日历的规则:月份中的每一天，为起始日历加一天
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            //得到的每一天就添加进集合
            dates.add(calBegin.getTime());
            //如果当前的起始日历超过结束日期后,就结束循环
        }
        return dates;
    }

    // ==================== 导入上下文类 ====================

    /**
     * 导入处理上下文
     */
    @Data
    private static class ImportContext {
        private final Long tenantId;
        private final String url;
        private List<List<String>> excelData;
        private List<VppElectricActualDTO> dataList;
        private List<VppElectricQuantityDTO> resultList;

        public ImportContext(Long tenantId, String url) {
            this.tenantId = tenantId;
            this.url = url;
        }
    }

    // ==================== 简化后的辅助方法 ====================

    /**
     * 文件校验
     */
    private boolean validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            log.warn("文件校验失败: 文件为空");
            return false;
        }

        if (!FileTypeUtil.checkExcelFile(file)) {
            log.warn("文件校验失败: 请上传xls或xlsx类型文件, 当前文件: {}", file.getOriginalFilename());
            return false;
        }

        return true;
    }

    /**
     * 上传文件并创建上下文
     */
    private ImportContext uploadFile(MultipartFile file) {
        try {
            DataResult<VppFileUploadVO> fileUploadResult = vppFileService.uploadFile(file);
            if (fileUploadResult.getData() == null) {
                log.error("文件上传失败: {}", file.getOriginalFilename());
                return null;
            }

            Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
            String url = fileUploadResult.getData().getUrl();
            log.info("文件上传成功 - 租户ID: {}, URL: {}", tenantId, url);

            return new ImportContext(tenantId, url);
        } catch (Exception e) {
            log.error("文件上传异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 读取Excel数据
     */
    private boolean readExcelData(ImportContext context, MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            List<HashMap<Integer, String>> excelData = EasyExcelFactory.read(inputStream)
                    .sheet()
                    .headRowNumber(0)
                    .doReadSync();

            if (excelData.isEmpty()) {
                log.warn("Excel文件内容为空: {}", file.getOriginalFilename());
                return false;
            }

            // 转换为List<List<String>>格式，保持列的顺序
            List<List<String>> convertedData = excelData.stream()
                    .map(rowMap -> {
                        List<String> row = new ArrayList<>();
                        int maxIndex = rowMap.keySet().stream().mapToInt(Integer::intValue).max().orElse(-1);
                        for (int i = 0; i <= maxIndex; i++) {
                            row.add(rowMap.getOrDefault(i, ""));
                        }
                        return row;
                    })
                    .collect(Collectors.toList());

            context.setExcelData(convertedData);
            return true;

        } catch (Exception e) {
            log.error("读取Excel文件异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解析和校验数据
     */
    private boolean parseAndValidateData(ImportContext context, String filename) {
        List<List<String>> importActualList = context.getExcelData();
        if (importActualList.size() < 2) {
            log.warn("Excel文件格式错误: 至少需要包含表头和一行数据, 文件: {}", filename);
            return false;
        }

        try {
            List<VppElectricActualDTO> dataList = parseExcelToDTO(importActualList);

            // 数据校验
            if (dataList.isEmpty()) {
                log.warn("没有解析到有效的电量数据, 文件: {}", filename);
                return false;
            }

            // 校验数据量是否为96的倍数
            if (dataList.size() % VppConstant.NINETY_SIX_NUMBER != 0) {
                log.warn("数据量错误，期望为96的倍数，实际为{}, 文件: {}", dataList.size(), filename);
                return false;
            }

            log.info("成功解析Excel数据，共{}条记录", dataList.size());
            context.setDataList(dataList);
            return true;

        } catch (Exception e) {
            log.error("解析Excel数据异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解析Excel数据为DTO对象
     */
    private List<VppElectricActualDTO> parseExcelToDTO(List<List<String>> importActualList) {
        final int NAME_INDEX = 0, REGISTERED_INDEX = 1, DATE_INDEX = 3, TIME_DATA_START_INDEX = 7;

        List<String> headerRow = importActualList.get(0);
        if (headerRow.size() < TIME_DATA_START_INDEX + 1) {
            throw new IllegalArgumentException("Excel表头格式不正确，缺少必要的列");
        }

        List<String> timeFrameList = headerRow.subList(TIME_DATA_START_INDEX, headerRow.size());
        log.info("解析到{}个时间段", timeFrameList.size());

        return importActualList.stream()
                .skip(1) // 跳过表头
                .filter(row -> row.size() >= headerRow.size()) // 过滤不完整行
                .flatMap(row -> parseRowToDTO(row, timeFrameList, NAME_INDEX, REGISTERED_INDEX, DATE_INDEX, TIME_DATA_START_INDEX).stream())
                .collect(Collectors.toList());
    }

    /**
     * 解析单行数据为DTO列表
     */
    private List<VppElectricActualDTO> parseRowToDTO(List<String> row, List<String> timeFrameList,
                                                     int nameIndex, int registeredIndex, int dateIndex, int timeDataStartIndex) {
        try {
            String name = row.get(nameIndex);
            String registered = row.get(registeredIndex);
            Date dateDay = DateUtil.parseDate(row.get(dateIndex));

            List<VppElectricActualDTO> rowData = new ArrayList<>();
            for (int j = 0; j < timeFrameList.size(); j++) {
                int dataIndex = timeDataStartIndex + j;
                if (dataIndex < row.size()) {
                    String electricityStr = row.get(dataIndex);
                    if (StringUtils.isNotBlank(electricityStr)) {
                        VppElectricActualDTO dto = createElectricActualDTO(name, registered, dateDay,
                                timeFrameList.get(j), new BigDecimal(electricityStr));
                        rowData.add(dto);
                    }
                }
            }
            return rowData;
        } catch (Exception e) {
            log.warn("解析行数据失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 创建电量DTO对象
     */
    private VppElectricActualDTO createElectricActualDTO(String name, String registered, Date dateDay,
                                                         String timeFrame, BigDecimal electricity) {
        VppElectricActualDTO dto = new VppElectricActualDTO();
        dto.setName(name);
        dto.setRegistered(registered);
        dto.setDateDay(dateDay);
        dto.setTimeFrame(timeFrame);
        dto.setElectricity(electricity);
        return dto;
    }

    /**
     * 数据聚合处理
     */
    private void aggregateData(ImportContext context, String filename) {
        List<VppElectricQuantityDTO> resultList = aggregateAndProcessData(context.getDataList(), filename, context.getUrl(), context.getTenantId());
        context.setResultList(resultList);
    }

    /**
     * 数据聚合和处理
     */
    private List<VppElectricQuantityDTO> aggregateAndProcessData(List<VppElectricActualDTO> dataList,
                                                                 String filename,
                                                                 String url,
                                                                 Long tenantId) {
        log.info("开始聚合数据，原始数据量: {}", dataList.size());

        // 使用Stream进行多级分组聚合：日期 -> 用户 -> 时间段
        Map<String, List<VppElectricActualDTO>> aggregatedByDate = dataList.stream()
                .collect(Collectors.groupingBy(
                        dto -> DateUtil.formatDate(dto.getDateDay()),
                        Collectors.collectingAndThen(
                                Collectors.groupingBy(
                                        dto -> dto.getRegistered() + ":" + dto.getTimeFrame(),
                                        Collectors.toList()
                                ),
                                this::aggregateElectricityData
                        )
                ));

        log.info("聚合完成，聚合后按日期分组: {} 天", aggregatedByDate.size());

        // 构建最终结果
        return aggregatedByDate.entrySet().stream()
                .map(entry -> createVppElectricQuantityDTO(filename, url, tenantId, entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
    }

    /**
     * 聚合电量数据
     */
    private List<VppElectricActualDTO> aggregateElectricityData(Map<String, List<VppElectricActualDTO>> groupedData) {
        return groupedData.values().stream()
                .filter(list -> !list.isEmpty())
                .map(this::mergeElectricityData)
                .collect(Collectors.toList());
    }

    /**
     * 合并同一用户同一时间段的电量数据
     */
    private VppElectricActualDTO mergeElectricityData(List<VppElectricActualDTO> dataList) {
        VppElectricActualDTO first = dataList.get(0);
        BigDecimal totalElectricity = dataList.stream()
                .map(VppElectricActualDTO::getElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        VppElectricActualDTO merged = new VppElectricActualDTO();
        merged.setName(first.getName());
        merged.setRegistered(first.getRegistered());
        merged.setUserCode(first.getUserCode());
        merged.setDateDay(first.getDateDay());
        merged.setTimeFrame(first.getTimeFrame());
        merged.setElectricity(totalElectricity);
        return merged;
    }

    /**
     * 创建VppElectricQuantityDTO对象
     */
    private VppElectricQuantityDTO createVppElectricQuantityDTO(String filename, String url, Long tenantId,
                                                                String dateStr, List<VppElectricActualDTO> dataList) {
        VppElectricQuantityDTO dto = new VppElectricQuantityDTO();
        dto.setName(filename);
        dto.setType(2);
        dto.setUrl(url);
        dto.setTenantId(tenantId);
        dto.setUploadTime(new Date());
        dto.setStatus(Boolean.TRUE);
        dto.setRunningDate(DateUtil.parse(dateStr));
        dto.setElectricActualList(dataList);
        return dto;
    }

    /**
     * 创建错误结果
     */
    private ImportExcelVO createErrorResult(String filename, String message) {
        ImportExcelVO result = new ImportExcelVO();
        result.setDetailList(new ArrayList<>());
        result.setSuccessNum(0);
        result.setFailedNum(1);

        ImportExcelDetailVO importDetails = new ImportExcelDetailVO();
        importDetails.setFilename(filename);
        importDetails.setInfoList(new ArrayList<>());
        importDetails.setImportDate(new Date());
        importDetails.setStatus(false);
        importDetails.setMessage(message);
        result.getDetailList().add(importDetails);

        log.warn("导入失败 - 文件: {}, 原因: {}", filename, message);
        return result;
    }

}
