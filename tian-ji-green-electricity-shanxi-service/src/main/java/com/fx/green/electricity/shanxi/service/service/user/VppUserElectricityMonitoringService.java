package com.fx.green.electricity.shanxi.service.service.user;

import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityMonitoringDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;

/**
 * 用户用电监测 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppUserElectricityMonitoringService {

    /**
     * 查询用户树形结构列表
     *
     * @param param 查询用户树形结构DTO
     * @return 用户树形结构列表
     */
    VppLoadUserVO.TreeVO queryTreeList(VppUserElectricityMonitoringDTO.QueryUserTreeDTO param);
}
