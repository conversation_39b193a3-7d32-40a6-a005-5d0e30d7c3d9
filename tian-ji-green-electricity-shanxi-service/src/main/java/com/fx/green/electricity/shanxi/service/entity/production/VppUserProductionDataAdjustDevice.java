package com.fx.green.electricity.shanxi.service.entity.production;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户生产数据-主要调节设备/主要生产工艺段表
 */
@Data
@ApiModel("用户生产数据-主要调节设备/主要生产工艺段表")
public class VppUserProductionDataAdjustDevice implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("用户生产数据id")
    private Long userProductionDataId;

    @ApiModelProperty("设备名称/工艺段名称")
    private String name;

    @ApiModelProperty("额定功率")
    private BigDecimal ratedPower;

    @ApiModelProperty("正常生产功率")
    private BigDecimal normalProductionPower;

    @ApiModelProperty("检修功率")
    private BigDecimal maintenancePower;

    @ApiModelProperty("停产功率")
    private BigDecimal shutdownPower;

    @ApiModelProperty("开机运行时长")
    private BigDecimal startupRuntime;
}