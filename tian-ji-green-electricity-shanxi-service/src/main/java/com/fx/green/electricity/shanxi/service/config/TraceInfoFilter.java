package com.fx.green.electricity.shanxi.service.config;

import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 日志链路trace-id
 *
 * <AUTHOR>
 */
@WebFilter
@Slf4j
public class TraceInfoFilter implements Filter {

    public static final String TRACE_ID = "trace-id";

    public static final String REQUEST_TRACE_ID = "traceId";

    @Override
    public void init(FilterConfig filterConfig) {
        // empty
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, <PERSON><PERSON><PERSON>hai<PERSON> filterChain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) servletRequest;
        HttpServletResponse resp = (HttpServletResponse) servletResponse;

        String traceId = req.getHeader(REQUEST_TRACE_ID);
        if (StringUtils.isBlank(traceId)) {
            traceId = IdUtil.simpleUUID();
        }
        try {
            MDC.put(TRACE_ID, traceId);
            resp.addHeader(REQUEST_TRACE_ID, traceId);

            String requestURI = req.getRequestURI();
            log.info("request-uri:{}", requestURI);

            filterChain.doFilter(req, resp);

        } finally {
            MDC.remove(TRACE_ID);
        }
    }

    @Override
    public void destroy() {
        MDC.remove(TRACE_ID);
    }

}
