package com.fx.green.electricity.shanxi.service.service.electric;


import com.fx.green.electricity.shanxi.api.pojo.dto.electric.ExcessMLDeclareCalDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.FaceElectricityVO;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 公共电量 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppElectricService {

    /**
     * 获取用电量
     * 如果有实际用电量导入用实际用电量
     * 如果没有实际用电量用关口表电量
     *
     * @param tenantId  企业ID
     * @param userList  用户列表
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {userId -> {date -> {time -> electric}}}
     */
    Map<Long, Map<String, Map<String, BigDecimal>>> getElectricMap(Long tenantId, List<VppUserDTO> userList, Date startDate, Date endDate);


    /**
     * 获取用电量
     * 如果有实际用电量导入用实际用电量
     * 如果没有实际用电量用关口表电量
     *
     * @param tenantId  企业ID
     * @param userList  用户列表
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {userId -> {date -> {time -> electric}}}
     */
    Map<Long, Map<String, Map<String, BigDecimal>>> getElectricNewMap(Long tenantId, List<VppUserDTO> userList, Date startDate, Date endDate);

    /**
     * 获取某段时间的实际用电量 聚合成24小时的数据
     *
     * @param param
     * @return
     */
    List<VppElectricActualConverge> getElectricByDate(ExcessMLDeclareCalDTO param);

    /**
     * 获取用户的实际用电量和电价
     * @param param
     * @param dateMap
     * @param timeMap
     * @return
     */
    Map<String, List<FaceElectricityVO>> getAllElecPrice(Date startTime, Date endTime, Long tenantId, Integer type, Map<String, List<Date>> dateMap, Map<String, List<String>> timeMap);


}
