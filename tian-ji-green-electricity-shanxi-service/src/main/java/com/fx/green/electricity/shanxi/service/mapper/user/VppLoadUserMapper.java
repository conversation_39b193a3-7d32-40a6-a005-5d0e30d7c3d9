package com.fx.green.electricity.shanxi.service.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *  负荷用户管理 Mapper
 *
 * <AUTHOR>
 */
@Repository
public interface VppLoadUserMapper extends BaseMapper<VppUser> {

    /**
     * 判断用户户号是否存在
     *
     * @param consNo 户号
     * @param userId 用户id
     * @return 数量
     */
    Integer getLoadUserNumberCount(@Param("consNo") String consNo, @Param("userId") Long userId, @Param("tenantId")  Long tenantId);

    /**
     * 获取虚拟电厂下的全部用户
     *
     * @param tenantId 租户id
     * @return 用户id
     */
    List<String> getUserIds(@Param("tenantId") Long tenantId);

    /**
     * 查询所有社会统一信用代码
     *
     * @return 所有社会统一信用代码
     */
    List<VppUser> getAllUserCode();

    /**
     * 获取全部用户信息
     *
     * @return 全部用户信息
     */
    List<VppUserDTO> getAllUserList();

    /**
     * 获取全部用户的id
     *
     * @param vppId 虚拟电厂id
     * @return 全部用户id
     */
    List<Long> getAllUser(@Param("vppId") Long vppId);

    /**
     * 获取负荷用户信息
     *
     * @param name 负荷用户名字
     * @return 负荷用户信息
     */
    VppUserDTO getLoadUserInfo(@Param("name") String name,@Param("tenantId") Long tenantId);

    /**
     * 通过openId获取用户信息
     *
     * @param openId 微信绑定ID
     * @return 用户信息
     */
    VppLoadUserVO getUserByOpenId(@Param("openId") String openId);

    /**
     * 将之前登录的openId 进行清空
     *
     * @param openId 微信绑定ID
     */
    void updateAllOpenId(@Param("openId") String openId);
}
