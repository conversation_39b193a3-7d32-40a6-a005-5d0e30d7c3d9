package com.fx.green.electricity.shanxi.service.service.data;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeUnitSaveDTO;
import com.fx.green.electricity.shanxi.service.entity.data.GeUnitNodePrice;

/**
 * 机组节点价格
 */
public interface GeUnitNodePriceService extends IService<GeUnitNodePrice> {

    /**
     * 通过不同类型导入机组数据
     *
     * @param param
     */
    void importUnitData(GeUnitSaveDTO param);
}
