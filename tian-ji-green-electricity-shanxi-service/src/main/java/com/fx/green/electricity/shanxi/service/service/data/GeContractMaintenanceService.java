package com.fx.green.electricity.shanxi.service.service.data;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeContractMaintenanceVO;
import com.fx.green.electricity.shanxi.service.entity.data.GeContractMaintenance;

import java.util.List;

/**
 * 合同数据维护
 */
public interface GeContractMaintenanceService extends IService<GeContractMaintenance> {

    /**
     * 新增合同数据
     *
     * @param param
     */
    void insertContract(GeContractMaintenanceDTO param);

    /**
     * 删除合同数据
     *
     * @param param
     */
    void deleteContract(IdDTO param);

    /**
     * 合同数据分页
     *
     * @param param
     * @return
     */
    List<GeContractMaintenanceVO> contractList(GeContractMaintenanceDTO param);
}
