package com.fx.green.electricity.shanxi.service.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDeviceAndMeterDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserDeviceAndMeterVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserDevice;

/**
 * 用户设备及电表管理 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppUserDeviceService extends IService<VppUserDevice> {

    /**
     * 保存或修改用户设备及电表
     *
     * @param param 用户设备及电表DTO
     */
    void saveOrUpdate(VppUserDeviceAndMeterDTO param);

    /**
     * 根据用户户号查询用户设备及电表
     *
     * @param param 用户户号
     * @return 用户设备及电表VO
     */
    VppUserDeviceAndMeterVO getUserDeviceAndMeterByConsNo(VppUserDeviceAndMeterDTO.VppUserDeviceDTO param);
}
