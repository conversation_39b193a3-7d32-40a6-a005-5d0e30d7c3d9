package com.fx.green.electricity.shanxi.service.controller.retail;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.RetailContractsManageDetailDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.UpdateRetailContractsManageDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageListVO;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 零售合同管理模块
 * <AUTHOR>
 **/
@RestController
@Api(tags = "零售合同管理模块")
@RequestMapping("vppRetailContractsManage")
public class VppRetailContractsManageController {

    @Autowired
    private VppRetailContractsManageService vppRetailContractsManageService;


    @ApiOperation("获取零售合同管理列表")
    @PostMapping("getList")
    public DataResult<FxPage<VppRetailContractsManageListVO>> getList(@RequestBody QueryVppRetailContractsDTO param) {
        return vppRetailContractsManageService.getList(param);
    }


    @ApiOperation("获取零售合同详情")
    @PostMapping("retailContractsManageDetail")
    public DataResult<VppRetailContractsManageDetailVO> retailContractsManageDetail(@RequestBody RetailContractsManageDetailDTO retailContractsManageDetailDTO) {
        return vppRetailContractsManageService.retailContractsManageDetail(retailContractsManageDetailDTO);
    }


    @ApiOperation("修改用户的红利系数")
    @PostMapping("updateDividendCoefficient")
    public DataResult<Void> updateDividendCoefficient(@RequestBody UpdateRetailContractsManageDTO param) {
        return vppRetailContractsManageService.updateDividendCoefficient(param);
    }

    @ApiOperation("导出零售合同管理列表")
    @PostMapping("exportManageList")
    public DataResult<List<VppRetailContractsManageListVO>> exportManageList(@RequestBody QueryVppRetailContractsDTO param) {
        return vppRetailContractsManageService.exportManageList(param);
    }

}