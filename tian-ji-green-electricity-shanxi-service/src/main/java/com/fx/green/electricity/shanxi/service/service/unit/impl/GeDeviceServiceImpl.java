package com.fx.green.electricity.shanxi.service.service.unit.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.dto.IdDTO;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeDeviceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeDeviceSaveDTO;
import com.fx.green.electricity.shanxi.service.entity.unit.GeDevice;
import com.fx.green.electricity.shanxi.service.mapper.unit.GeDeviceMapper;
import com.fx.green.electricity.shanxi.service.service.unit.GeDeviceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备信息
 */
@Service
public class GeDeviceServiceImpl extends ServiceImpl<GeDeviceMapper, GeDevice> implements GeDeviceService {
    /**
     * 新增设备信息
     *
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertDevice(GeDeviceSaveDTO param) {
        LambdaQueryWrapper<GeDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(GeDevice::getDeviceName, param.getDeviceList().stream().map(GeDeviceDTO::getDeviceName).collect(Collectors.toList()));
        Integer integer = baseMapper.selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.DeviceInsertRepeatError.getMessage());
        }
        List<GeDevice> collect = param.getDeviceList().stream().map(t -> {
            GeDevice geDevice = new GeDevice();
            BeanUtil.copyProperties(t, geDevice);
            geDevice.setTenantId(param.getTenantId());
            geDevice.setUnitId(param.getUnitId());
            return geDevice;
        }).collect(Collectors.toList());
        this.saveBatch(collect);
    }

    /**
     * 删除设备
     *
     * @param param
     */
    public void deleteDevice(IdDTO param) {
        baseMapper.deleteById(param.getId());
    }
}
