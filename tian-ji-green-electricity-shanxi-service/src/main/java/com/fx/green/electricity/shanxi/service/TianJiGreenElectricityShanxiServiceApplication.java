package com.fx.green.electricity.shanxi.service;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@ServletComponentScan
@EnableDiscoveryClient
@MapperScan(basePackages = "com.fx.green.electricity.shanxi.service.mapper")
@ComponentScan("com.fx")
@EnableFeignClients("com.fx")
public class TianJiGreenElectricityShanxiServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(TianJiGreenElectricityShanxiServiceApplication.class, args);
    }

}
