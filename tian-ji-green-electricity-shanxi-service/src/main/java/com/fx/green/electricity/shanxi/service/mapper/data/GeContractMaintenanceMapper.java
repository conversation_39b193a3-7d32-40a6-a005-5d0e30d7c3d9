package com.fx.green.electricity.shanxi.service.mapper.data;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeContractMaintenanceVO;
import com.fx.green.electricity.shanxi.service.entity.data.GeContractMaintenance;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface GeContractMaintenanceMapper extends BaseMapper<GeContractMaintenance> {

    /**
     * 合同数据分页
     *
     * @param wrapper
     * @return
     */
    List<GeContractMaintenanceVO> contractList(@Param(Constants.WRAPPER) Wrapper<GeContractMaintenanceDTO> wrapper);
}
