package com.fx.green.electricity.shanxi.service.service.data.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.dto.IdDTO;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeContractMaintenanceVO;
import com.fx.green.electricity.shanxi.service.entity.data.GeContractMaintenance;
import com.fx.green.electricity.shanxi.service.mapper.data.GeContractMaintenanceMapper;
import com.fx.green.electricity.shanxi.service.service.data.GeContractMaintenanceService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 合同数据维护
 */
@Service
public class GeContractMaintenanceServiceImpl extends ServiceImpl<GeContractMaintenanceMapper, GeContractMaintenance> implements GeContractMaintenanceService {

    /**
     * 新增合同数据
     *
     * @param param
     */
    @Override
    public void insertContract(GeContractMaintenanceDTO param) {
        LambdaQueryWrapper<GeContractMaintenance> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GeContractMaintenance::getTenantId, param.getTenantId());
        queryWrapper.eq(GeContractMaintenance::getUnitId, param.getUnitId());
        queryWrapper.and(
                wrapper -> wrapper.le(GeContractMaintenance::getTargetStartDate, param.getTargetStartDate()).ge(GeContractMaintenance::getTargetEndDate, param.getTargetStartDate())
                        .or().ge(GeContractMaintenance::getTargetStartDate, param.getTargetStartDate()).le(GeContractMaintenance::getTargetEndDate, param.getTargetEndDate())
                        .or().le(GeContractMaintenance::getTargetStartDate, param.getTargetEndDate()).ge(GeContractMaintenance::getTargetEndDate, param.getTargetEndDate()));
        Integer integer = baseMapper.selectCount(queryWrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.ContractTargetError.getMessage());
        }
        GeContractMaintenance geContractMaintenance = new GeContractMaintenance();
        BeanUtil.copyProperties(param, geContractMaintenance);
        baseMapper.insert(geContractMaintenance);
    }

    /**
     * 删除合同数据
     *
     * @param param
     */
    @Override
    public void deleteContract(IdDTO param) {
        baseMapper.deleteById(param.getId());
    }

    /**
     * 合同数据分页
     *
     * @return
     */
    @Override
    public List<GeContractMaintenanceVO> contractList(GeContractMaintenanceDTO param) {
        QueryWrapper<GeContractMaintenanceDTO> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("gc.target_start_date");
        List<GeContractMaintenanceVO> geContractMaintenanceVOS = baseMapper.contractList(wrapper);
        if (ObjectUtil.isNotEmpty(geContractMaintenanceVOS)) {
            return geContractMaintenanceVOS;
        }
        return Collections.emptyList();
    }
}
