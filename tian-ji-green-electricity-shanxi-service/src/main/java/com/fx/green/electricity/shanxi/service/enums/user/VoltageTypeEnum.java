package com.fx.green.electricity.shanxi.service.enums.user;

/**
 * 供电电压编码枚举
 *
 * <AUTHOR>
 */
public enum VoltageTypeEnum {

    AC00031("AC00031", "交流3kV"),
    AC00061("AC00061", "交流6kV"),
    AC00062("AC00062", "交流6V"),
    AC00101("AC00101", "交流10kV"),
    AC00122("AC00122", "交流12V"),
    AC00201("AC00201", "交流20kV"),
    AC00242("AC00242", "交流24V"),
    AC00351("AC00351", "交流35kV"),
    AC00362("AC00362", "交流36V"),
    AC00482("AC00482", "交流48V"),
    AC00661("AC00661", "交流66kV"),
    AC01101("AC01101", "交流110kV"),
    AC01102("AC01102", "交流110V"),
    AC02201("AC02201", "交流220kV"),
    AC02202("AC02202", "交流220V"),
    AC03301("AC03301", "交流330kV"),
    AC03802("AC03802", "交流380V"),
    AC05001("AC05001", "交流500kV"),
    AC06002("AC06002", "交流600V"),
    AC06602("AC06602", "交流660V"),
    AC07501("AC07501", "交流750kV"),
    AC07502("AC07502", "交流750V"),
    AC10001("AC10001", "交流1000kV"),
    AC10002("AC10002", "交流1000V (含1140V)"),
    AC15002("AC15002", "交流1500V"),
    AC25002("AC25002", "交流2500V"),
    AC30002("AC30002", "交流3000V"),
    DC00062("DC00062", "直流6V"),
    DC00122("DC00122", "直流12V"),
    DC00242("DC00242", "直流24V"),
    DC00362("DC00362", "直流36V"),
    DC00482("DC00482", "直流48V"),
    DC01102("DC01102", "直流110V"),
    DC02202("DC02202", "直流220V"),
    DC05001("DC05001", "直流500kV"),
    DC06002("DC06002", "直流600V"),
    DC07502("DC07502", "直流750V"),
    DC08001("DC08001", "直流800kV"),
    DC15002("DC15002", "直流1500V"),
    DC30002("DC30002", "直流3000V");


    public final String type;
    public final String value;


    VoltageTypeEnum(String type, String value) {
        this.type = type;
        this.value = value;
    }

    /**
     * 根据 type 获取 value
     *
     * @param type 类型
     * @return value
     */
    public static VoltageTypeEnum getValueByType(String type) {
        Integer integer = Integer.valueOf(type);
        for (VoltageTypeEnum loadUerTypeEnum : VoltageTypeEnum.values()) {
            if (loadUerTypeEnum.type.equals(integer)) {
                return loadUerTypeEnum;
            }
        }
        return null;
    }
}
