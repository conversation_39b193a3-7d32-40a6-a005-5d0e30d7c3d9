package com.fx.green.electricity.shanxi.service.entity.production;

import com.fx.green.electricity.shanxi.service.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户生产数据（主表）
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("用户生产数据（主表）")
public class VppUserProductionData extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("负荷用户id")
    private Long userId;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("是否装表(0“已安装，1：未安装)")
    private Integer isInstall;

    @ApiModelProperty("生产流程")
    private String process;

    @ApiModelProperty("时间安排")
    private String timeSchedule;

    @ApiModelProperty("固定检修计划")
    private String fixedMaintenancePlan;

    @ApiModelProperty("最大运行负荷")
    private BigDecimal maxOperatingLoad;

    @ApiModelProperty("最小运行负荷")
    private BigDecimal minOperatingLoad;

    @ApiModelProperty("调节容量")
    private BigDecimal adjustingCapacity;


}