package com.fx.green.electricity.shanxi.service.service.predicted.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryDateOneDTO;
import com.fx.green.electricity.shanxi.service.entity.predicted.VppPredictedReferenceDate;
import com.fx.green.electricity.shanxi.service.mapper.predicted.VppPredictedReferenceDateMapper;
import com.fx.green.electricity.shanxi.service.service.predicted.VppPredictedReferenceDateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 *
 **/
@Service
public class VppPredictedReferenceDateServiceImpl extends ServiceImpl<VppPredictedReferenceDateMapper, VppPredictedReferenceDate> implements VppPredictedReferenceDateService {

    @Override
    public VppPredictedReferenceDate getData(QueryDateOneDTO param) {
        LambdaQueryWrapper<VppPredictedReferenceDate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppPredictedReferenceDate::getDateDay, param.getQueryDate());
        queryWrapper.eq(VppPredictedReferenceDate::getTenantId, param.getTenantId());
        queryWrapper.eq(VppPredictedReferenceDate::getUserId, param.getUserId());
        queryWrapper.eq(VppPredictedReferenceDate::getDimension, param.getDimension());
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addData(List<VppPredictedReferenceDate> predictedReferenceDateList, Integer type) {
        if (ObjectUtil.isNotEmpty(predictedReferenceDateList)) {
            //判断当前日期是否有数据
            LambdaQueryWrapper<VppPredictedReferenceDate> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VppPredictedReferenceDate::getDateDay, predictedReferenceDateList.get(0).getDateDay());
            queryWrapper.eq(VppPredictedReferenceDate::getTenantId, predictedReferenceDateList.get(0).getTenantId());
            queryWrapper.eq(VppPredictedReferenceDate::getDimension, predictedReferenceDateList.get(0).getDimension());
            if (type.equals(2)) {
                queryWrapper.eq(VppPredictedReferenceDate::getUserId, predictedReferenceDateList.get(0).getUserId());
                VppPredictedReferenceDate vppPredictedReferenceDate = baseMapper.selectOne(queryWrapper);
                if (ObjectUtil.isNotNull(vppPredictedReferenceDate)) {
                    for (VppPredictedReferenceDate predictedReferenceDate : predictedReferenceDateList) {
                        String name = predictedReferenceDate.getName();
                        String name1 = vppPredictedReferenceDate.getName();
                        if (name.equals(name1)) {
                            predictedReferenceDate.setTypicalDay(predictedReferenceDate.getTypicalDay());
                        }
                    }
                }
            }
            baseMapper.delete(queryWrapper);
            saveBatch(predictedReferenceDateList);
        }
    }

    @Override
    public void deleteData(Long userId, Date dateDay) {
        LambdaQueryWrapper<VppPredictedReferenceDate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppPredictedReferenceDate::getUserId, userId);
        queryWrapper.ge(VppPredictedReferenceDate::getDateDay, dateDay);
        baseMapper.delete(queryWrapper);
    }
}
