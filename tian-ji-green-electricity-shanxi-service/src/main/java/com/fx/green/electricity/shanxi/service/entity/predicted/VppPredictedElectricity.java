package com.fx.green.electricity.shanxi.service.entity.predicted;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 预测电量
**/
@Data
@ApiModel("预测电量")
public class VppPredictedElectricity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;
    @ApiModelProperty("用户名字")
    private String name;
    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("预测日期")
    private Date dateDay;
    @ApiModelProperty("时点")
    private String timeFrame;
    @ApiModelProperty("")
    private BigDecimal electricity;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("预测维度 1.日 2.上旬 3.中旬 4.下旬 5.月")
    private Integer dimension;



}