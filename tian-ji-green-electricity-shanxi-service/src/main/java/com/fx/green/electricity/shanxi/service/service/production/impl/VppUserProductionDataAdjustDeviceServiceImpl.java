package com.fx.green.electricity.shanxi.service.service.production.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionDataAdjustDevice;
import com.fx.green.electricity.shanxi.service.mapper.production.VppUserProductionDataAdjustDeviceMapper;
import com.fx.green.electricity.shanxi.service.service.production.VppUserProductionDataAdjustDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户生产数据-主要调节设备/主要生产工艺段表
 **/
@Slf4j
@Service
public class VppUserProductionDataAdjustDeviceServiceImpl extends ServiceImpl<VppUserProductionDataAdjustDeviceMapper, VppUserProductionDataAdjustDevice> implements VppUserProductionDataAdjustDeviceService {

    private LambdaQueryWrapper<VppUserProductionDataAdjustDevice> buildQueryWrapper(Long id) {
        LambdaQueryWrapper<VppUserProductionDataAdjustDevice> lqw = new LambdaQueryWrapper<>();
        lqw.eq(VppUserProductionDataAdjustDevice::getUserProductionDataId, id);
        return lqw;
    }

    @Override
    public void removeByDataId(Long id) {
        remove(buildQueryWrapper(id));
    }

    @Override
    public List<VppUserProductionDataAdjustDevice> getByDataId(Long id) {
        return this.list(buildQueryWrapper(id));
    }

    @Override
    public List<VppUserProductionDataAdjustDevice> getPowerByIdList(List<Long> deviceIdList) {
        LambdaQueryWrapper<VppUserProductionDataAdjustDevice> lqw = new LambdaQueryWrapper<>();
        lqw.in(VppUserProductionDataAdjustDevice::getId, deviceIdList);
        return list(lqw);

    }
}
