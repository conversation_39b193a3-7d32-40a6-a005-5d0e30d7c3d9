package com.fx.green.electricity.shanxi.service.entity.production;

import com.fx.green.electricity.shanxi.service.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户生产统计（主表）
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("用户生产统计（主表）")
public class VppUserProductionStatistics extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("负荷用户id")
    private Long userId;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("日期")
    private Date dateDay;

    @ApiModelProperty("类型：1.预计生产计划，2.实际生产情况")
    private Integer type;

    @ApiModelProperty("负荷用户名称")
    private String userName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("生产状态：1.正常生产，2.检修，3.增产，4.减产，5.停产")
    @NotNull(message = "生产状态不能为空")
    private Integer productionStatus;

    @ApiModelProperty("生产状态详情：【月检】，【季检】，【年检】，【10%-100%】，【10%-100%】")
    private String productionStatusDetail;


}