package com.fx.green.electricity.shanxi.service.entity.unit;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 绿电租户信息
 */
@Data
@ApiModel("绿电租户信息")
public class GeTenant implements Serializable {
    private static final long serialVersionUID = -4591185583241324995L;

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("租户名称")
    private String tenantName;
    @ApiModelProperty("联系人")
    private String leader;
    @ApiModelProperty("联系方式")
    private String phone;
    @ApiModelProperty("区域编码")
    private String areaCode;
    @ApiModelProperty("地区名称")
    private String areaName;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateUser;
}
