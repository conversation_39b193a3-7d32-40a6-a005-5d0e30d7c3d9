package com.fx.green.electricity.shanxi.service.entity.production;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户生产数据-沟通人员表
 */
@Data
@ApiModel("用户生产数据-沟通人员表")
public class VppUserProductionDataStaff implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("用户生产数据id")
    private Long userProductionDataId;

    @ApiModelProperty("沟通人员姓名")
    private String name;

    @ApiModelProperty("沟通人员联系方式")
    private String contactInformation;

    @ApiModelProperty("职位")
    private String position;

}