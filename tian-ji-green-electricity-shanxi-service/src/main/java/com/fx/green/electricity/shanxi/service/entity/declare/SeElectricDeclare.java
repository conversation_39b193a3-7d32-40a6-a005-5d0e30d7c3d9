package com.fx.green.electricity.shanxi.service.entity.declare;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("现货数据详情")
public class SeElectricDeclare implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("电量数据id")
    private Long electricId;
    @ApiModelProperty("时间")
    private Date dateDay;
    @ApiModelProperty("时段")
    private String timeFrame;
    @ApiModelProperty("电量")
    private BigDecimal electricity;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("是否删除0未删除 1删除")
    private Integer isDelete;
    @ApiModelProperty("创建人")
    private Long createUser;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改人")
    private Long updateUser;
    @ApiModelProperty("修改时间")
    private Date updateTime;

}