package com.fx.green.electricity.shanxi.service.service.retail;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryImportRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContracts;
import org.springframework.web.multipart.MultipartFile;

/**
 * 零售合同管理 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppRetailContractsService extends IService<VppRetailContracts> {

    /**
     * 获取零售列表
     *
     * @param param
     * @return
     */
    DataResult<FxPage<VppRetailContractsVO>> getList(QueryVppRetailContractsDTO param);

    /**
     * 导入零售合同
     *
     * @param queryImportRecordDTO
     * @return
     */
    DataResult<Void> queryImportRecord(QueryImportRecordDTO queryImportRecordDTO);

    /**
     * 导入零售合同
     *
     * @param file
     * @param runMonth
     * @return
     */
    DataResult<Void> importRetailContract(MultipartFile file, String runMonth);
}
