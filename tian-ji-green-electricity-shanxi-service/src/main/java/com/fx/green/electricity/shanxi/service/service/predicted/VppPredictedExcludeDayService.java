package com.fx.green.electricity.shanxi.service.service.predicted;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryDateOneDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.VppPredictedExcludeDayDTO;
import com.fx.green.electricity.shanxi.service.entity.predicted.VppPredictedExcludeDay;

import java.util.Date;
import java.util.List;

/**
*
**/
public interface VppPredictedExcludeDayService extends IService<VppPredictedExcludeDay> {

    DataResult<Void> add(VppPredictedExcludeDayDTO param);

    /**
     * 根据日期进行数据保存，需要删除该租户的当日数据再进行批量新增
     *
     * @param day                        日期
     * @param vppPredictedExcludeDayList 正常异常的数据
     */
    boolean saveListByDay(Date day, List<VppPredictedExcludeDay> vppPredictedExcludeDayList);

    List<VppPredictedExcludeDay> getDataList(Long tenantId);

    List<VppPredictedExcludeDay> getDataListByUser(QueryDateOneDTO param);
}
