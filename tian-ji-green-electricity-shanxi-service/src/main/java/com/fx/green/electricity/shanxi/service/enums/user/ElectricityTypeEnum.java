package com.fx.green.electricity.shanxi.service.enums.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用电类型 枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ElectricityTypeEnum {

    DALIYONG(1, "大工业"),
    YIBAN_GONGYE(2, "一般工商业"),
    PINGFU_GONGYE(3, "普通工业"),
    OTHER(4, "其他");

    /**
     * 代码
     */
    private final int code;
    /**
     * 值
     */
    private final String value;

    /**
     * 根据代码获取用电类型
     *
     * @param code 代码
     * @return 用电类型
     */
    public static ElectricityTypeEnum getByCode(int code) {
        for (ElectricityTypeEnum electricityTypeEnum : values()) {
            if (electricityTypeEnum.code == code) {
                return electricityTypeEnum;
            }
        }
        return null;
    }
}
