package com.fx.green.electricity.shanxi.service.service.data;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeFileRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeDayRecordVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO;
import com.fx.green.electricity.shanxi.service.entity.data.GeFileRecord;

import java.util.List;
import java.util.Map;

/**
 * 文件记录表
 */
public interface GeFileRecordService extends IService<GeFileRecord> {

    /**
     * 导入文件记录表
     *
     * @param param
     */
    void insertFileRecord(List<GeFileRecordDTO> param);

    /**
     * 文件维护记录
     *
     * @param param
     * @return
     */
    List<GeDayRecordVO> getFileRecordByDay(GeFileRecordDTO param);

    /**
     * 文件列表状态
     *
     * @param param
     * @return
     */
    List<GeFileRecordVO> fileStatus(GeFileRecordDTO param);

    /**
     * 获取当日下数据
     *
     * @param param
     * @return
     */
    Map<String, String> getInfoDataValue(GeFileRecordDTO param);
}
