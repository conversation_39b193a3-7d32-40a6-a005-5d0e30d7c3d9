package com.fx.green.electricity.shanxi.service.service.data.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.enums.GeFileImportEnum;
import com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeFileRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeUnitImportDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeUnitSaveDTO;
import com.fx.green.electricity.shanxi.service.entity.data.GeUnitNodePrice;
import com.fx.green.electricity.shanxi.service.entity.data.GeUnitPowerPredict;
import com.fx.green.electricity.shanxi.service.entity.data.GeUnitRealElectricity;
import com.fx.green.electricity.shanxi.service.entity.unit.GeUnitBasic;
import com.fx.green.electricity.shanxi.service.mapper.data.GeUnitNodePriceMapper;
import com.fx.green.electricity.shanxi.service.service.data.GeFileRecordService;
import com.fx.green.electricity.shanxi.service.service.data.GeUnitNodePriceService;
import com.fx.green.electricity.shanxi.service.service.data.GeUnitPowerPredictService;
import com.fx.green.electricity.shanxi.service.service.data.GeUnitRealElectricityService;
import com.fx.green.electricity.shanxi.service.service.unit.GeUnitBasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 机组节点价格
 */
@Service
public class GeUnitNodePriceServiceImpl extends ServiceImpl<GeUnitNodePriceMapper, GeUnitNodePrice> implements GeUnitNodePriceService {

    @Autowired
    private GeUnitRealElectricityService geUnitRealElectricityService;
    @Autowired
    private GeUnitPowerPredictService geUnitPowerPredictService;
    @Autowired
    private GeUnitBasicService geUnitBasicService;
    @Autowired
    private GeFileRecordService geFileRecordService;

    /**
     * 通过不同类型导入机组数据
     *
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importUnitData(GeUnitSaveDTO param) {
        //机组名称
        List<String> unitNameList = param.getFileDataList().stream().map(GeUnitImportDTO::getUnitName).distinct().collect(Collectors.toList());
        //根据机组名称获取到机组id
        LambdaQueryWrapper<GeUnitBasic> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(GeUnitBasic::getUnitName, unitNameList);
        List<GeUnitBasic> geUnitBasics = geUnitBasicService.getBaseMapper().selectList(wrapper);
        if (unitNameList.size() != geUnitBasics.size()) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.FileUnitCatchBase.getMessage());
        }
        Map<String, GeUnitBasic> unitMap = geUnitBasics.stream().collect(Collectors.toMap(GeUnitBasic::getUnitName, t -> t));

        List<Long> unitList = unitMap.values().stream().map(GeUnitBasic::getId).collect(Collectors.toList());
        List<Date> dateList = param.getFileDataList().stream().map(GeUnitImportDTO::getInfoDate).collect(Collectors.toList());
        if (Objects.equals(param.getFileType(), GeFileImportEnum.unit_real_electricity.getFileType())) {
            LambdaQueryWrapper<GeUnitRealElectricity> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.in(GeUnitRealElectricity::getUnitId, unitList);
            deleteWrapper.in(GeUnitRealElectricity::getInfoDate, dateList);
            geUnitRealElectricityService.remove(deleteWrapper);

            List<GeUnitRealElectricity> collect = param.getFileDataList().stream().map(t -> {
                GeUnitRealElectricity geUnitRealElectricity = new GeUnitRealElectricity();
                geUnitRealElectricity.setUnitId(unitMap.get(t.getUnitName()).getId());
                geUnitRealElectricity.setTenantId(unitMap.get(t.getUnitName()).getTenantId());
                geUnitRealElectricity.setInfoDate(t.getInfoDate());
                geUnitRealElectricity.setTimePoint(t.getTimeFrame());
                geUnitRealElectricity.setValue(t.getValue());
                return geUnitRealElectricity;
            }).collect(Collectors.toList());
            geUnitRealElectricityService.saveBatch(collect);
            //批量导入文件记录
            List<GeFileRecordDTO> fileList = collect.stream().map(t -> {
                GeFileRecordDTO geFileRecordDTO = new GeFileRecordDTO();
                geFileRecordDTO.setBelongId(t.getUnitId());
                geFileRecordDTO.setDataDate(t.getInfoDate());
                geFileRecordDTO.setFileType(GeFileImportEnum.unit_real_electricity.getFileType());
                return geFileRecordDTO;
            }).distinct().collect(Collectors.toList());
            geFileRecordService.insertFileRecord(fileList);
        }
        if (Objects.equals(param.getFileType(), GeFileImportEnum.unit_node_price.getFileType())) {

            LambdaQueryWrapper<GeUnitNodePrice> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.in(GeUnitNodePrice::getUnitId, unitList);
            deleteWrapper.in(GeUnitNodePrice::getInfoDate, dateList);
            this.remove(deleteWrapper);

            List<GeUnitNodePrice> collect = param.getFileDataList().stream().map(t -> {
                GeUnitNodePrice unitNodePrice = new GeUnitNodePrice();
                unitNodePrice.setUnitId(unitMap.get(t.getUnitName()).getId());
                unitNodePrice.setTenantId(unitMap.get(t.getUnitName()).getTenantId());
                unitNodePrice.setInfoDate(t.getInfoDate());
                unitNodePrice.setTimePoint(t.getTimeFrame());
                unitNodePrice.setValue(t.getValue());
                return unitNodePrice;
            }).distinct().collect(Collectors.toList());
            this.saveBatch(collect);

            //批量导入文件记录
            List<GeFileRecordDTO> fileList = collect.stream().map(t -> {
                GeFileRecordDTO geFileRecordDTO = new GeFileRecordDTO();
                geFileRecordDTO.setBelongId(t.getUnitId());
                geFileRecordDTO.setDataDate(t.getInfoDate());
                geFileRecordDTO.setFileType(GeFileImportEnum.unit_node_price.getFileType());
                return geFileRecordDTO;
            }).distinct().collect(Collectors.toList());
            geFileRecordService.insertFileRecord(fileList);
        }
        if (Objects.equals(param.getFileType(), GeFileImportEnum.unit_power_predict.getFileType())) {

            LambdaQueryWrapper<GeUnitPowerPredict> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.in(GeUnitPowerPredict::getUnitId, unitList);
            deleteWrapper.in(GeUnitPowerPredict::getInfoDate, dateList);
            geUnitPowerPredictService.remove(deleteWrapper);

            List<GeUnitPowerPredict> collect = param.getFileDataList().stream().map(t -> {
                GeUnitPowerPredict unitPowerPredict = new GeUnitPowerPredict();
                unitPowerPredict.setUnitId(unitMap.get(t.getUnitName()).getId());
                unitPowerPredict.setTenantId(unitMap.get(t.getUnitName()).getTenantId());
                unitPowerPredict.setInfoDate(t.getInfoDate());
                unitPowerPredict.setTimePoint(t.getTimeFrame());
                unitPowerPredict.setValue(t.getValue());
                return unitPowerPredict;
            }).collect(Collectors.toList());
            geUnitPowerPredictService.saveBatch(collect);

            //批量导入文件记录
            List<GeFileRecordDTO> fileList = collect.stream().map(t -> {
                GeFileRecordDTO geFileRecordDTO = new GeFileRecordDTO();
                geFileRecordDTO.setBelongId(t.getUnitId());
                geFileRecordDTO.setDataDate(t.getInfoDate());
                geFileRecordDTO.setFileType(GeFileImportEnum.unit_power_predict.getFileType());
                return geFileRecordDTO;
            }).distinct().collect(Collectors.toList());
            geFileRecordService.insertFileRecord(fileList);
        }
    }

}
