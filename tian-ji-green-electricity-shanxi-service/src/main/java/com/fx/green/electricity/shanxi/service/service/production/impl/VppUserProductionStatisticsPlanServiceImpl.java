package com.fx.green.electricity.shanxi.service.service.production.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionStatisticsPlan;
import com.fx.green.electricity.shanxi.service.mapper.production.VppUserProductionStatisticsPlanMapper;
import com.fx.green.electricity.shanxi.service.service.production.VppUserProductionStatisticsPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户生产统计 - 生产计划表 Service 实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppUserProductionStatisticsPlanServiceImpl extends ServiceImpl<VppUserProductionStatisticsPlanMapper, VppUserProductionStatisticsPlan> implements VppUserProductionStatisticsPlanService {

    private LambdaQueryWrapper<VppUserProductionStatisticsPlan> buildQueryWrapper(IdDTO param) {
        LambdaQueryWrapper<VppUserProductionStatisticsPlan> lqw = new LambdaQueryWrapper<>();
        lqw.eq(VppUserProductionStatisticsPlan::getUserProductionStatisticsId, param.getId());
        return lqw;
    }

    @Override
    public List<VppUserProductionStatisticsPlan> getDataById(Long id) {
        IdDTO idDTO = new IdDTO();
        idDTO.setId(id);
        return this.list(buildQueryWrapper(idDTO));
    }

    @Override
    public List<VppUserProductionStatisticsPlan> getDataByIdList(List<Long> idList) {
        LambdaQueryWrapper<VppUserProductionStatisticsPlan> lqw = new LambdaQueryWrapper<>();
        lqw.in(VppUserProductionStatisticsPlan::getUserProductionStatisticsId, idList);
        return this.list(lqw);
    }
}
