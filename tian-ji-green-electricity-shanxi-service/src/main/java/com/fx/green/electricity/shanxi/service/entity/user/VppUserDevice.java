package com.fx.green.electricity.shanxi.service.entity.user;

import com.fx.green.electricity.shanxi.service.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 负荷用户设备表
 *
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("负荷用户设备表")
public class VppUserDevice extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备id")
    private Long id;

    @ApiModelProperty("负荷用户id")
    private Long userId;

    @ApiModelProperty("户号id")
    private Long accountId;

    @ApiModelProperty("用户户号")
    private String consNo;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("电压等级(详见VoltageLevelEnum枚举: 1.220Kv 2.110Kv 3.35Kv 4.10Kv 5.6Kv 6.380V 7.220V)")
    private Integer volLevel;

    @ApiModelProperty("设备地区编号")
    private String deviceAreaCode;

    @ApiModelProperty("设备详细地址")
    private String deviceAddressDetail;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("额定电压（kV）")
    private BigDecimal ratedVoltage;

    @ApiModelProperty("额变电容量/变压器报装容量（kVA）")
    private BigDecimal ratedCapacity;

    @ApiModelProperty("最大可调功率（kW）")
    private BigDecimal maxAdjustPower;

    @ApiModelProperty("最大上调功率（kW）")
    private BigDecimal maxUpPower;

    @ApiModelProperty("最大下调功率（kW）")
    private BigDecimal maxDownPower;

    @ApiModelProperty("租户id")
    private Long tenantId;

}