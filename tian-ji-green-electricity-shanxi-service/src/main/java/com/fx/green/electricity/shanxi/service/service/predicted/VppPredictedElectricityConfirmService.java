package com.fx.green.electricity.shanxi.service.service.predicted;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.UpdatePredictedElectricityDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.VppPredictedElectricityConfirmDTO;
import com.fx.green.electricity.shanxi.service.entity.predicted.VppPredictedElectricityConfirm;

import java.util.Date;
import java.util.List;

/**
 *
 **/
public interface VppPredictedElectricityConfirmService extends IService<VppPredictedElectricityConfirm> {

    void saveOrUpdateElectricity(UpdatePredictedElectricityDTO param);

    List<VppPredictedElectricityConfirm> getDataList(VppPredictedElectricityConfirmDTO param);

    void deleteData(Long userId, Date dateDay);
}
