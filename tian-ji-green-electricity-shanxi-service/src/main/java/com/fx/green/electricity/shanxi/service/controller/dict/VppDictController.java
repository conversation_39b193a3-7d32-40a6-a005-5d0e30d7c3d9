package com.fx.green.electricity.shanxi.service.controller.dict;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.GetVppDictionaryDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.dictionary.VppDictionaryVO;
import com.fx.green.electricity.shanxi.service.service.dict.VppDictionaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 字典管理 Controller
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "字典 - 字典管理")
@RequestMapping("/vppDict")
public class VppDictController {

    @Resource
    private VppDictionaryService vppDictionaryService;

    @ApiOperation("获取字典信息")
    @PostMapping("/getDictionary")
    public DataResult<List<VppDictionaryVO>> getDictionary(@RequestBody GetVppDictionaryDTO param) {
        return DataResult.success(vppDictionaryService.getDictionary(param));
    }

}
