package com.fx.green.electricity.shanxi.service.entity.data;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同数据维护
 */
@Data
@ApiModel("合同数据维护")
public class GeContractMaintenance implements Serializable {
    private static final long serialVersionUID = 1998363951172656142L;

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("机组id")
    private Long unitId;
    @ApiModelProperty("合同标的开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date targetStartDate;
    @ApiModelProperty("合同标的结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date targetEndDate;
    @ApiModelProperty("合同价格")
    private BigDecimal contractPrice;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateUser;
}
