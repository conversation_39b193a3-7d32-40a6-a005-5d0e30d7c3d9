package com.fx.green.electricity.shanxi.service.entity.user;

import com.fx.green.electricity.shanxi.service.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 绑定周期
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("绑定周期")
public class VppUserBindCycle extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("绑定周期id")
    private Long id;

    @ApiModelProperty("负荷用户id")
    private Long userId;

    @ApiModelProperty("用户绑定周期开始")
    private Date bindCycleStart;

    @ApiModelProperty("用户绑定周期结束")
    private Date bindCycleEnd;

    @ApiModelProperty("租户id")
    private Long tenantId;

}