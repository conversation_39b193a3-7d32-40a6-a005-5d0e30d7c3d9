package com.fx.green.electricity.shanxi.service.service.production;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.production.VppUserProductionDataDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionDataVO;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionData;

import java.util.List;

/**
 * 用户生产数据 - 主表 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppUserProductionDataService extends IService<VppUserProductionData> {

    /**
     * 根据id删除
     *
     * @param param 用户生产数据id
     */
    void delete(IdDTO param);

    /**
     * 添加/修改用户生产数据
     *
     * @param param 用户生产数据DTO
     */
    void saveOrUpdate(VppUserProductionDataDTO param);

    /**
     * 根据用户id查询用户生产数据(包含详情)
     *
     * @param param 用户id
     * @return 用户生产数据VO
     */
    VppUserProductionDataVO getProductionDataByUserId(IdDTO param);

    /**
     *根据用户id查询用户生产数据(不包含详情)
     * @param idDTO 用户id
     * @return 用户生产数据VO
     */
    VppUserProductionDataVO getByUserId(IdDTO idDTO);

    /**
     *根据用户id列表和租户id获取列表
     * @param tenantId 租户id   
     * @param idList 用户id列表
     * @return 用户生产数据VO列表
     */
    List<VppUserProductionDataVO> getByUserIdList(Long tenantId, List<Long> idList);
}
