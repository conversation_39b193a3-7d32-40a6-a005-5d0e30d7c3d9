package com.fx.green.electricity.shanxi.service.controller.data;

import com.fx.green.electricity.shanxi.service.service.data.GeUnitRealElectricityService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 机组实际发电曲线
 */
@RestController
@Api(tags = "数据维护 - 机组实际发电曲线")
@RequestMapping("geUnitRealElectricity")
public class GeUnitRealElectricityController {

    @Autowired
    private GeUnitRealElectricityService geUnitRealElectricityService;
}
