package com.fx.green.electricity.shanxi.service.utils;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fx.green.electricity.shanxi.service.constant.VppConstant;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 时间工具
 *
 * <AUTHOR>
 */

public class TimeUtils {


    /**
     * 生成相隔时间的数组 每15分钟一个
     */
    public static List<String> minuteList(String start, String end, Integer offSet) {
        List<String> list = new ArrayList<>();
        list.add(start);
        DateTime startTime = DateUtil.parse(start);
        DateTime endTime = null;
        if (end.equals(VppConstant.TIME_END)) {
            endTime = DateUtil.parse(VppConstant.TIME_END_SHIFT);
        } else {
            endTime = DateUtil.parse(end);
        }
        int num = 96;
        for (int i = 0; i < num; i++) {
            startTime = DateUtil.offsetMinute(startTime, offSet);
            String startH = DateUtil.format(startTime, "HH:mm");
            int compare = DateUtil.compare(startTime, endTime);
            if (compare < 0) {
                list.add(startH);
            } else {
                break;
            }
        }
        if (end.equals(end.equals(VppConstant.TIME_END))) {
            list.add(VppConstant.TIME_END_SHIFT);
        }
        list.add(end);
        return list;
    }


    /**
     * 获取两个时间之间的整点
     */
    public static List<String> hoursList(String start, String end) {
        List<String> list = new ArrayList<>();

        String[] split = start.split(":");
        start = split[0] + ":00";
        String[] endSplit = end.split(":");
        if (endSplit[0].equals(VppConstant.TWENTY_FOUR)) {
            end = "23:00";
        } else {
            end = endSplit[0] + ":00";
        }

        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.parse(start), DateUtil.parse(end), DateField.HOUR_OF_DAY);
        for (int i = 1; i < dateTimes.size(); i++) {
            DateTime dateTime = dateTimes.get(i);
            String format = DateUtil.format(dateTime, "HH:mm");
            list.add(format);
        }
        if (endSplit[0].equals(VppConstant.TWENTY_FOUR)) {
            list.add(VppConstant.TIME_END);
        }
        return list;
    }


    /**
     * 获取两个时间之间的整点
     */
    public static List<String> hoursAllList(String start, String end) {
        List<String> list = new ArrayList<>();

        String[] split = start.split(":");
        start = split[0] + ":00";
        String[] endSplit = end.split(":");
        if (endSplit[0].equals(VppConstant.TWENTY_FOUR)) {
            end = "23:00";
        } else {
            end = endSplit[0] + ":00";
        }

        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.parse(start), DateUtil.parse(end), DateField.HOUR_OF_DAY);
        for (int i = 0; i < dateTimes.size(); i++) {
            DateTime dateTime = dateTimes.get(i);
            String format = DateUtil.format(dateTime, "HH:mm");
            list.add(format);
        }
        if (endSplit[0].equals(VppConstant.TWENTY_FOUR)) {
            list.add(VppConstant.TIME_END);
        }
        return list;
    }

    /**
     * 传入两个时间范围，返回这两个时间范围内的所有日期，并保存在一个集合中
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 日期列表
     */
    public static List<Date> findEveryDay(Date beginTime, Date endTime) {
        //创建一个放所有日期的集合
        List<Date> dates = new ArrayList<>();

        //将格式化后的第一天添加进集合
        dates.add(beginTime);
        //使用本地的时区和区域获取日历
        Calendar calBegin = Calendar.getInstance();
        //传入起始时间将此日历设置为起始日历
        calBegin.setTime(beginTime);
        //判断结束日期是否在起始日历的日期之后
        while (endTime.after(calBegin.getTime())) {
            //根据日历的规则:月份中的每一天，为起始日历加一天
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            //得到的每一天就添加进集合
            dates.add(calBegin.getTime());
            //如果当前的起始日历超过结束日期后,就结束循环
        }
        return dates;
    }


    /**
     * 24点转96点 获取4个点
     *
     * @param timeFrame 传入的点
     * @return 4个点
     */
    public static List<String> point24To96(String timeFrame) {
        String[] t = timeFrame.split(":");
        int m = Integer.parseInt(t[0]);
        m -= 1;
        List<String> result = new ArrayList<>();
        result.add(String.format("%02d:%02d", m, 15));
        result.add(String.format("%02d:%02d", m, 30));
        result.add(String.format("%02d:%02d", m, 45));
        result.add(timeFrame);
        return result;
    }

    /**
     * 获取去掉秒的时间数据
     *
     * @return 去掉秒的时间
     */
    public static DateTime date() {
        DateTime date = DateUtil.date();
        return date.offset(DateField.SECOND, -date.second());
    }


    /**
     * 获取两个时间之间的全部时间
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<Date> getDatesBetween(Date startDate, Date endDate) {
        List<Date> dates = new ArrayList<>();
        Date current = (Date) startDate.clone();

        while (current.compareTo(endDate) <= 0) {
            dates.add((Date) current.clone());
            current = DateUtil.offsetDay(current, 1); // 假设DateUtil.offsetDay也支持Date类型
        }
        return dates;
    }

    /**
     * 通过开始时间和结束时间生成月份时间list
     *
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    public static List<String> getMonthsBetween(String startDateStr, String endDateStr) {
        Date startDate = DateUtil.parse(startDateStr, "yyyy-MM");
        Date endDate = DateUtil.parse(endDateStr, "yyyy-MM");
        List<String> months = new ArrayList<>();
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(startDate);
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endDate);

        while (!startCalendar.after(endCalendar)) {
            SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");
            months.add(monthFormat.format(startCalendar.getTime()));
            startCalendar.add(Calendar.MONTH, 1);
        }
        return months;
    }

}
