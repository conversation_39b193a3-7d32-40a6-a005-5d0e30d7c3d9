package com.fx.green.electricity.shanxi.service.entity.data;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件导入记录
 */
@Data
@ApiModel("文件导入记录表")
public class GeFileRecord implements Serializable {
    private static final long serialVersionUID = 112962988853817317L;

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("文件类型")
    private Integer fileType;
    @ApiModelProperty("数据日期")
    private Date dataDate;
    @ApiModelProperty("文件记录中所属id ， 机组或者租户")
    private Long belongId;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateUser;

}
