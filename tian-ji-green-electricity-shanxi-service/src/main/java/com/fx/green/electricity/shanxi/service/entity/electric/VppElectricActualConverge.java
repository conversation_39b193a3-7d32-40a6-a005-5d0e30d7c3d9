package com.fx.green.electricity.shanxi.service.entity.electric;


import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("用户实际电量聚合数据表")
public class VppElectricActualConverge implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("售电租户ID")
    private Long tenantId;
    @ApiModelProperty("电量数据ID")
    private Long electricId;
    @ApiModelProperty("统一社会信用代码")
    private String userCode;
    @ApiModelProperty("日期")
    private Date dateDay;
    @ApiModelProperty("时点")
    private String timeFrame;
    @ApiModelProperty("电量")
    private BigDecimal electricity;
    @ApiModelProperty("负荷用户id")
    private Long userId;

    @TableLogic
    @ApiModelProperty("是否删除")
    private Integer isDelete;
}
