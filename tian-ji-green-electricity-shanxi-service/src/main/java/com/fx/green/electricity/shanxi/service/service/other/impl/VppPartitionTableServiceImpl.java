package com.fx.green.electricity.shanxi.service.service.other.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fx.common.constant.Separator;
import com.fx.green.electricity.shanxi.service.config.DataSourceConfig;
import com.fx.green.electricity.shanxi.service.mapper.other.VppPartitionTableMapper;
import com.fx.green.electricity.shanxi.service.service.other.VppPartitionTableService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@Service
public class VppPartitionTableServiceImpl implements VppPartitionTableService {

    @Resource
    private VppPartitionTableMapper vppPartitionTableMapper;

    @Resource
    private DataSourceConfig dataSourceConfig;

    @Override
    public void createPartitionTable(String masterTable, Date dateDay) {

        String month = DateUtil.format(dateDay, "yyyyMM");
        // 分区表名称
        String tableName = masterTable + Separator.UNDER_SCORE + month;

        //开始月
        DateTime startMonth = DateUtil.beginOfMonth(dateDay);
        //结束月
        DateTime endMonth = DateUtil.offsetMonth(startMonth, 1);

        //查看分区表是否存在
        Integer count = this.countPartitionTable(tableName);
        if (count == 0) {
            if (dataSourceConfig.isOG()) {
                vppPartitionTableMapper.createPartitionTableOG(tableName, masterTable, DateUtil.formatDate(startMonth), DateUtil.formatDate(endMonth));
            } else {
                vppPartitionTableMapper.createPartitionTable(tableName, masterTable, DateUtil.formatDate(startMonth), DateUtil.formatDate(endMonth));
            }
        }
    }

    @Override
    public Integer countPartitionTable(String tableName) {
        if (dataSourceConfig.isOG()) {
            return vppPartitionTableMapper.countPartitionTableOG(tableName);
        } else {
            return vppPartitionTableMapper.countPartitionTable(tableName);
        }
    }


}
