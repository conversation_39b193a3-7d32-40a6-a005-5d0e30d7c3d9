package com.fx.green.electricity.shanxi.service.service.data.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.util.MyDateUtil;
import com.fx.green.electricity.shanxi.api.enums.GeFileImportEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeFileRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeDayRecordVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO;
import com.fx.green.electricity.shanxi.service.entity.data.GeFileRecord;
import com.fx.green.electricity.shanxi.service.entity.data.GeUnitNodePrice;
import com.fx.green.electricity.shanxi.service.entity.data.GeUnitPowerPredict;
import com.fx.green.electricity.shanxi.service.entity.data.GeUnitRealElectricity;
import com.fx.green.electricity.shanxi.service.mapper.data.GeFileRecordMapper;
import com.fx.green.electricity.shanxi.service.service.data.GeFileRecordService;
import com.fx.green.electricity.shanxi.service.service.data.GeUnitNodePriceService;
import com.fx.green.electricity.shanxi.service.service.data.GeUnitPowerPredictService;
import com.fx.green.electricity.shanxi.service.service.data.GeUnitRealElectricityService;
import com.fx.green.electricity.shanxi.service.service.unit.GeTenantService;
import com.fx.green.electricity.shanxi.service.service.unit.GeUnitBasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件导入记录
 */
@Service
public class GeFileRecordServiceImpl extends ServiceImpl<GeFileRecordMapper, GeFileRecord> implements GeFileRecordService {

    @Autowired
    private GeTenantService geTenantService;
    @Autowired
    private GeUnitBasicService geUnitBasicService;
    @Autowired
    private GeUnitRealElectricityService geUnitRealElectricityService;
    @Autowired
    private GeUnitPowerPredictService geUnitPowerPredictService;
    @Autowired
    private GeUnitNodePriceService geUnitNodePriceService;

    /**
     * 导入文件记录表
     *
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertFileRecord(List<GeFileRecordDTO> param) {
        LambdaQueryWrapper<GeFileRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GeFileRecord::getFileType, param.get(0).getFileType());
        queryWrapper.in(GeFileRecord::getDataDate, param.stream().map(GeFileRecordDTO::getDataDate).collect(Collectors.toList()));
        queryWrapper.in(GeFileRecord::getBelongId, param.stream().map(GeFileRecordDTO::getBelongId).collect(Collectors.toList()));
        this.remove(queryWrapper);
        List<GeFileRecord> list = param.stream().map(t -> {
            GeFileRecord geFileRecord = new GeFileRecord();
            BeanUtil.copyProperties(t, geFileRecord);
            return geFileRecord;
        }).collect(Collectors.toList());
        this.saveBatch(list);
    }

    /**
     * 文件维护记录
     *
     * @param param
     * @return
     */
    @Override
    public List<GeDayRecordVO> getFileRecordByDay(GeFileRecordDTO param) {
        List<GeDayRecordVO> resultList = new ArrayList<>();
        List<String> dateList = MyDateUtil.getDateRangeFormatByDate(DateUtil.beginOfMonth(param.getDataDate()),
                DateUtil.endOfMonth(param.getDataDate()), null);
        //租户数量
        Integer tenantCount = geTenantService.getBaseMapper().selectCount(null);
        //机组数量
        Integer unitCount = geUnitBasicService.getBaseMapper().selectCount(null);
        //获取到本月所有导入数据
        LambdaQueryWrapper<GeFileRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(GeFileRecord::getDataDate, DateUtil.beginOfMonth(param.getDataDate()));
        List<GeFileRecord> geFileRecords = baseMapper.selectList(wrapper);
        Map<String, List<GeFileRecord>> fileRecordMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(geFileRecords)) {
            fileRecordMap = geFileRecords.stream().collect(
                    Collectors.groupingBy(t -> t.getFileType() + DateUtil.formatDate(t.getDataDate())));
        }
        for (String date : dateList) {
            GeDayRecordVO geDayRecordVO = new GeDayRecordVO();
            geDayRecordVO.setInfoDate(date);
            List<GeDayRecordVO.FileRecordVO> fileRecordList = new ArrayList<>();
            for (GeFileImportEnum file : GeFileImportEnum.values()) {
                GeDayRecordVO.FileRecordVO fileRecord = new GeDayRecordVO.FileRecordVO();
                fileRecord.setFileType(file.getFileType());
                List<GeFileRecord> fileList = fileRecordMap.getOrDefault(file.getFileType() + date, Collections.emptyList());
                boolean isHold = false;
                if (Objects.equals(file.getFileType(), GeFileImportEnum.unit_real_electricity.getFileType())
                        || Objects.equals(file.getFileType(), GeFileImportEnum.unit_node_price.getFileType())
                        || Objects.equals(file.getFileType(), GeFileImportEnum.unit_power_predict.getFileType())) {
                    isHold = fileList.size() == unitCount;
                }
                if (Objects.equals(file.getFileType(), GeFileImportEnum.tenant_real_electricity.getFileType())) {
                    isHold = fileList.size() == tenantCount;
                }
                if (Objects.equals(file.getFileType(), GeFileImportEnum.front_day_report.getFileType())) {
                    isHold = fileList.size() == 1;
                }
                fileRecord.setIsHold(isHold);
                fileRecordList.add(fileRecord);
            }
            geDayRecordVO.setFileRecordList(fileRecordList);
            resultList.add(geDayRecordVO);
        }
        return resultList;
    }

    /**
     * 文件列表状态
     *
     * @param param
     * @return
     */
    @Override
    public List<GeFileRecordVO> fileStatus(GeFileRecordDTO param) {
        if (Objects.equals(param.getFileType(), GeFileImportEnum.unit_real_electricity.getFileType())
                || Objects.equals(param.getFileType(), GeFileImportEnum.unit_node_price.getFileType())
                || Objects.equals(param.getFileType(), GeFileImportEnum.unit_power_predict.getFileType())) {
            QueryWrapper<GeFileRecordDTO> wrapper = new QueryWrapper<>();
            wrapper.eq("gf.file_type", param.getFileType());
            wrapper.eq("gf.data_date", param.getDataDate());
            List<GeFileRecordVO> fileRecordByUnit = baseMapper.getFileRecordByUnit(wrapper);
            if (ObjectUtil.isEmpty(fileRecordByUnit)) {
                return Collections.emptyList();
            }
            fileRecordByUnit.forEach(t -> t.setStatus(ObjectUtil.isNotEmpty(t.getDataDate())));
            return fileRecordByUnit;
        }
        if (Objects.equals(param.getFileType(), GeFileImportEnum.tenant_real_electricity.getFileType())) {
            QueryWrapper<GeFileRecordDTO> wrapper = new QueryWrapper<>();
            wrapper.eq("gf.file_type", param.getFileType());
            wrapper.eq("gf.data_date", param.getDataDate());
            List<GeFileRecordVO> fileRecordByUnit = baseMapper.getFileRecordByTenant(wrapper);
            if (ObjectUtil.isEmpty(fileRecordByUnit)) {
                return Collections.emptyList();
            }
            fileRecordByUnit.forEach(t -> t.setStatus(ObjectUtil.isNotEmpty(t.getDataDate())));
            return fileRecordByUnit;
        }
        return Collections.emptyList();
    }

    /**
     * 获取当日下数据
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, String> getInfoDataValue(GeFileRecordDTO param) {
        if (Objects.equals(param.getFileType(), GeFileImportEnum.unit_real_electricity.getFileType())) {
            // 展示机组实际发电量
            LambdaQueryWrapper<GeUnitRealElectricity> realWrapper = new LambdaQueryWrapper<>();
            realWrapper.eq(GeUnitRealElectricity::getUnitId, param.getUnitId());
            realWrapper.eq(GeUnitRealElectricity::getInfoDate, param.getDataDate());
            List<GeUnitRealElectricity> realList = geUnitRealElectricityService.getBaseMapper().selectList(realWrapper);
            if (ObjectUtil.isEmpty(realList)) {
                return Collections.emptyMap();
            }
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("日期", DateUtil.formatDate(realList.get(0).getInfoDate()));
            realList.forEach(t -> resultMap.put(t.getTimePoint(), t.getValue().setScale(3, RoundingMode.HALF_UP).toString()));
            return resultMap;
        }
        if (Objects.equals(param.getFileType(), GeFileImportEnum.unit_node_price.getFileType())) {
            // 展示机组节点价格
            LambdaQueryWrapper<GeUnitNodePrice> realWrapper = new LambdaQueryWrapper<>();
            realWrapper.eq(GeUnitNodePrice::getUnitId, param.getUnitId());
            realWrapper.eq(GeUnitNodePrice::getInfoDate, param.getDataDate());
            List<GeUnitNodePrice> nodeList = geUnitNodePriceService.getBaseMapper().selectList(realWrapper);
            if (ObjectUtil.isEmpty(nodeList)) {
                return Collections.emptyMap();
            }
            Map<String, String> resultMap = new LinkedHashMap<>();
            resultMap.put("日期", DateUtil.formatDate(nodeList.get(0).getInfoDate()));
            nodeList.forEach(t -> resultMap.put(t.getTimePoint(), t.getValue().setScale(3, RoundingMode.HALF_UP).toString()));
            return resultMap;
        }
        if (Objects.equals(param.getFileType(), GeFileImportEnum.unit_power_predict.getFileType())) {
            // 展示机组功率预测
            LambdaQueryWrapper<GeUnitPowerPredict> realWrapper = new LambdaQueryWrapper<>();
            realWrapper.eq(GeUnitPowerPredict::getUnitId, param.getUnitId());
            realWrapper.eq(GeUnitPowerPredict::getInfoDate, param.getDataDate());
            List<GeUnitPowerPredict> powerList = geUnitPowerPredictService.getBaseMapper().selectList(realWrapper);
            if (ObjectUtil.isEmpty(powerList)) {
                return Collections.emptyMap();
            }
            Map<String, String> resultMap = new LinkedHashMap<>();
            resultMap.put("日期", DateUtil.formatDate(powerList.get(0).getInfoDate()));
            powerList.forEach(t -> resultMap.put(t.getTimePoint(), t.getValue().setScale(3, RoundingMode.HALF_UP).toString()));
            return resultMap;
        }
        if (Objects.equals(param.getFileType(), GeFileImportEnum.tenant_real_electricity.getFileType())) {
            //todo 展示实际用电量
            return null;
        }
        return null;
    }
}
