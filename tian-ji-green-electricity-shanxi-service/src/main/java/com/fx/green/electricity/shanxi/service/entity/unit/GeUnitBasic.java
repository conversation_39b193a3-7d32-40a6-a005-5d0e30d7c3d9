package com.fx.green.electricity.shanxi.service.entity.unit;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 机组信息表
 */
@Data
@ApiModel("机组信息表")
public class GeUnitBasic implements Serializable {
    private static final long serialVersionUID = -4090623415869697097L;

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("机组名称")
    private String unitName;
    @ApiModelProperty("装机容量")
    private BigDecimal capacity;
    @ApiModelProperty("机组类型")
    private Integer type;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateUser;
}
