package com.fx.green.electricity.shanxi.service.service.predicted.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.UpdatePredictedElectricityDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.VppPredictedElectricityConfirmDTO;
import com.fx.green.electricity.shanxi.service.constant.VppConstant;
import com.fx.green.electricity.shanxi.service.entity.predicted.VppPredictedElectricityConfirm;
import com.fx.green.electricity.shanxi.service.mapper.predicted.VppPredictedElectricityConfirmMapper;
import com.fx.green.electricity.shanxi.service.service.predicted.VppPredictedElectricityConfirmService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 **/
@Service
public class VppPredictedElectricityConfirmServiceImpl extends ServiceImpl<VppPredictedElectricityConfirmMapper, VppPredictedElectricityConfirm> implements VppPredictedElectricityConfirmService {

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateElectricity(UpdatePredictedElectricityDTO param) {
        List<VppPredictedElectricityConfirm> list = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(param.getDataList())) {
            List<UpdatePredictedElectricityDTO.UpdatePredictedElectricityInfo> dataList = param.getDataList();
            for (UpdatePredictedElectricityDTO.UpdatePredictedElectricityInfo updatePredictedElectricityDTO : dataList) {
                List<BigDecimal> electricityList = updatePredictedElectricityDTO.getElectricityList();
                if (ObjectUtil.isNotNull(electricityList) && ObjectUtil.isNotEmpty(electricityList)) {
                    Long tenantId = param.getTenantId();
                    Long userId = updatePredictedElectricityDTO.getUserId();
                    Date dateDay = updatePredictedElectricityDTO.getDateDay();
                    Integer dimension = updatePredictedElectricityDTO.getDimension();
                    //删除日期中的数据
                    LambdaQueryWrapper<VppPredictedElectricityConfirm> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(VppPredictedElectricityConfirm::getDateDay, dateDay);
                    queryWrapper.eq(VppPredictedElectricityConfirm::getTenantId, tenantId);
                    queryWrapper.eq(VppPredictedElectricityConfirm::getUserId, userId);
                    queryWrapper.eq(VppPredictedElectricityConfirm::getDimension, dimension);
                    remove(queryWrapper);
                    //组装数据
                    for (int i = 0; i < VppConstant.NINETY_SIX_TIMES.length; i++) {
                        String ninetySixTime = VppConstant.NINETY_SIX_TIMES[i];
                        VppPredictedElectricityConfirm vppPredictedElectricityConfirm = new VppPredictedElectricityConfirm();
                        vppPredictedElectricityConfirm.setTenantId(tenantId);
                        vppPredictedElectricityConfirm.setDateDay(dateDay);
                        vppPredictedElectricityConfirm.setUserId(userId);
                        vppPredictedElectricityConfirm.setDimension(dimension);
                        vppPredictedElectricityConfirm.setName(updatePredictedElectricityDTO.getName());
                        vppPredictedElectricityConfirm.setTimeFrame(ninetySixTime);
                        vppPredictedElectricityConfirm.setDimension(updatePredictedElectricityDTO.getDimension());
                        BigDecimal electricity = electricityList.get(i);
                        vppPredictedElectricityConfirm.setElectricity(electricity);
                        list.add(vppPredictedElectricityConfirm);
                    }
                }
            }
        }
        saveBatch(list);
    }

    @Override
    public List<VppPredictedElectricityConfirm> getDataList(VppPredictedElectricityConfirmDTO param) {
        LambdaQueryWrapper<VppPredictedElectricityConfirm> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppPredictedElectricityConfirm::getDateDay, param.getDateDay());
        queryWrapper.eq(VppPredictedElectricityConfirm::getTenantId, param.getTenantId());
        queryWrapper.eq(VppPredictedElectricityConfirm::getDimension, param.getDimension());
        queryWrapper.eq(ObjectUtil.isNotNull(param.getUserId()), VppPredictedElectricityConfirm::getUserId, param.getUserId());
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void deleteData(Long userId, Date dateDay) {
        LambdaQueryWrapper<VppPredictedElectricityConfirm> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppPredictedElectricityConfirm::getUserId, userId);
        queryWrapper.ge(VppPredictedElectricityConfirm::getDateDay, dateDay);
        baseMapper.delete(queryWrapper);
    }
}
