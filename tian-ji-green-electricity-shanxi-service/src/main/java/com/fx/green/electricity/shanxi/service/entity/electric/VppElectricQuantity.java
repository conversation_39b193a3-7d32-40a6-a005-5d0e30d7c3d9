package com.fx.green.electricity.shanxi.service.entity.electric;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fx.green.electricity.shanxi.service.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("电量数据")
public class VppElectricQuantity extends BaseEntity {

    private static final long serialVersionUID = -5795813622640050376L;

    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("售电租户ID")
    private Long tenantId;
    @ApiModelProperty("数据类型 （1. 日前申报电量；2. 实际用电量）")
    private Integer type;
    @ApiModelProperty("运行日期")
    private Date runningDate;
    @ApiModelProperty("上传时间")
    private Date uploadTime;
    @ApiModelProperty("上传状态 （0. 未上传；1. 已上传）")
    private Boolean status;
    @ApiModelProperty("电量总和")
    private BigDecimal sumElectricity;
    @ApiModelProperty("电量最大值")
    private BigDecimal maxElectricity;
    @ApiModelProperty("电量最小值")
    private BigDecimal minElectricity;
    @ApiModelProperty("电量平均值")
    private BigDecimal avgElectricity;
    @ApiModelProperty("是否逻辑删除(0未删除，null删除)")
    @TableLogic
    private Integer isDelete;
    @ApiModelProperty("文件地址")
    private String url;

}