package com.fx.green.electricity.shanxi.service.service.predicted;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.service.entity.predicted.VppPredictedElectricity;

import java.util.Date;
import java.util.List;

/**
 *
 **/
public interface VppPredictedElectricityService extends IService<VppPredictedElectricity> {


    List<PredictedElectricityVO> getPredictionElectricity(Date calDate, Long tenantId, Long userId, Integer type, List<String> predictionDayList, Integer dimension);

    DataResult<VppLoadUserVO.TreeVO> queryTreeListNew(QueryTreeListDTO param);

    DataResult<QueryPredictedElectricityVO> queryPredictedElectricity(QueryPredictedElectricityDTO param);

    DataResult<VppPredictedReferenceDateVO> getReferenceDate(QueryDateOneDTO param);

    DataResult<Void> saveOrUpdateElectricity(UpdatePredictedElectricityDTO param);

    DataResult<Void> confirmPrediction(ConfirmPredictionDTO param);

    DataResult<Void> resetPrediction(ResetPredictedElectricityDTO param);

    DataResult<QueryPredictedElectricityContrastVO> getPredictedContrastData(QueryPredictedElectricityDTO param);

    DataResult<VppPredictedExcludeDateVO> getExcludeDay(QueryDateOneDTO param);

    void deleteData(Long userId, Date dateDay);
}
