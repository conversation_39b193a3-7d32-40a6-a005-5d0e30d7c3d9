package com.fx.green.electricity.shanxi.service.controller.unit;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeTenantDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeTenantVO;
import com.fx.green.electricity.shanxi.service.service.unit.GeTenantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 租户信息维护
 */
@Api(tags = "租户信息维护")
@RestController
@RequestMapping("geTenant")
public class GeTenantController {

    @Autowired
    private GeTenantService geTenantService;

    /**
     * 新增绿电租户
     *
     * @param param
     */
    @ApiOperation("新增绿电租户")
    @PostMapping("insertTenant")
    public DataResult<Void> insertTenant(@RequestBody GeTenantDTO param) {
        geTenantService.insertTenant(param);
        return DataResult.success();
    }

    /**
     * 删除绿电租户
     *
     * @param param
     */
    @ApiOperation("删除绿电租户")
    @PostMapping("deleteTenant")
    public DataResult<Void> deleteTenant(@RequestBody IdDTO param) {
        geTenantService.deleteTenant(param);
        return DataResult.success();
    }

    /**
     * 绿电租户分页
     *
     * @param param
     * @return
     */
    @ApiOperation("绿电租户分页")
    @PostMapping("tenantList")
    public DataResult<List<GeTenantVO>> tenantList(@RequestBody GeTenantDTO param) {
        List<GeTenantVO> geTenantVOS = geTenantService.tenantList(param);
        return DataResult.success(geTenantVOS);
    }

    /**
     * 获取所有租户和机组对应信息
     *
     * @return
     */
    @ApiOperation("获取所有租户和机组对应信息")
    @PostMapping("getTenantUnitDetail")
    public DataResult<List<GeTenantVO>> getTenantUnitDetail() {
        List<GeTenantVO> tenantUnitDetail = geTenantService.getTenantUnitDetail();
        return DataResult.success(tenantUnitDetail);
    }
}
