package com.fx.green.electricity.shanxi.service.entity.production;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户生产数据-发电设备表
 */
@Data
@ApiModel("用户生产数据-发电设备表")
public class VppUserProductionDataPowerDevice implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("用户生产数据id")
    private Long userProductionDataId;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("额定功率")
    private BigDecimal ratedPower;

    @ApiModelProperty("正常生产功率")
    private BigDecimal normalProductionPower;


}