package com.fx.green.electricity.shanxi.service.controller.data;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.excel.vo.ImportExcelDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.ImportExcelDetailPageListVO;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricQuantityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 电量数据
 *
 * <AUTHOR>
 **/
@RestController
@Api(tags = "数据维护 - 电量数据")
@RequestMapping("/vppElectricQuantity")
public class VppElectricQuantityController {

    @Resource
    private VppElectricQuantityService vppElectricQuantityService;

    @ApiOperation("查询实际用电量导入记录")
    @PostMapping("/queryImportRecord")
    public DataResult<FxPage<ImportExcelDetailPageListVO>> queryImportRecord(@RequestBody VppElectricQuantityDTO.QueryDTO param) {
        return DataResult.success(vppElectricQuantityService.queryImportRecord(param));
    }

    @CommonNoRepeat
    @ApiOperation("申报电量数据导入")
    @PostMapping("/importRecord")
    public DataResult<ImportExcelDetailVO> importRecord(@RequestBody VppElectricQuantityDTO param) {
        return vppElectricQuantityService.importRecord(param, 2);
    }

    @CommonNoRepeat
    @ApiOperation("日前申报数据下载")
    @PostMapping("/downloadRecord")
    public DataResult<List<SeElectricDeclareVO>> downloadRecord(@RequestBody CommonDTO.DateDTO param) {
        return vppElectricQuantityService.downloadRecord(param);
    }

    @ApiOperation("删除某一天的申报数据")
    @PostMapping("/deleteRecordData")
    public DataResult<Void> deleteRecordData(@RequestBody DeleteRecordDTO deleteRecordDTO) {
        return vppElectricQuantityService.deleteRecordData(deleteRecordDTO);
    }
}
