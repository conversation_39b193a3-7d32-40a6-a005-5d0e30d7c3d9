package com.fx.green.electricity.shanxi.service.controller.production;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.production.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionDataDeviceListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatisticsListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatisticsVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatusVO;
import com.fx.green.electricity.shanxi.service.service.production.VppUserProductionStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 用户生产统计 Controller
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "用户生产统计 - 用户生产统计")
@RequestMapping("/VppUserProductionStatistics")
public class VppUserProductionStatisticsController {

    @Resource
    private VppUserProductionStatisticsService vppUserProductionStatisticsService;

    @ApiOperation("根据用户id查询用户生产计划")
    @PostMapping("/getProductionStatisticsByUserId")
    public DataResult<VppUserProductionStatisticsVO> getProductionStatisticsByUserId(@Valid @RequestBody VppUserProductionStatisticsQueryDetailDTO param) {
        return DataResult.success(vppUserProductionStatisticsService.getProductionStatisticsByUserId(param));
    }

    @ApiOperation("查询某一天的用户生产计划")
    @PostMapping("/getProductionStatisticsByDay")
    public DataResult<VppUserProductionStatisticsListVO> getProductionStatisticsByDay(@RequestBody VppUserProductionStatisticsQueryDTO param) {
        return DataResult.success(vppUserProductionStatisticsService.getProductionStatisticsByDay(param));
    }

    @CommonNoRepeat
    @ApiOperation("添加/修改用户生产计划")
    @PostMapping("/saveUserProductionPlan")
    public DataResult<Void> saveUserProductionPlan(@RequestBody VppUserProductionStatisticsDTO param) {
        vppUserProductionStatisticsService.saveUserProductionPlan(param);
        return DataResult.success();
    }

    @ApiOperation("根据生产计划类型获取设备列表")
    @PostMapping("/getDeviceListByType")
    public DataResult<List<VppUserProductionDataDeviceListVO>> getDeviceListByType(@RequestBody VppUserProductionDataDeviceQueryDTO param) {
        return DataResult.success(vppUserProductionStatisticsService.getDeviceListByType(param));
    }

    @ApiOperation("获取用户的生产状态列表")
    @PostMapping("/getUserProductionStatus")
    public DataResult<List<VppUserProductionStatusVO>> getUserProductionStatus(@RequestBody VppUserProductionStatusQueryDTO param) {
        return DataResult.success(vppUserProductionStatisticsService.getUserProductionStatus(param));
    }

    @ApiOperation("获取所有用户的生产状态列表")
    @PostMapping("/getUserProductionStatusByTenantId")
    public DataResult<List<VppUserProductionStatusVO>> getUserProductionStatusByTenantId(@RequestBody IdDTO param) {
        return DataResult.success(vppUserProductionStatisticsService.getUserProductionStatusByTenantId(param));
    }
}
