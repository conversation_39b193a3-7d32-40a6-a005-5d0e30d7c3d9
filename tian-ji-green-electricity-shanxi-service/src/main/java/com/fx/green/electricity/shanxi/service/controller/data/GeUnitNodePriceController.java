package com.fx.green.electricity.shanxi.service.controller.data;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeUnitSaveDTO;
import com.fx.green.electricity.shanxi.service.service.data.GeUnitNodePriceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 机组节点价格 Controller
 */
@RestController
@Api(tags = "数据维护 - 机组节点价格")
@RequestMapping("/geUnitNodePrice")
public class GeUnitNodePriceController {

    @Resource
    private GeUnitNodePriceService geUnitNodePriceService;

    @ApiOperation("通过不同类型导入机组数据")
    @PostMapping("/importUnitData")
    public DataResult<Void> importUnitData(@RequestBody GeUnitSaveDTO param) {
        geUnitNodePriceService.importUnitData(param);
        return DataResult.success();
    }
}
