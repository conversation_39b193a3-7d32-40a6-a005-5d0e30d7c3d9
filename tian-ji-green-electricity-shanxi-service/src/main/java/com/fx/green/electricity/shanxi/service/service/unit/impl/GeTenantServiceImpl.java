package com.fx.green.electricity.shanxi.service.service.unit.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.dto.IdDTO;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.enums.GreenElectricityServiceCodeEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeTenantDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeTenantVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeUnitBasicVO;
import com.fx.green.electricity.shanxi.service.entity.unit.GeDevice;
import com.fx.green.electricity.shanxi.service.entity.unit.GeTenant;
import com.fx.green.electricity.shanxi.service.entity.unit.GeUnitBasic;
import com.fx.green.electricity.shanxi.service.mapper.unit.GeTenantMapper;
import com.fx.green.electricity.shanxi.service.service.unit.GeDeviceService;
import com.fx.green.electricity.shanxi.service.service.unit.GeTenantService;
import com.fx.green.electricity.shanxi.service.service.unit.GeUnitBasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 绿电租户维护
 */
@Service
public class GeTenantServiceImpl extends ServiceImpl<GeTenantMapper, GeTenant> implements GeTenantService {

    @Autowired
    private GeUnitBasicService geUnitBasicService;
    @Autowired
    private GeDeviceService geDeviceService;

    /**
     * 新增绿电租户
     *
     * @param param
     */
    @Override
    public void insertTenant(GeTenantDTO param) {
        LambdaQueryWrapper<GeTenant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GeTenant::getTenantName, param.getTenantName());
        Integer integer = baseMapper.selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.TenantInsertRepeatError.getMessage());
        }
        GeTenant geTenant = new GeTenant();
        BeanUtil.copyProperties(param, geTenant);
        baseMapper.insert(geTenant);
    }

    /**
     * 删除绿电租户
     *
     * @param param
     */
    @Override
    public void deleteTenant(IdDTO param) {
        LambdaQueryWrapper<GeUnitBasic> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GeUnitBasic::getTenantId, param.getId());
        Integer integer = geUnitBasicService.getBaseMapper().selectCount(wrapper);
        if (integer != 0) {
            throw new FxServiceException(GreenElectricityServiceCodeEnum.TenantDeleteError.getMessage());
        }
        baseMapper.deleteById(param.getId());
    }

    /**
     * 绿电租户分页
     *
     * @param param
     * @return
     */
    @Override
    public List<GeTenantVO> tenantList(GeTenantDTO param) {
        LambdaQueryWrapper<GeTenant> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(ObjectUtil.isNotEmpty(param.getTenantName()), GeTenant::getTenantName, param.getTenantName());
        wrapper.orderByDesc(GeTenant::getUpdateTime);
        List<GeTenant> greenTenantList = baseMapper.selectList(wrapper);
        if (ObjectUtil.isEmpty(greenTenantList)) {
            return Collections.emptyList();
        }
        List<Long> tenantIdList = greenTenantList.stream().map(GeTenant::getId).collect(Collectors.toList());
        LambdaQueryWrapper<GeUnitBasic> unitWrapper = new LambdaQueryWrapper<>();
        unitWrapper.in(GeUnitBasic::getTenantId, tenantIdList);
        List<GeUnitBasic> geUnitBasics = geUnitBasicService.getBaseMapper().selectList(unitWrapper);
        LambdaQueryWrapper<GeDevice> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.in(GeDevice::getTenantId, tenantIdList);
        List<GeDevice> geDevices = geDeviceService.getBaseMapper().selectList(deviceWrapper);
        return greenTenantList.stream().map(t -> {
            GeTenantVO geTenantVO = new GeTenantVO();
            BeanUtil.copyProperties(t, geTenantVO);
            geTenantVO.setUnitCount((int) geUnitBasics.stream().filter(p -> Objects.equals(p.getTenantId(), p.getId())).count());
            geTenantVO.setDeviceCount((int) geDevices.stream().filter(p -> Objects.equals(p.getTenantId(), p.getId())).count());
            return geTenantVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取所有租户和机组对应信息
     *
     * @return
     */
    @Override
    public List<GeTenantVO> getTenantUnitDetail() {
        List<GeTenant> geTenants = baseMapper.selectList(null);
        if (ObjectUtil.isEmpty(geTenants)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<GeUnitBasic> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(GeUnitBasic::getTenantId, geTenants.stream().map(GeTenant::getId).collect(Collectors.toList()));
        List<GeUnitBasic> geUnitBasics = geUnitBasicService.getBaseMapper().selectList(wrapper);
        Map<Long, List<GeUnitBasicVO>> unitMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(geTenants)) {
            unitMap = geUnitBasics.stream().map(t -> {
                GeUnitBasicVO geUnitBasicVO = new GeUnitBasicVO();
                BeanUtil.copyProperties(t, geUnitBasicVO);
                return geUnitBasicVO;
            }).collect(Collectors.groupingBy(GeUnitBasicVO::getTenantId));
        }
        List<GeTenantVO> resultList = new ArrayList<>();
        for (GeTenant geTenant : geTenants) {
            GeTenantVO geTenantVO = new GeTenantVO();
            BeanUtil.copyProperties(geTenant, geTenantVO);
            geTenantVO.setUnitBasicList(unitMap.getOrDefault(geTenant.getId(), Collections.emptyList()));
            resultList.add(geTenantVO);
        }
        return resultList;
    }
}
