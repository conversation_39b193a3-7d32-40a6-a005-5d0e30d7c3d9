package com.fx.green.electricity.shanxi.service.service.production;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionDataPowerDevice;

import java.util.List;

/**
 * 用户生产数据-发电设备表
 **/
public interface VppUserProductionDataPowerDeviceService extends IService<VppUserProductionDataPowerDevice> {

    void removeByDataId(Long id);

    List<VppUserProductionDataPowerDevice> getByDataId(Long id);

    List<VppUserProductionDataPowerDevice> getAdjustByIdList(List<Long> deviceIdList);

}
