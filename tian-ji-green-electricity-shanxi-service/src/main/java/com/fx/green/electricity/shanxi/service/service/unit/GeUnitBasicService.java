package com.fx.green.electricity.shanxi.service.service.unit;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeUnitBasicDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeUnitBasicVO;
import com.fx.green.electricity.shanxi.service.entity.unit.GeUnitBasic;

import java.util.List;

/**
 * 机组信息维护
 */
public interface GeUnitBasicService extends IService<GeUnitBasic> {

    /**
     * 新增机组信息
     *
     * @param param
     */
    void insertUnitBase(GeUnitBasicDTO param);

    /**
     * 删除机组信息
     *
     * @param param
     */
    void deleteUnitBase(IdDTO param);

    /**
     * 根据租户id查询对应机组
     *
     * @param param
     * @return
     */
    List<GeUnitBasicVO> unitBaseList(IdDTO param);
}
