package com.fx.green.electricity.shanxi.service.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityMonitoringDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import com.fx.green.electricity.shanxi.service.service.user.VppUserElectricityMonitoringService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户用电监测 Service 实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppUserElectricityMonitoringServiceImpl implements VppUserElectricityMonitoringService {

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Override
    public VppLoadUserVO.TreeVO queryTreeList(VppUserElectricityMonitoringDTO.QueryUserTreeDTO param) {
        try {
            // 参数校验
            if (param.getTenantId() == null) {
                throw new FxServiceException("租户ID不能为空");
            }
            if (param.getQueryDate() == null) {
                throw new FxServiceException("查询日期不能为空");
            }

            // 1. 查询虚拟电厂下负荷用户列表
            List<VppUser> allUserList = vppLoadUserService.getListByTenantId(param.getTenantId(), "");

            // 2. 根据绑定周期过滤有效用户
            List<VppUser> validUserList = vppLoadUserService.getUserList(param.getQueryDate(), allUserList);

            // 3. 构建树形用户列表
            List<VppLoadUserVO.TreeUserVO> treeUserVOList = buildTreeUserVOList(validUserList);

            // 4. 构建返回结果
            VppLoadUserVO.TreeVO result = new VppLoadUserVO.TreeVO()
                    .setName("虚拟电厂")
                    .setType(1)
                    .setTreeUserVOList(treeUserVOList);

            log.debug("queryTreeList: 租户{}查询到{}个有效用户", param.getTenantId(), treeUserVOList.size());
            return result;

        } catch (Exception e) {
            log.error("queryTreeList失败，租户ID：{}", param.getTenantId(), e);
            throw new RuntimeException("查询树形用户列表失败", e);
        }
    }

    /**
     * 构建树形用户VO列表
     *
     * @param userList 用户列表
     * @return 树形用户VO列表
     */
    private List<VppLoadUserVO.TreeUserVO> buildTreeUserVOList(List<VppUser> userList) {
        if (CollUtil.isEmpty(userList)) {
            return new ArrayList<>();
        }

        return userList.stream()
                .map(user -> {
                    VppLoadUserVO.TreeUserVO treeUserVO = new VppLoadUserVO.TreeUserVO();
                    BeanUtil.copyProperties(user, treeUserVO);
                    treeUserVO.setType(2); // 用户类型
                    return treeUserVO;
                })
                .collect(Collectors.toList());
    }
}
