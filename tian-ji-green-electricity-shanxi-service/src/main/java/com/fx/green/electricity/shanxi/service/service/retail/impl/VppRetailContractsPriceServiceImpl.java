package com.fx.green.electricity.shanxi.service.service.retail.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsPriceVO;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContractsPrice;
import com.fx.green.electricity.shanxi.service.mapper.retail.VppRetailContractsPriceMapper;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsPriceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 **/
@Service
public class VppRetailContractsPriceServiceImpl extends ServiceImpl<VppRetailContractsPriceMapper, VppRetailContractsPrice> implements VppRetailContractsPriceService {

    @Override
    public List<VppRetailContractsPriceVO> getPriceList(Date signingMonth) {
        LambdaQueryWrapper<VppRetailContractsPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppRetailContractsPrice::getSigningMonth, signingMonth);
        queryWrapper.orderByAsc(VppRetailContractsPrice::getType);
        queryWrapper.orderByAsc(VppRetailContractsPrice::getTimeFrame);
        return BeanUtil.copyToList(baseMapper.selectList(queryWrapper), VppRetailContractsPriceVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addData(Map<String, BigDecimal> priceAllMap, Date signingMonth) {
        List<VppRetailContractsPrice> list = new ArrayList<>();

        for (Map.Entry<String, BigDecimal> entry : priceAllMap.entrySet()) {
            VppRetailContractsPrice vppRetailContractsPrice = getVppRetailContractsPrice(signingMonth, entry);
            list.add(vppRetailContractsPrice);
        }
        LambdaQueryWrapper<VppRetailContractsPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppRetailContractsPrice::getType, 1);
        queryWrapper.eq(VppRetailContractsPrice::getSigningMonth, signingMonth);
        baseMapper.delete(queryWrapper);
        saveBatch(list);
//        MQUtil.sendMessage("tian_ji_vir_retail_contracts_price", JSON.toJSONString(list));

    }

    private static VppRetailContractsPrice getVppRetailContractsPrice(Date signingMonth, Map.Entry<String, BigDecimal> entry) {
        String key = entry.getKey();
        BigDecimal value = entry.getValue();
        VppRetailContractsPrice vppRetailContractsPrice = new VppRetailContractsPrice();
        vppRetailContractsPrice.setSigningMonth(signingMonth);
        vppRetailContractsPrice.setPrice(value);
        vppRetailContractsPrice.setType(1);
        vppRetailContractsPrice.setTimeFrame(key);
        vppRetailContractsPrice.setCreateTime(new Date());
        vppRetailContractsPrice.setUpdateTime(new Date());
        vppRetailContractsPrice.setDataSources(1);
        return vppRetailContractsPrice;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDataList(List<VppRetailContractsPrice> retailContractsPrices) {
        Map<Integer, List<VppRetailContractsPrice>> priceMap = retailContractsPrices.stream().collect(Collectors.groupingBy(VppRetailContractsPrice::getType));
        for (Map.Entry<Integer, List<VppRetailContractsPrice>> entry : priceMap.entrySet()) {
            Integer key = entry.getKey();
            List<VppRetailContractsPrice> vppRetailContractsPrices = priceMap.get(key);
            if (ObjectUtil.isNotEmpty(vppRetailContractsPrices)) {
                Date signingMonth = vppRetailContractsPrices.get(0).getSigningMonth();
                signingMonth = DateUtil.beginOfMonth(signingMonth);
                Integer type = vppRetailContractsPrices.get(0).getType();
                //删除
                LambdaQueryWrapper<VppRetailContractsPrice> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(VppRetailContractsPrice::getType, type);
                queryWrapper.eq(VppRetailContractsPrice::getSigningMonth, signingMonth);
                baseMapper.delete(queryWrapper);
            }
        }
        saveBatch(retailContractsPrices);
//        MQUtil.sendMessage("tian_ji_vir_retail_contracts_price", JSON.toJSONString(retailContractsPrices));
    }
}
