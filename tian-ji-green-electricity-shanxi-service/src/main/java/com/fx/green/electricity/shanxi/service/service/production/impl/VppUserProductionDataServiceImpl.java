package com.fx.green.electricity.shanxi.service.service.production.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.production.VppUserProductionDataDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionDataVO;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionData;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionDataAdjustDevice;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionDataPowerDevice;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionDataStaff;
import com.fx.green.electricity.shanxi.service.mapper.production.VppUserProductionDataMapper;
import com.fx.green.electricity.shanxi.service.service.production.VppUserProductionDataAdjustDeviceService;
import com.fx.green.electricity.shanxi.service.service.production.VppUserProductionDataPowerDeviceService;
import com.fx.green.electricity.shanxi.service.service.production.VppUserProductionDataService;
import com.fx.green.electricity.shanxi.service.service.production.VppUserProductionDataStaffService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户生产数据 Service 实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppUserProductionDataServiceImpl extends ServiceImpl<VppUserProductionDataMapper, VppUserProductionData> implements VppUserProductionDataService {

    @Resource
    private VppUserProductionDataStaffService vppUserProductionDataStaffService;

    @Resource
    private VppUserProductionDataPowerDeviceService vppUserProductionDataPowerDeviceService;

    @Resource
    private VppUserProductionDataAdjustDeviceService vppUserProductionDataAdjustDeviceService;

    private LambdaQueryWrapper<VppUserProductionData> buildQueryWrapper(IdDTO param) {
        LambdaQueryWrapper<VppUserProductionData> lqw = new LambdaQueryWrapper<>();
        lqw.eq(VppUserProductionData::getUserId, param.getId());
        lqw.eq(VppUserProductionData::getTenantId, param.getTenantId());
        return lqw;
    }

    @Override
    public void delete(IdDTO param) {
        VppUserProductionData productionData = getOne(buildQueryWrapper(param));
        if (ObjectUtil.isNotNull(productionData)) {
            vppUserProductionDataStaffService.removeByDataId(productionData.getId());
            vppUserProductionDataPowerDeviceService.removeByDataId(productionData.getId());
            vppUserProductionDataAdjustDeviceService.removeByDataId(productionData.getId());
            removeById(productionData.getId());
        }

    }

    @Override
    public void saveOrUpdate(VppUserProductionDataDTO param) {
        VppUserProductionData productionData = new VppUserProductionData();
        BeanCopyUtils.copy(param, productionData);
        this.saveOrUpdate(productionData);

        if (ObjectUtil.isNotEmpty(param.getStaffVOList())) {
            List<VppUserProductionDataStaff> staffList = BeanCopyUtils.copyList(param.getStaffVOList(), VppUserProductionDataStaff.class);
            List<Long> hasIdList = staffList.stream().map(VppUserProductionDataStaff::getId).filter(Objects::nonNull).collect(Collectors.toList());

            //查询数据库中已有的数据,将参数中没有的数据删掉
            List<VppUserProductionDataStaff> staffs = vppUserProductionDataStaffService.getByDataId(productionData.getId());
            List<Long> removeIdList = staffs.stream().map(VppUserProductionDataStaff::getId).filter(id -> !hasIdList.contains(id)).collect(Collectors.toList());
            vppUserProductionDataStaffService.removeByIds(removeIdList);

            //保存数据
            for (VppUserProductionDataStaff staff : staffList) {
                staff.setUserProductionDataId(productionData.getId());
            }
            vppUserProductionDataStaffService.saveOrUpdateBatch(staffList);
        }
        if (ObjectUtil.isNotNull(param.getAdjustDeviceVOList())) {
            List<VppUserProductionDataAdjustDevice> adjustDeviceList = BeanCopyUtils.copyList(param.getAdjustDeviceVOList(), VppUserProductionDataAdjustDevice.class);
            List<Long> hasIdList = adjustDeviceList.stream().map(VppUserProductionDataAdjustDevice::getId).filter(Objects::nonNull).collect(Collectors.toList());
            //查询数据库中已有的数据,将参数中没有的数据删掉
            List<VppUserProductionDataAdjustDevice> adjustDevices = vppUserProductionDataAdjustDeviceService.getByDataId(productionData.getId());
            List<Long> removeIdList = adjustDevices.stream().map(VppUserProductionDataAdjustDevice::getId).filter(id -> !hasIdList.contains(id)).collect(Collectors.toList());
            vppUserProductionDataAdjustDeviceService.removeByIds(removeIdList);

            //保存数据
            for (VppUserProductionDataAdjustDevice adjustDevice : adjustDeviceList) {
                adjustDevice.setUserProductionDataId(productionData.getId());
            }
            vppUserProductionDataAdjustDeviceService.saveOrUpdateBatch(adjustDeviceList);
        }

        if (ObjectUtil.isNotNull(param.getPowerDeviceVOList())) {
            List<VppUserProductionDataPowerDevice> powerDeviceList = BeanCopyUtils.copyList(param.getPowerDeviceVOList(), VppUserProductionDataPowerDevice.class);
            List<Long> hasIdList = powerDeviceList.stream().map(VppUserProductionDataPowerDevice::getId).filter(Objects::nonNull).collect(Collectors.toList());

            //查询数据库中已有的数据,将参数中没有的数据删掉
            List<VppUserProductionDataPowerDevice> powerDevices = vppUserProductionDataPowerDeviceService.getByDataId(productionData.getId());
            List<Long> removeIdList = powerDevices.stream().map(VppUserProductionDataPowerDevice::getId).filter(id -> !hasIdList.contains(id)).collect(Collectors.toList());
            vppUserProductionDataPowerDeviceService.removeByIds(removeIdList);

            //保存数据
            for (VppUserProductionDataPowerDevice powerDevice : powerDeviceList) {
                powerDevice.setUserProductionDataId(productionData.getId());
            }
            vppUserProductionDataPowerDeviceService.saveOrUpdateBatch(powerDeviceList);
        }
    }

    @Override
    public VppUserProductionDataVO getProductionDataByUserId(IdDTO param) {
        VppUserProductionDataVO resultVO = new VppUserProductionDataVO();
        VppUserProductionData productionData = getOne(buildQueryWrapper(param));
        BeanCopyUtils.copy(productionData, resultVO);
        if (ObjectUtil.isNotNull(productionData)) {
            List<VppUserProductionDataStaff> vppUserProductionDataStaff = vppUserProductionDataStaffService.getByDataId(productionData.getId());
            List<VppUserProductionDataVO.vppUserProductionDataStaffVO> staffVOList = BeanCopyUtils.copyList(vppUserProductionDataStaff, VppUserProductionDataVO.vppUserProductionDataStaffVO.class);
            BeanCopyUtils.copy(vppUserProductionDataStaff, staffVOList);
            resultVO.setStaffVOList(staffVOList);
            List<VppUserProductionDataAdjustDevice> vppUserProductionDataAdjustDevice = vppUserProductionDataAdjustDeviceService.getByDataId(productionData.getId());
            List<VppUserProductionDataVO.VppUserProductionDataAdjustDeviceVO> adjustDeviceVOList = BeanCopyUtils.copyList(vppUserProductionDataAdjustDevice, VppUserProductionDataVO.VppUserProductionDataAdjustDeviceVO.class);
            resultVO.setAdjustDeviceVOList(adjustDeviceVOList);

            List<VppUserProductionDataPowerDevice> vppUserProductionDataPowerDevice = vppUserProductionDataPowerDeviceService.getByDataId(productionData.getId());
            List<VppUserProductionDataVO.VppUserProductionDataPowerDeviceVO> powerDeviceVOList = BeanCopyUtils.copyList(vppUserProductionDataPowerDevice, VppUserProductionDataVO.VppUserProductionDataPowerDeviceVO.class);
            resultVO.setPowerDeviceVOList(powerDeviceVOList);
        }

        return resultVO;
    }

    @Override
    public VppUserProductionDataVO getByUserId(IdDTO idDTO) {
        VppUserProductionData productionData = getOne(buildQueryWrapper(idDTO));
        return BeanCopyUtils.copy(productionData, VppUserProductionDataVO.class);
    }

    @Override
    public List<VppUserProductionDataVO> getByUserIdList(Long tenantId, List<Long> idList) {
        if (ObjectUtil.isEmpty(idList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<VppUserProductionData> lqw = new LambdaQueryWrapper<>();
        lqw.eq(VppUserProductionData::getTenantId, tenantId);
        lqw.in(VppUserProductionData::getUserId, idList);
        List<VppUserProductionData> list = list(lqw);
        return BeanCopyUtils.copyList(list, VppUserProductionDataVO.class);
    }
}
