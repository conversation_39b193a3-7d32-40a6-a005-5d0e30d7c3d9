package com.fx.green.electricity.shanxi.service.controller.user;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserElectricityMonitoringDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.service.service.user.VppUserElectricityMonitoringService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户用电监测 Controller
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "用户管理 - 用户用电监测")
@RequestMapping("/vppUserElectricityMonitoring")
public class VppUserElectricityMonitoringController {

    @Resource
    private VppUserElectricityMonitoringService vppUserElectricityMonitoringService;

    @ApiOperation("负荷用户树级列表")
    @PostMapping("/queryTreeList")
    public DataResult<VppLoadUserVO.TreeVO> queryTreeList(@RequestBody VppUserElectricityMonitoringDTO.QueryUserTreeDTO param) {
        return DataResult.success(vppUserElectricityMonitoringService.queryTreeList(param));
    }

}
