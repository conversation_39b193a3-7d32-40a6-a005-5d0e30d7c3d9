package com.fx.green.electricity.shanxi.service.service.production;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionStatisticsPlan;

import java.util.List;

/**
 * 用户生产统计 - 生产计划表 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppUserProductionStatisticsPlanService extends IService<VppUserProductionStatisticsPlan> {


    /**
     * 根据主表的id获取生产计划
     * @param id 主表的id
     * @return 生产计划列表
     */
    List<VppUserProductionStatisticsPlan> getDataById(Long id);

    /**
     * 根据主表的id列表获取生产计划
     * @param id 主表的id列表
     * @return 生产计划列表
     */
    List<VppUserProductionStatisticsPlan> getDataByIdList(List<Long> id);

}
