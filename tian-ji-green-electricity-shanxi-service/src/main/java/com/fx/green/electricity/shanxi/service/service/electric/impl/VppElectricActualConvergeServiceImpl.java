package com.fx.green.electricity.shanxi.service.service.electric.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.ExcessMLDeclareCalDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.ResultAnalysisDTO;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge;
import com.fx.green.electricity.shanxi.service.mapper.electric.VppElectricActualConvergeMapper;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualConvergeService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 实际用电量合并 Service 实现类
 *
 * <AUTHOR>
 **/
@Service
public class VppElectricActualConvergeServiceImpl extends ServiceImpl<VppElectricActualConvergeMapper, VppElectricActualConverge> implements VppElectricActualConvergeService {

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Override
    public List<VppElectricActualConverge> queryElectricByUserIdList(List<Long> userIdList, Date startTime,
                                                                     Date endTime, Long tenantId) {
        if (CollUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<VppElectricActualConverge> electricConvergeLqw = new LambdaQueryWrapper<>();
        electricConvergeLqw.in(VppElectricActualConverge::getUserId, userIdList);
        electricConvergeLqw.ge(VppElectricActualConverge::getDateDay, startTime);
        electricConvergeLqw.le(VppElectricActualConverge::getDateDay, endTime);
        electricConvergeLqw.eq(VppElectricActualConverge::getTenantId, tenantId);
        return this.list(electricConvergeLqw);
    }

    @Override
    public List<VppElectricActualConverge> queryElectricByUserIdLists(List<Long> userIdList, Date startTime,
                                                                      Date endTime, Long tenantId) {
        if (CollUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        List<VppElectricActualConverge> list = baseMapper.queryElectricByUserIdLists(userIdList, startTime, endTime, tenantId);
        return list;
    }

    @Override
    public List<VppElectricActualConverge> queryElectricByUserIdListNew(List<Long> userIdList, Date startTime, Date endTime, Long tenantId) {
        if (CollUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return baseMapper.queryElectricByUserIdListNew(userIdList, startTime, endTime, tenantId);
    }

    @Override
    public void removeByParam(Date dateDay, Long tenantId) {
        baseMapper.removeByParam(dateDay, tenantId);
    }

    @Override
    public List<VppElectricActualConverge> getElectricByDate(ExcessMLDeclareCalDTO param) {
        return baseMapper.getElectricByDate(param.getTenantId(), DateUtil.beginOfMonth(param.getMonth()), DateUtil.endOfMonth(param.getMonth()));
    }

    @Override
    public List<VppElectricActualConverge> getElecList(ResultAnalysisDTO param, String startTime, String endTime) {
        LambdaQueryWrapper<VppElectricActualConverge> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppElectricActualConverge::getUserId, param.getUserId());
        queryWrapper.ge(VppElectricActualConverge::getDateDay, param.getStartDate());
        queryWrapper.le(VppElectricActualConverge::getDateDay, param.getEndDate());
        queryWrapper.ge(ObjectUtil.isNotNull(startTime), VppElectricActualConverge::getTimeFrame, startTime);
        queryWrapper.le(ObjectUtil.isNotNull(startTime), VppElectricActualConverge::getTimeFrame, endTime);
        queryWrapper.eq(VppElectricActualConverge::getUserId, param.getUserId());
        queryWrapper.eq(VppElectricActualConverge::getIsDelete, 0);
        List<VppElectricActualConverge> vppElectricActuals = baseMapper.selectList(queryWrapper);
        return vppElectricActuals;
    }
}
