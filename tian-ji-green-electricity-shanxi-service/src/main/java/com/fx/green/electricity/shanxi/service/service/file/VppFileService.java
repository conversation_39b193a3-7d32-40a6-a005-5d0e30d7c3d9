package com.fx.green.electricity.shanxi.service.service.file;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.vo.file.VppFileUploadVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件上传 Service 接口
 *
 * <AUTHOR>
 */
public interface VppFileService {

    /**
     * 上传文件
     *
     * @param file 文件
     * @return 文件上传VO
     */
    DataResult<VppFileUploadVO> uploadFile(MultipartFile file);

    /**
     * 上传多个文件
     *
     * @param files 文件
     * @return 文件上传VO列表
     */
    DataResult<List<VppFileUploadVO>> uploadFiles(MultipartFile[] files);
}
