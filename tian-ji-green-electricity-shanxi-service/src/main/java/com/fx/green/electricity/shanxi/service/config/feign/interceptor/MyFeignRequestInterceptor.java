package com.fx.green.electricity.shanxi.service.config.feign.interceptor;

import com.alibaba.nacos.common.utils.StringUtils;
import com.fx.green.electricity.shanxi.service.config.TraceInfoFilter;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.Objects;

/**
 * 自定义的Feign拦截器
 *
 * <AUTHOR>
 */
@Slf4j
public class MyFeignRequestInterceptor implements RequestInterceptor {
    /**
     * 这里可以实现对请求的拦截，对请求添加一些额外信息之类的
     *
     * @param requestTemplate 参数
     */
    @Override
    public void apply(RequestTemplate requestTemplate) {
        final ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Enumeration<String> headerNames = request.getHeaderNames();
            if (Objects.nonNull(headerNames)) {
                while (headerNames.hasMoreElements()) {
                    String name = headerNames.nextElement();
                    if ("content-length".equals(name)) {
                        continue;
                    }
                    String value = request.getHeader(name);
                    requestTemplate.header(name, value);
                }
            }
        }

        // 下探trace_id
        String traceId = MDC.get(TraceInfoFilter.TRACE_ID);
        if (StringUtils.isNotBlank(traceId)) {
            requestTemplate.header(TraceInfoFilter.REQUEST_TRACE_ID, traceId);
        }
    }
}

