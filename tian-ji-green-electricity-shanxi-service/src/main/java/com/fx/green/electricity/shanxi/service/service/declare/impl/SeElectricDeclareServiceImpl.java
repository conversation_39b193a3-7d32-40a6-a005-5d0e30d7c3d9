package com.fx.green.electricity.shanxi.service.service.declare.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricDeclareDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.service.entity.declare.SeElectricDeclare;
import com.fx.green.electricity.shanxi.service.mapper.declare.SeElectricDeclareMapper;
import com.fx.green.electricity.shanxi.service.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.service.service.declare.SeElectricDeclareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 **/
@Service
public class SeElectricDeclareServiceImpl extends ServiceImpl<SeElectricDeclareMapper, SeElectricDeclare> implements SeElectricDeclareService {


    @Autowired
    private DataMaintenanceService dataMaintenanceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addElectricDeclareList(List<VppElectricDeclareDTO> electricDeclareList) {
        List<SeElectricDeclare> seElectricDeclares = BeanUtil.copyToList(electricDeclareList, SeElectricDeclare.class);
        saveBatch(seElectricDeclares);
    }

    @Override
    public void deleteByElectricId(Long id) {
        LambdaQueryWrapper<SeElectricDeclare> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SeElectricDeclare::getElectricId, id);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public DataResult<Void> deleteRecordData(DeleteRecordDTO deleteRecordDTO) {
        LambdaQueryWrapper<SeElectricDeclare> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SeElectricDeclare::getDateDay, deleteRecordDTO.getDateDay());
        queryWrapper.eq(SeElectricDeclare::getTenantId, deleteRecordDTO.getTenantId());
        baseMapper.delete(queryWrapper);

        //维护日历数据
        DataMaintenanceUpdateDTO updateDTO = new DataMaintenanceUpdateDTO();
        updateDTO.setTenantId(deleteRecordDTO.getTenantId());
        updateDTO.setDateDay(deleteRecordDTO.getDateDay());
        updateDTO.setDeclareStatus(2);
        updateDTO.setDeclareSources(0);
        dataMaintenanceService.updateStatus(updateDTO);

        return DataResult.success();
    }

    @Override
    public List<SeElectricDeclareVO> getDataByElectricId(Long id) {
        LambdaQueryWrapper<SeElectricDeclare> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SeElectricDeclare::getElectricId, id);
        List<SeElectricDeclare> seElectricDeclares = baseMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(seElectricDeclares, SeElectricDeclareVO.class);
    }
}
