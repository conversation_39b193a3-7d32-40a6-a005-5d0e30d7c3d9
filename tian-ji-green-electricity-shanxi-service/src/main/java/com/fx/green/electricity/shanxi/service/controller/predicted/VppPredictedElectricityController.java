package com.fx.green.electricity.shanxi.service.controller.predicted;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.QueryPredictedElectricityContrastVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.QueryPredictedElectricityVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.VppPredictedExcludeDateVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.VppPredictedReferenceDateVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.service.service.predicted.VppPredictedElectricityService;
import com.fx.green.electricity.shanxi.service.service.predicted.VppPredictedExcludeDayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *
 **/
@RestController
@Api(tags = "VppPredictedElectricity")
@RequestMapping("vppPredictedElectricity")
public class VppPredictedElectricityController {

    @Resource
    private VppPredictedElectricityService vppPredictedElectricityService;

    @Resource
    private VppPredictedExcludeDayService vppPredictedExcludeDayService;

    @ApiOperation("获取新的树状列表")
    @PostMapping("queryTreeListNew")
    public DataResult<VppLoadUserVO.TreeVO> queryTreeListNew(@RequestBody QueryTreeListDTO param) {
        return vppPredictedElectricityService.queryTreeListNew(param);
    }

    @ApiOperation("获取数据列表")
    @PostMapping("queryPredictedElectricity")
    public DataResult<QueryPredictedElectricityVO> queryPredictedElectricity(@RequestBody QueryPredictedElectricityDTO param) {
        param.setFlag(1);
        return vppPredictedElectricityService.queryPredictedElectricity(param);
    }

    @ApiOperation("获取某一天的参考日")
    @PostMapping("getReferenceDate")
    public DataResult<VppPredictedReferenceDateVO> getReferenceDate(@RequestBody QueryDateOneDTO param) {
        return vppPredictedElectricityService.getReferenceDate(param);
    }

    @ApiOperation("获取不可选择的日期")
    @PostMapping("getExcludeDay")
    public DataResult<VppPredictedExcludeDateVO> getExcludeDay(@RequestBody QueryDateOneDTO param) {
        return vppPredictedElectricityService.getExcludeDay(param);
    }

    @ApiOperation("编辑修改预测电量")
    @PostMapping("saveOrUpdateElectricity")
    public DataResult<Void> saveOrUpdateElectricity(@RequestBody UpdatePredictedElectricityDTO param) {
        return vppPredictedElectricityService.saveOrUpdateElectricity(param);
    }

    @ApiOperation("确认虚拟电厂预测")
    @PostMapping("confirmPrediction")
    public DataResult<Void> confirmPrediction(@RequestBody ConfirmPredictionDTO param) {
        return vppPredictedElectricityService.confirmPrediction(param);
    }

    @ApiOperation("重置虚拟电厂预测")
    @PostMapping("resetPrediction")
    public DataResult<Void> resetPrediction(@RequestBody ResetPredictedElectricityDTO param) {
        return vppPredictedElectricityService.resetPrediction(param);
    }


    @ApiOperation("预测电量和实际用电量数据对比")
    @PostMapping("getPredictedContrastData")
    public DataResult<QueryPredictedElectricityContrastVO> getPredictedContrastData(@RequestBody QueryPredictedElectricityDTO param) {
        return vppPredictedElectricityService.getPredictedContrastData(param);
    }

    @ApiOperation("添加关口表问题日期")
    @PostMapping("addExcludeDay")
    public DataResult<Void> addExcludeDay(@RequestBody VppPredictedExcludeDayDTO param) {
        return vppPredictedExcludeDayService.add(param);
    }


}
