package com.fx.green.electricity.shanxi.service.service.dict;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.GetVppDictionaryDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.dictionary.VppDictionaryVO;
import com.fx.green.electricity.shanxi.service.entity.dictionary.VppDictionary;

import java.util.List;

/**
 * 字典 Service 接口
 *
 * <AUTHOR>
**/
public interface VppDictionaryService extends IService<VppDictionary> {

    /**
     * 获取字典信息
     *
     * @param param 获取字典信息 DTO
     * @return 字典信息列表
     */
    List<VppDictionaryVO> getDictionary(GetVppDictionaryDTO param);
}
