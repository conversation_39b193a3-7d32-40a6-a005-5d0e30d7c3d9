package com.fx.green.electricity.shanxi.service.entity.production;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户生产统计-生产计划表
 */
@Data
@ApiModel("用户生产统计-生产计划表")
public class VppUserProductionStatisticsPlan implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("用户生产统计主键id")
    private Long userProductionStatisticsId;

    @ApiModelProperty("计划类型：1.生产计划，2.调节设备运行计划，3.发电设备运行计划")
    private Integer planType;

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("产品名称")
    private String name;

    @ApiModelProperty("规格")
    private String specifications;

    @ApiModelProperty("产量（吨）")
    private BigDecimal production;

    @ApiModelProperty("设备id")
    private Long deviceId;

    @ApiModelProperty("连续运行时长")
    private BigDecimal continuousOperationDuration;

    @ApiModelProperty("运行功率/发电功率")
    private BigDecimal operatingPower;

}