package com.fx.green.electricity.shanxi.service.controller.user;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserBindCycleDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserBindCycleVO;
import com.fx.green.electricity.shanxi.service.service.user.VppBindCycleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 绑定周期 Controller
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "用户管理 - 绑定周期管理")
@RequestMapping("/vppBindCycle")
public class VppBindCycleController {

    @Resource
    private VppBindCycleService vppBindCycleService;

    @CommonNoRepeat
    @ApiOperation("添加")
    @PostMapping("/add")
    public DataResult<Void> add(@RequestBody VppUserBindCycleDTO param) {
        return vppBindCycleService.addVppBindCycle(param);
    }

    @ApiOperation("列表")
    @PostMapping("/selectList")
    public DataResult<List<VppUserBindCycleVO>> selectList(@RequestBody IdDTO param) {
        List<VppUserBindCycleVO> list = vppBindCycleService.selectList(param);
        return DataResult.success(list);
    }

    @ApiOperation("删除")
    @PostMapping("/delete")
    public DataResult<Void> delete(@RequestBody IdDTO param) {
        return vppBindCycleService.delete(param);
    }

}
