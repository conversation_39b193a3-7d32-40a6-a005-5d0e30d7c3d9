package com.fx.green.electricity.shanxi.service.service.predicted;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.QueryDateOneDTO;
import com.fx.green.electricity.shanxi.service.entity.predicted.VppPredictedReferenceDate;

import java.util.Date;
import java.util.List;

/**
 *
 **/
public interface VppPredictedReferenceDateService extends IService<VppPredictedReferenceDate> {

    VppPredictedReferenceDate getData(QueryDateOneDTO param);

    void addData(List<VppPredictedReferenceDate> predictedReferenceDateList, Integer type);

    void deleteData(Long userId, Date dateDay);
}
