package com.fx.green.electricity.shanxi.service.service.retail.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryImportRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.RetailContractsManageDetailDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.UpdateRetailContractsManageDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricActualForUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.ExcessMLDeclareDateNumVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.*;
import com.fx.green.electricity.shanxi.service.constant.VppConstant;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContractsManage;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContractsPrice;
import com.fx.green.electricity.shanxi.service.enums.DecimalPlaceEnum;
import com.fx.green.electricity.shanxi.service.mapper.retail.VppRetailContractsManageMapper;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualService;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsManageService;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsPriceService;
import com.fx.green.electricity.shanxi.service.utils.DecimalPlaceUtil;
import com.fx.green.electricity.shanxi.service.utils.PointConvertUtil;
import com.fx.operation.api.api.OperationConfigurationApi;
import com.fx.operation.api.api.OperationTransactionScheduleApi;
import com.fx.operation.api.dto.IdListDTO;
import com.fx.operation.api.dto.OpeTransactionScheduleSystemDTO;
import com.fx.operation.api.dto.TenantTypicalCurveDTO;
import com.fx.operation.api.dto.fd.OpeFdSystemRequestDTO;
import com.fx.operation.api.vo.*;
import com.fx.operation.api.vo.fd.OpeFdSystemRequestVO;
import com.fx.publicdata.shanxi.api.api.PublicDataApi;
import com.fx.publicdata.shanxi.api.dto.longTermMarket.PublicTradeQueryDTO;
import com.fx.publicdata.shanxi.api.vo.longTermMarket.LongTermMarketResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.fx.green.electricity.shanxi.service.utils.MonthDaysUtil.getDecadeDays;


/**
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppRetailContractsManageServiceImpl extends ServiceImpl<VppRetailContractsManageMapper, VppRetailContractsManage> implements VppRetailContractsManageService {

    @Resource
    private VppElectricActualService vppElectricActualService;

    @Resource
    private OperationConfigurationApi operationConfigurationApi;

    @Resource
    private VppRetailContractsPriceService vppRetailContractsPriceService;

    @Resource
    private OperationTransactionScheduleApi operationTransactionScheduleApi;

    @Resource
    private PublicDataApi publicDataApi;


    private static final String DATA_SPILT = "卐";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataResult<Void> addList(List<VppRetailContractsManageVO> resultList, QueryImportRecordDTO queryImportRecordDTO) {

        // 判断当前导入的数据中是否有电量 没有的话取补上一个月的实际用电量均值 还没有的话取运营平台配置的典型曲线
        Map<Long, List<VppRetailContractsManageVO>> resultMap = resultList.stream().collect(Collectors.groupingBy(VppRetailContractsManageVO::getUserId));
        for (Long id : resultMap.keySet()) {
            List<VppRetailContractsManageVO> userList = resultMap.get(id);
            Map<Integer, List<VppRetailContractsManageVO>> userMap = userList.stream().collect(Collectors.groupingBy(VppRetailContractsManageVO::getType));
            // 判断当前导入的数据中是否有电量 没有的话取补上一个月的实际用电量均值 还没有的话取运营平台配置的典型曲线
            if (ObjectUtil.isNull(userMap.get(VppConstant.ONE_NUMBER))) {
                List<VppRetailContractsManageVO> priceList = userMap.get(VppConstant.TWO_NUMBER);
                Date runMonth = priceList.get(0).getSigningMonth();
                Long userId = priceList.get(0).getUserId();
                // 获取上一个月的实际用电量
                Date monthStart = DateUtil.beginOfMonth(DateUtil.offsetMonth(runMonth, -1));
                Date monthEnd = DateUtil.endOfMonth(DateUtil.offsetMonth(runMonth, -1));
                List<VppElectricActualForUserVO> powerList = vppElectricActualService.getElectricActual(userId, monthStart, monthEnd);
                if (ObjectUtil.isNotNull(powerList) && ObjectUtil.isNotEmpty(powerList.size())) {
                    for (VppElectricActualForUserVO vo : powerList) {
                        VppRetailContractsManageVO vppRetailContractsManageVO = new VppRetailContractsManageVO();
                        vppRetailContractsManageVO.setSigningMonth(runMonth);
                        vppRetailContractsManageVO.setRetailContractsId(priceList.get(0).getRetailContractsId());
                        vppRetailContractsManageVO.setName(priceList.get(0).getName());
                        vppRetailContractsManageVO.setUserId(priceList.get(0).getUserId());
                        vppRetailContractsManageVO.setCycle(priceList.get(0).getCycle());
                        vppRetailContractsManageVO.setDividendSharCoefficient(priceList.get(0).getDividendSharCoefficient());
                        vppRetailContractsManageVO.setTradeName(priceList.get(0).getTradeName());
                        vppRetailContractsManageVO.setTimeFrame(vo.getTimeFrame());
                        vppRetailContractsManageVO.setVal(vo.getElectricity());
                        vppRetailContractsManageVO.setType(VppConstant.ONE_NUMBER);
                        resultList.add(vppRetailContractsManageVO);
                    }
                } else {
                    // 运营平台配置的典型曲线
                    TenantTypicalCurveDTO tenantTypicalCurveDTO = new TenantTypicalCurveDTO();
                    tenantTypicalCurveDTO.setOperationTime(runMonth);
                    DataResult<List<TenantTcDetailListVO>> tenantTcDetailList = operationConfigurationApi.getTenantTcDetailList(tenantTypicalCurveDTO);
                    List<TenantTcDetailListVO> data = tenantTcDetailList.getData();
                    List<BigDecimal> list = PointConvertUtil.make96To24(data.stream().map(o -> o.getNumericalValue()).collect(Collectors.toList()), "SUM");
                    for (int i = 0; i < list.size(); i++) {
                        BigDecimal val = list.get(i);
                        String timeFrame = VppConstant.TWENTY_FOUR_TIMES[i];
                        VppRetailContractsManageVO vppRetailContractsManageVO = new VppRetailContractsManageVO();
                        vppRetailContractsManageVO.setSigningMonth(runMonth);
                        vppRetailContractsManageVO.setRetailContractsId(priceList.get(0).getRetailContractsId());
                        vppRetailContractsManageVO.setName(priceList.get(0).getName());
                        vppRetailContractsManageVO.setUserId(priceList.get(0).getUserId());
                        vppRetailContractsManageVO.setTradeName(priceList.get(0).getTradeName());
                        vppRetailContractsManageVO.setCycle(priceList.get(0).getCycle());
                        vppRetailContractsManageVO.setDividendSharCoefficient(priceList.get(0).getDividendSharCoefficient());
                        vppRetailContractsManageVO.setTimeFrame(timeFrame);
                        vppRetailContractsManageVO.setVal(val);
                        vppRetailContractsManageVO.setType(VppConstant.ONE_NUMBER);
                        resultList.add(vppRetailContractsManageVO);
                    }
                }

            }
        }
        if (ObjectUtil.isNotEmpty(resultList)) {
            // 从规则获取数据
            Date signingMonth = resultList.get(0).getSigningMonth();
            signingMonth = DateUtil.beginOfMonth(signingMonth);
            // 保存数据
            saveData(signingMonth);
            // 获取数据
            List<VppRetailContractsPriceVO> priceList = vppRetailContractsPriceService.getPriceList(signingMonth);
            Map<String, List<VppRetailContractsPriceVO>> priceListMap = new HashMap<>();
            if (ObjectUtil.isNotEmpty(priceList)) {
                priceListMap = priceList.stream().collect(Collectors.groupingBy(o -> DateUtil.formatDate(o.getSigningMonth()) + " " + o.getTimeFrame()));
            }
            // 判断是否存在价格数据
            Map<Long, List<VppRetailContractsManageVO>> priceMap = resultList.stream().collect(Collectors.groupingBy(VppRetailContractsManageVO::getUserId));
            for (Map.Entry<Long, List<VppRetailContractsManageVO>> entry : priceMap.entrySet()) {
                Long key = entry.getKey();
                for (VppRetailContractsManageVO vppRetailContractsManageVO : priceMap.get(key)) {
                    Integer type = vppRetailContractsManageVO.getType();
                    if (type == 2 && ObjectUtil.isNull(vppRetailContractsManageVO.getVal())) {
                        String timeFrame = vppRetailContractsManageVO.getTimeFrame();
                        String priceName = vppRetailContractsManageVO.getPriceName();
                        List<VppRetailContractsPriceVO> prices = new ArrayList<>();
                        List<VppRetailContractsPriceVO> list = priceListMap.get(DateUtil.formatDate(signingMonth) + " " + timeFrame);
                        if (priceName.contains("6:4加权出清电价") && ObjectUtil.isNotEmpty(list)) {
                            prices = list.stream().filter(o -> o.getType() == 1).collect(Collectors.toList());
                        } else if (priceName.contains("现货加权出清电价")) {
                            prices = list.stream().filter(o -> o.getType() == 2).collect(Collectors.toList());
                        }
                        if (ObjectUtil.isNotEmpty(prices)) {
                            BigDecimal price = prices.get(0).getPrice();
                            BigDecimal priceDifference = vppRetailContractsManageVO.getPriceDifference();
                            vppRetailContractsManageVO.setVal(DecimalPlaceUtil.add(price, priceDifference));
                        }
                    }
                }
            }
        }

        List<VppRetailContractsManage> list = BeanUtil.copyToList(resultList, VppRetailContractsManage.class);


        LambdaQueryWrapper<VppRetailContractsManage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppRetailContractsManage::getIsDelete, 0);
        queryWrapper.eq(VppRetailContractsManage::getRetailContractsId, queryImportRecordDTO.getRetailContractsId());
        List<VppRetailContractsManage> vppRetailContractsManageList = baseMapper.selectList(queryWrapper);
        if (ObjectUtil.isNotEmpty(vppRetailContractsManageList)) {
            for (VppRetailContractsManage vppRetailContractsManage : list) {
                String name = vppRetailContractsManage.getName();
                List<VppRetailContractsManage> userData = vppRetailContractsManageList.stream().filter(o -> o.getName().equals(name)).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(userData)) {
                    vppRetailContractsManage.setDividendSharCoefficient(userData.get(0).getDividendSharCoefficient());
                }
            }
        }

        Map<String, List<VppRetailContractsManage>> collect = list.stream().collect(Collectors.groupingBy(VppRetailContractsManage::getName));
        for (String s : collect.keySet()) {
            List<VppRetailContractsManage> vppRetailContractsManages = collect.get(s);
            if (ObjectUtil.isNotEmpty(vppRetailContractsManages)) {
                Date signingMonth = vppRetailContractsManages.get(0).getSigningMonth();
                Long retailContractsId = vppRetailContractsManages.get(0).getRetailContractsId();
                Long tenantId = vppRetailContractsManages.get(0).getTenantId();
                // 删除
                baseMapper.removeData(signingMonth, retailContractsId, tenantId);
            }
        }
        saveBatch(list);
        return DataResult.success();
    }

    private void saveData(Date signingMonth) {
        //  //获取运营平台推送的数据
        OpeFdSystemRequestDTO opeFdSystemRequestDTO = new OpeFdSystemRequestDTO();
        opeFdSystemRequestDTO.setRunningTime(signingMonth);
        opeFdSystemRequestDTO.setRuleCode("retailMarketPrice");
        DataResult<Map<String, OpeFdSystemRequestVO>> mapDataResult = operationConfigurationApi.versionSystemRequest(opeFdSystemRequestDTO);
        Map<String, OpeFdSystemRequestVO> data = mapDataResult.getData();
        log.info("查询运营平台规则6:4 {}" + data);
        if (ObjectUtil.isNotNull(data) && ObjectUtil.isNotEmpty(data)) {
            OpeFdSystemRequestVO opeFdSystemRequestVO = data.get("versionInfo-spotArithAvg24");
            String[] spotArithAvg24 = new String[0];
            if (opeFdSystemRequestVO != null) {
                String spotArithAvg = opeFdSystemRequestVO.getVal();
                spotArithAvg24 = spotArithAvg.split(DATA_SPILT);
            }

            String[] spotWeightAvg24 = new String[0];
            OpeFdSystemRequestVO opeFdSystemRequestVO1 = data.get("versionInfo-spotWeightAvg24");
            if (ObjectUtil.isNotNull(opeFdSystemRequestVO1)) {
                String spotWeightAvg = opeFdSystemRequestVO1.getVal();
                spotWeightAvg24 = spotWeightAvg.split(DATA_SPILT);
            }
            List<VppRetailContractsPrice> retailContractsPrices = new ArrayList<>();
            for (int i = 0; i < VppConstant.TWENTY_FOUR_TIMES.length; i++) {
                String twentyFourTime = VppConstant.TWENTY_FOUR_TIMES[i];
                if (ObjectUtil.isNotEmpty(spotArithAvg24)) {
                    VppRetailContractsPrice vppRetailContractsPrice = new VppRetailContractsPrice();
                    String price = spotArithAvg24[i];
                    vppRetailContractsPrice.setType(VppConstant.ONE_NUMBER);
                    vppRetailContractsPrice.setSigningMonth(signingMonth);
                    vppRetailContractsPrice.setPrice(new BigDecimal(price));
                    vppRetailContractsPrice.setCreateTime(new Date());
                    vppRetailContractsPrice.setUpdateTime(new Date());
                    vppRetailContractsPrice.setTimeFrame(twentyFourTime);
                    vppRetailContractsPrice.setDataSources(2);
                    retailContractsPrices.add(vppRetailContractsPrice);
                }
                if (ObjectUtil.isNotEmpty(spotWeightAvg24)) {
                    VppRetailContractsPrice vppRetailContractsPrices = new VppRetailContractsPrice();
                    String price = spotWeightAvg24[i];
                    vppRetailContractsPrices.setType(VppConstant.TWO_NUMBER);
                    vppRetailContractsPrices.setSigningMonth(signingMonth);
                    vppRetailContractsPrices.setPrice(new BigDecimal(price));
                    vppRetailContractsPrices.setCreateTime(new Date());
                    vppRetailContractsPrices.setUpdateTime(new Date());
                    vppRetailContractsPrices.setTimeFrame(twentyFourTime);
                    vppRetailContractsPrices.setDataSources(2);
                    retailContractsPrices.add(vppRetailContractsPrices);
                }
            }
            if (ObjectUtil.isNotEmpty(retailContractsPrices)) {
                vppRetailContractsPriceService.addDataList(retailContractsPrices);
            }
        } else {
            //计算本月得上中下旬各有多少天
            Map<String, BigDecimal> priceAllMap = getAllList(signingMonth);
            //数据入库
            vppRetailContractsPriceService.addData(priceAllMap, signingMonth);
        }
    }

    private Map<String, BigDecimal> getAllList(Date signingMonth) {
        Map<String, BigDecimal> priceAllMap = new HashMap<>();
        List<VppRetailContractsPriceMapVO> vppRetailContractsPriceMapList = new ArrayList<>();
        ExcessMLDeclareDateNumVO numVO = getDecadeDays(signingMonth);
        //获取交易安排数据
        List<OpeTransactionScheduleVO> data = getOperationScheduleList(signingMonth);
        //去除新能源和年度数据
        List<OpeTransactionScheduleVO> dataList = data.stream().filter(o -> !o.getName().contains("新能源")).filter(o -> !o.getName().contains("年度")).collect(Collectors.toList());
        Map<Integer, List<OpeTransactionScheduleVO>> opeTransactionScheduleMap = dataList.stream().collect(Collectors.groupingBy(OpeTransactionScheduleVO::getCycle));
        IdListDTO idListDTO = new IdListDTO();
        idListDTO.setIdList(dataList.stream().map(OpeTransactionScheduleVO::getId).collect(Collectors.toList()));
        DataResult<List<OpeTransactionDataVO>> transactionDataList = operationTransactionScheduleApi.getTransactionDataList(idListDTO);
        List<OpeTransactionDataVO> transactionData = transactionDataList.getData();
        for (Map.Entry<Integer, List<OpeTransactionScheduleVO>> entry : opeTransactionScheduleMap.entrySet()) {
            Integer key = entry.getKey();
            Integer numDays = 0;
            if (key.equals(4)) {
                numDays = numVO.getTotalDays();
            } else if (key.equals(5)) {
                numDays = numVO.getFirstDecadeDays();
            } else if (key.equals(6)) {
                numDays = numVO.getSecondDecadeDays();
            } else if (key.equals(7)) {
                numDays = numVO.getThirdDecadeDays();
            }
            List<OpeCentralizedBiddingVO> centralizedBiddingVOList = Collections.emptyList();
            List<OpeRollingMatchingVO> opeRollingMatchingVOList = Collections.emptyList();
            for (OpeTransactionScheduleVO opeTransactionScheduleVO : opeTransactionScheduleMap.get(key)) {
                Long id = opeTransactionScheduleVO.getId();
                Integer mode = opeTransactionScheduleVO.getMode();
                List<OpeTransactionDataVO> opeTransactionDataList = transactionData.stream().filter(o -> o.getDetailId().equals(id)).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(opeTransactionDataList)) {
                    if (mode.equals(1)) {
                        centralizedBiddingVOList = opeTransactionDataList.get(0).getCentralizedBiddingVOList();
                    } else if (mode.equals(2)) {
                        opeRollingMatchingVOList = opeTransactionDataList.get(0).getOpeRollingMatchingVOList();
                    }
                }
            }
            //数据插入同一个List中
            for (String twentyFourTime : VppConstant.TWENTY_FOUR_TIMES) {
                List<VppRetailContractsPriceMapVO> finalList = vppRetailContractsPriceMapList.stream().filter(o -> o.getTimeFrame().equals(twentyFourTime)).collect(Collectors.toList());
                VppRetailContractsPriceMapVO vppRetailContractsPriceMapVO = new VppRetailContractsPriceMapVO();
                vppRetailContractsPriceMapVO.setSigningMonth(signingMonth);
                vppRetailContractsPriceMapVO.setTimeFrame(twentyFourTime);
                List<OpeCentralizedBiddingVO> biddList = centralizedBiddingVOList.stream().filter(o -> o.getTimeType().split("-")[1].equals(twentyFourTime)).collect(Collectors.toList());
                List<OpeRollingMatchingVO> matchList = opeRollingMatchingVOList.stream().filter(o -> o.getTimeType().split("-")[1].equals(twentyFourTime)).collect(Collectors.toList());
                BigDecimal allData = BigDecimal.ZERO;
                BigDecimal clinchDealTotal = BigDecimal.ZERO;
                if (ObjectUtil.isNotEmpty(biddList)) {
                    BigDecimal clearingPrice = biddList.get(0).getClearingPrice();
                    BigDecimal clinchDeal = biddList.get(0).getClinchDeal();
                    BigDecimal clinchDeals = DecimalPlaceUtil.multiply(clinchDeal, new BigDecimal(numDays), DecimalPlaceEnum.TRANSITION);
                    allData = DecimalPlaceUtil.add(allData, DecimalPlaceUtil.multiply(clearingPrice, clinchDeals, DecimalPlaceEnum.TRANSITION));
                    clinchDealTotal = DecimalPlaceUtil.add(clinchDealTotal, clinchDeals);
                }
                if (ObjectUtil.isNotEmpty(matchList)) {
                    BigDecimal clearingPrice = matchList.get(0).getWeightedPrice();
                    BigDecimal clinchDeal = matchList.get(0).getVolumeBusiness();
                    BigDecimal clinchDeals = DecimalPlaceUtil.multiply(clinchDeal, new BigDecimal(numDays), DecimalPlaceEnum.TRANSITION);
                    allData = DecimalPlaceUtil.add(allData, DecimalPlaceUtil.multiply(clearingPrice, clinchDeals, DecimalPlaceEnum.TRANSITION));
                    clinchDealTotal = DecimalPlaceUtil.add(clinchDealTotal, clinchDeals);
                }
                BigDecimal price = DecimalPlaceUtil.divide(allData, clinchDealTotal, DecimalPlaceEnum.TRANSITION);

                if (ObjectUtil.isNotEmpty(finalList)) {
                    VppRetailContractsPriceMapVO vppRetailContractsPrice = finalList.get(0);
                    vppRetailContractsPriceMapList.remove(vppRetailContractsPrice);
                    updateContractPrice(vppRetailContractsPrice, key, numDays, price, clinchDealTotal);
                    vppRetailContractsPriceMapList.add(vppRetailContractsPrice);
                } else {
                    updateContractPrice(vppRetailContractsPriceMapVO, key, numDays, price, clinchDealTotal);
                    vppRetailContractsPriceMapList.add(vppRetailContractsPriceMapVO);
                }
            }
        }

        //计算月旬日历天出清价格
        for (VppRetailContractsPriceMapVO vppRetailContractsPriceMapVO : vppRetailContractsPriceMapList) {
            //计算成交电价*电量*天数
            BigDecimal monthly = calculateTotal(vppRetailContractsPriceMapVO.getMonthlyPrice(), vppRetailContractsPriceMapVO.getMonthlyClinchDeal(), vppRetailContractsPriceMapVO.getTotalDays());
            BigDecimal first = calculateTotal(vppRetailContractsPriceMapVO.getFirstDecadePrice(), vppRetailContractsPriceMapVO.getFirstDecadeClinchDeal(), vppRetailContractsPriceMapVO.getFirstDecadeDays());
            BigDecimal second = calculateTotal(vppRetailContractsPriceMapVO.getSecondDecadePrice(), vppRetailContractsPriceMapVO.getSecondDecadeClinchDeal(), vppRetailContractsPriceMapVO.getSecondDecadeDays());
            BigDecimal third = calculateTotal(vppRetailContractsPriceMapVO.getThirdDecadePrice(), vppRetailContractsPriceMapVO.getThirdDecadeClinchDeal(), vppRetailContractsPriceMapVO.getThirdDecadeDays());
            BigDecimal all = monthly.add(first).add(second).add(third);

            //计算成交电量*天数
            BigDecimal monthlyDeal = calculateDeal(vppRetailContractsPriceMapVO.getMonthlyClinchDeal(), vppRetailContractsPriceMapVO.getTotalDays());
            BigDecimal firstDeal = calculateDeal(vppRetailContractsPriceMapVO.getFirstDecadeClinchDeal(), vppRetailContractsPriceMapVO.getFirstDecadeDays());
            BigDecimal secondDeal = calculateDeal(vppRetailContractsPriceMapVO.getSecondDecadeClinchDeal(), vppRetailContractsPriceMapVO.getSecondDecadeDays());
            BigDecimal thirdDeal = calculateDeal(vppRetailContractsPriceMapVO.getThirdDecadeClinchDeal(), vppRetailContractsPriceMapVO.getThirdDecadeDays());
            BigDecimal add = monthlyDeal.add(firstDeal).add(secondDeal).add(thirdDeal);

            BigDecimal price = DecimalPlaceUtil.divide(all, add, DecimalPlaceEnum.COMPUTE);
            if (ObjectUtil.isNotNull(price)) {
                price = price.setScale(2, RoundingMode.HALF_UP);
            }
            priceAllMap.put(vppRetailContractsPriceMapVO.getTimeFrame(), price);
        }
        //map排序
        Map<String, BigDecimal> finalMap = getStringBigDecimalMap(priceAllMap);


        //计算年度数据价格
        List<OpeTransactionScheduleVO> bilateralDataList = data.stream().filter(o -> !o.getName().contains("新能源")).filter(o -> o.getCycle().equals(1) && o.getMode().equals(3)).collect(Collectors.toList());
        PublicTradeQueryDTO publicTradeQueryDTO = new PublicTradeQueryDTO();
        publicTradeQueryDTO.setArrangeIdList(bilateralDataList.stream().map(OpeTransactionScheduleVO::getId).collect(Collectors.toList()));
        publicTradeQueryDTO.setSearchTargetStartDate(DateUtil.beginOfYear(signingMonth));
        publicTradeQueryDTO.setSearchTargetEndDate(DateUtil.endOfYear(signingMonth));
        DataResult<LongTermMarketResultVO> bilateralData = publicDataApi.getDealChart(publicTradeQueryDTO);
        BigDecimal averageDealPrice = bilateralData.getData().getAverageDealPrice();
        BigDecimal totalDealQuantity = bilateralData.getData().getTotalDealQuantity();

        //总数
        BigDecimal totalCost = BigDecimal.ZERO;
        BigDecimal monthDealQuantity = BigDecimal.ZERO;
        //处理年度双边数据
        if (ObjectUtil.isNotNull(averageDealPrice) && ObjectUtil.isNotNull(totalDealQuantity)) {
            BigDecimal quantity = DecimalPlaceUtil.multiply(DecimalPlaceUtil.divide(totalDealQuantity, new BigDecimal(365), DecimalPlaceEnum.COMPUTE), new BigDecimal(numVO.getTotalDays()), DecimalPlaceEnum.COMPUTE);
            BigDecimal cost = DecimalPlaceUtil.multiply(quantity, averageDealPrice, DecimalPlaceEnum.COMPUTE);
            totalCost = DecimalPlaceUtil.add(totalCost, cost, DecimalPlaceEnum.COMPUTE);
            monthDealQuantity = DecimalPlaceUtil.add(monthDealQuantity, quantity, DecimalPlaceEnum.COMPUTE);
        }
        //处理年度数据
        List<OpeTransactionScheduleVO> yearDataList = data.stream().filter(o -> !o.getName().contains("新能源")).filter(o -> o.getCycle().equals(1) && o.getMode().equals(1)).collect(Collectors.toList());
        publicTradeQueryDTO.setArrangeIdList(yearDataList.stream().map(OpeTransactionScheduleVO::getId).collect(Collectors.toList()));
        DataResult<LongTermMarketResultVO> yearData = publicDataApi.getDealChart(publicTradeQueryDTO);
        BigDecimal averageDealPriceYear = yearData.getData().getAverageDealPrice();
        BigDecimal totalDealQuantityYear = yearData.getData().getTotalDealQuantity();
        if (ObjectUtil.isNotNull(averageDealPriceYear) && ObjectUtil.isNotNull(totalDealQuantityYear)) {
            BigDecimal quantity = DecimalPlaceUtil.multiply(DecimalPlaceUtil.divide(totalDealQuantityYear, new BigDecimal(365), DecimalPlaceEnum.COMPUTE), new BigDecimal(numVO.getTotalDays()), DecimalPlaceEnum.COMPUTE);
            BigDecimal cost = DecimalPlaceUtil.multiply(quantity, averageDealPriceYear, DecimalPlaceEnum.COMPUTE);
            totalCost = DecimalPlaceUtil.add(totalCost, cost, DecimalPlaceEnum.COMPUTE);
            monthDealQuantity = DecimalPlaceUtil.add(monthDealQuantity, quantity, DecimalPlaceEnum.COMPUTE);
        }
        BigDecimal monthDealQuantityHour = DecimalPlaceUtil.divide(monthDealQuantity, new BigDecimal("24"), DecimalPlaceEnum.COMPUTE);
        //获取月度集中竞价原数据
        Map<String, BigDecimal> mapYears = new LinkedHashMap<>();

        List<OpeTransactionScheduleVO> opeTransactionScheduleList = data.stream().filter(o -> o.getMode().equals(1) && o.getCycle().equals(4)).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(opeTransactionScheduleList)) {
            Long id = opeTransactionScheduleList.get(0).getId();
            List<OpeTransactionDataVO> OpeTransactionDataList = transactionData.stream().filter(o -> o.getDetailId().equals(id)).collect(Collectors.toList());
            if (ObjectUtil.isNotNull(OpeTransactionDataList) && ObjectUtil.isNotEmpty(OpeTransactionDataList)) {
                List<OpeCentralizedBiddingVO> centralizedBiddingVOList = OpeTransactionDataList.get(0).getCentralizedBiddingVOList();
                BigDecimal clearPrice = centralizedBiddingVOList.stream().map(OpeCentralizedBiddingVO::getClearingPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                for (OpeCentralizedBiddingVO opeCentralizedBiddingVO : centralizedBiddingVOList) {
                    String timeType = opeCentralizedBiddingVO.getTimeType();
                    BigDecimal clearingPrice = opeCentralizedBiddingVO.getClearingPrice();
                    BigDecimal multiply = DecimalPlaceUtil.multiply(DecimalPlaceUtil.divide(clearingPrice, clearPrice, DecimalPlaceEnum.COMPUTE), totalCost, DecimalPlaceEnum.COMPUTE);
                    BigDecimal price = DecimalPlaceUtil.divide(multiply, monthDealQuantityHour, DecimalPlaceEnum.COMPUTE);
                    if (ObjectUtil.isNotNull(price) && ObjectUtil.isNotNull(timeType)) {
                        mapYears.put(timeType.split("-")[1], price.setScale(BigDecimal.ROUND_CEILING, RoundingMode.HALF_UP));
                    }
                }
            }
        }
        //map排序
        Map<String, BigDecimal> finalMapYear = getStringBigDecimalMap(mapYears);
        //计算最终价格
        Map<String, BigDecimal> resultMap = new LinkedHashMap<>();
        for (String twentyFourTime : VppConstant.TWENTY_FOUR_TIMES) {
            BigDecimal priceYear = finalMapYear.get(twentyFourTime);
            BigDecimal priceMonth = finalMap.get(twentyFourTime);

            BigDecimal yearPrice = DecimalPlaceUtil.multiply(priceYear, new BigDecimal("0.6"), DecimalPlaceEnum.COMPUTE);
            BigDecimal monthPrice = DecimalPlaceUtil.multiply(priceMonth, new BigDecimal("0.4"), DecimalPlaceEnum.COMPUTE);
            BigDecimal price = DecimalPlaceUtil.add(yearPrice, monthPrice, DecimalPlaceEnum.PRICE);
            resultMap.put(twentyFourTime, price);
        }
        return resultMap;
    }

    /**
     * map排序
     *
     * @param priceAllMap
     * @return
     */
    private static Map<String, BigDecimal> getStringBigDecimalMap(Map<String, BigDecimal> priceAllMap) {
        return priceAllMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
    }

    /**
     * 公共方法：计算总价
     */
    private BigDecimal calculateTotal(BigDecimal price, BigDecimal clinchDeal, Integer days) {
        if (price == null || clinchDeal == null || days == null) {
            return BigDecimal.ZERO;
        }
        return DecimalPlaceUtil.multiply(
                DecimalPlaceUtil.multiply(price, clinchDeal, DecimalPlaceEnum.COMPUTE),
                BigDecimal.valueOf(days),
                DecimalPlaceEnum.COMPUTE
        );
    }

    /**
     * 公共方法：计算成交额
     */
    private BigDecimal calculateDeal(BigDecimal clinchDeal, Integer days) {
        if (clinchDeal == null || days == null) {
            return BigDecimal.ZERO;
        }
        return DecimalPlaceUtil.multiply(clinchDeal, BigDecimal.valueOf(days), DecimalPlaceEnum.COMPUTE);
    }


    /**
     * 更新合同价格的公共方法
     */
    private void updateContractPrice(VppRetailContractsPriceMapVO contractPrice, Integer key, int numDays, BigDecimal price, BigDecimal clinchDealTotal) {
        switch (key) {
            case 4:
                contractPrice.setTotalDays(numDays);
                contractPrice.setMonthlyPrice(price);
                contractPrice.setMonthlyClinchDeal(clinchDealTotal);
                break;
            case 5:
                contractPrice.setFirstDecadeDays(numDays);
                contractPrice.setFirstDecadePrice(price);
                contractPrice.setFirstDecadeClinchDeal(clinchDealTotal);
                break;
            case 6:
                contractPrice.setSecondDecadeDays(numDays);
                contractPrice.setSecondDecadePrice(price);
                contractPrice.setSecondDecadeClinchDeal(clinchDealTotal);
                break;
            case 7:
                contractPrice.setThirdDecadeDays(numDays);
                contractPrice.setThirdDecadePrice(price);
                contractPrice.setThirdDecadeClinchDeal(clinchDealTotal);
                break;
        }
    }


    private List<OpeTransactionScheduleVO> getOperationScheduleList(Date signingMonth) {
        OpeTransactionScheduleSystemDTO opeTransactionScheduleSystemDTO = new OpeTransactionScheduleSystemDTO();
        opeTransactionScheduleSystemDTO.setSearchTargetStartDate(DateUtil.beginOfMonth(signingMonth));
        opeTransactionScheduleSystemDTO.setSearchTargetEndDate(DateUtil.endOfMonth(signingMonth));
        List<Integer> searchModes = new ArrayList<>();
        searchModes.add(1);
        searchModes.add(2);
        searchModes.add(3);
        opeTransactionScheduleSystemDTO.setSearchModes(searchModes);
        List<Integer> searchCycles = new ArrayList<>();
        searchCycles.add(1);
        searchCycles.add(4);
        searchCycles.add(5);
        searchCycles.add(6);
        searchCycles.add(7);
        opeTransactionScheduleSystemDTO.setSearchCycles(searchCycles);
        opeTransactionScheduleSystemDTO.setProvince(1L);
        opeTransactionScheduleSystemDTO.setSearchContractType(1);
        DataResult<List<OpeTransactionScheduleVO>> transactionSchedule = operationTransactionScheduleApi.getTransactionSchedule(opeTransactionScheduleSystemDTO);
        List<OpeTransactionScheduleVO> data = transactionSchedule.getData();
        return data;
    }


    @Override
    public DataResult<FxPage<VppRetailContractsManageListVO>> getList(QueryVppRetailContractsDTO param) {
        List<VppRetailContractsManageListVO> list = baseMapper.getList(param);
        //计算预估结算电价（元/MWh）
        for (VppRetailContractsManageListVO vo : list) {
            //获取用户的全部电量 电价
            LambdaQueryWrapper<VppRetailContractsManage> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(VppRetailContractsManage::getUserId, vo.getUserId());
            queryWrapper.eq(VppRetailContractsManage::getSigningMonth, vo.getSigningMonth());
            queryWrapper.eq(VppRetailContractsManage::getName, vo.getName());
            queryWrapper.eq(VppRetailContractsManage::getIsDelete, 0);
            queryWrapper.orderByDesc(VppRetailContractsManage::getSigningMonth);
            List<VppRetailContractsManage> vppRetailContractsManages = baseMapper.selectList(queryWrapper);
            Result result = getResult(vppRetailContractsManages);

            List<VppRetailContractsManage> priceList = vppRetailContractsManages.stream().filter(o -> o.getType().equals(2)).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(priceList)) {
                BigDecimal priceDifference = priceList.get(0).getPriceDifference();
                if (ObjectUtil.isNotNull(priceDifference)) {
                    vo.setPriceDifference(priceDifference.setScale(DecimalPlaceEnum.PRICE.length, RoundingMode.HALF_UP));
                }
            }

            if (!result.powerMap.isEmpty()) {
                BigDecimal totalPower = BigDecimal.ZERO;
                BigDecimal total = BigDecimal.ZERO;
                for (String s : result.powerMap.keySet()) {
                    //电量
                    BigDecimal power = result.powerMap.get(s);
                    //电价
                    BigDecimal powerPrice = result.powerPriceMap.get(s);

                    total = DecimalPlaceUtil.add(total, DecimalPlaceUtil.multiply(power, powerPrice, DecimalPlaceEnum.LOAD));

                    totalPower = DecimalPlaceUtil.add(totalPower, power);
                }
                BigDecimal price = DecimalPlaceUtil.divide(total, totalPower, DecimalPlaceEnum.PRICE);
                vo.setPrice(price);
            }

        }
        if (ObjectUtil.isNotNull(param.getType())) {
            if (param.getType().equals(1)) {
                list = list.stream().filter(o -> o.getDividendSharCoefficient() == null).collect(Collectors.toList());
            } else if (param.getType().equals(2)) {
                list = list.stream().filter(o -> o.getDividendSharCoefficient() != null).collect(Collectors.toList());
            }
        }
        FxPage fxPage = this.ListPage(param, list);
        return DataResult.success(fxPage);
    }

    public FxPage ListPage(QueryVppRetailContractsDTO param, List data) {
        Integer pageNum = param.getPage();
        Integer pageSize = param.getPageSize();
        // 记录总数
        Integer count = data.size();
        // 页数
        int pageCount;
        if (count % pageSize == 0) {
            pageCount = count / pageSize;
        } else {
            pageCount = count / pageSize + 1;
        }
        // 开始索引
        int fromIndex;
        // 结束索引
        int toIndex;
        if (!pageNum.equals(pageCount)) {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = fromIndex + pageSize;
        } else {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = count;
        }
        List pageList = new ArrayList<>();
        if (data.size() != 0) {
            pageList = data.subList(fromIndex, toIndex);
        }
        return FxPage.page(pageList, count, pageNum, pageSize);
    }


    @Override
    public DataResult<VppRetailContractsManageDetailVO> retailContractsManageDetail(RetailContractsManageDetailDTO retailContractsManageDetailDTO) {
        VppRetailContractsManageDetailVO vppRetailContractsManageDetailVO = new VppRetailContractsManageDetailVO();
        //获取用户的全部电量 电价
        LambdaQueryWrapper<VppRetailContractsManage> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(VppRetailContractsManage::getUserId, retailContractsManageDetailDTO.getUserId());
        queryWrapper.eq(VppRetailContractsManage::getRetailContractsId, retailContractsManageDetailDTO.getRetailContractsId());
        queryWrapper.eq(VppRetailContractsManage::getSigningMonth, retailContractsManageDetailDTO.getSigningMonth());
        queryWrapper.eq(VppRetailContractsManage::getName, retailContractsManageDetailDTO.getName());
        queryWrapper.eq(VppRetailContractsManage::getIsDelete, 0);
        List<VppRetailContractsManage> vppRetailContractsManages = baseMapper.retailContractsManageDetail(queryWrapper);
        if (vppRetailContractsManages.size() > 0) {
            BigDecimal dividendSharCoefficient = vppRetailContractsManages.get(0).getDividendSharCoefficient();
            Result result = getResult(vppRetailContractsManages);
            if (result.powerMap.size() > 0) {
                BigDecimal totalPower = BigDecimal.ZERO;
                BigDecimal total = BigDecimal.ZERO;
                for (String s : result.powerMap.keySet()) {
                    //电量
                    BigDecimal power = result.powerMap.get(s);
                    //电价
                    BigDecimal powerPrice = result.powerPriceMap.get(s);
                    total = total.add(power.multiply(powerPrice));
                    totalPower = totalPower.add(power);
                }
                BigDecimal price = total.divide(totalPower, BigDecimal.ROUND_CEILING, BigDecimal.ROUND_HALF_UP);
                vppRetailContractsManageDetailVO.setPrice(price);
                if (ObjectUtil.isNotNull(dividendSharCoefficient)) {
                    vppRetailContractsManageDetailVO.setDividendSharCoefficient(dividendSharCoefficient.setScale(BigDecimal.ROUND_CEILING, BigDecimal.ROUND_UP));
                }
            }
        }

        Map<String, List<VppRetailContractsManage>> map = vppRetailContractsManages.stream().collect(Collectors.groupingBy(VppRetailContractsManage::getTimeFrame));
        Map<String, List<VppRetailContractsManage>> resultMap = new LinkedHashMap<>();
        map.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEachOrdered(x -> resultMap.put(x.getKey(), x.getValue()));

        List<String> timeFrameList = new ArrayList<>(resultMap.keySet());

        List<BigDecimal> powerList = new ArrayList<>();
        List<BigDecimal> priceList = new ArrayList<>();
        for (Map.Entry<String, List<VppRetailContractsManage>> entry : resultMap.entrySet()) {
            for (VppRetailContractsManage item : entry.getValue()) {
                if (item.getType().equals(VppConstant.ONE_NUMBER)) {
                    powerList.add(item.getVal());
                } else if (item.getType().equals(VppConstant.TWO_NUMBER)) {
                    BigDecimal val = item.getVal();
                    priceList.add(val.setScale(BigDecimal.ROUND_CEILING, BigDecimal.ROUND_UP));
                }
            }
        }
        List<String> list = convertTimeFrames(timeFrameList);
        if (list.size() > VppConstant.TWO_NUMBER) {
            list = list.subList(0, list.size() - VppConstant.TWO_NUMBER);
            // 添加开头的"00:00-01:00"
            list.add(VppConstant.ZERO_NUMBER, "00:00-01:00");
            // 添加结尾的"23:00-24:00"
            list.add("23:00-24:00");
        }
        vppRetailContractsManageDetailVO.setTimeFrame(timeFrameList);
        vppRetailContractsManageDetailVO.setIntervalTimeFrame(list);
        vppRetailContractsManageDetailVO.setPowerList(powerList);
        vppRetailContractsManageDetailVO.setPriceList(priceList);
        return DataResult.success(vppRetailContractsManageDetailVO);
    }

    @Override
    public List<VppRetailContractsManage> getUserPrice(Long tenantId, Long userId, Date date) {
        //获取用户的全部电量 电价
        LambdaQueryWrapper<VppRetailContractsManage> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(VppRetailContractsManage::getUserId, userId);
        queryWrapper.eq(VppRetailContractsManage::getSigningMonth, DateUtil.beginOfMonth(date));
        queryWrapper.eq(VppRetailContractsManage::getIsDelete, 0);
        queryWrapper.eq(VppRetailContractsManage::getType, 2);
        return baseMapper.retailContractsManageDetail(queryWrapper);
    }

    @Override
    public List<VppRetailContractsManage> getUserListPrice(Long tenantId, List<VppUserDTO> userList, Date date) {
        //获取用户的全部电量 电价
        List<Long> userIdList = userList.stream().map(VppUserDTO::getId).collect(Collectors.toList());
        return this.getUserListPrice(userIdList, date);
    }

    @Override
    public List<VppRetailContractsManage> getUserListPrice(List<Long> userIdList, Date date) {
        //获取用户的全部电量 电价
        LambdaQueryWrapper<VppRetailContractsManage> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(VppRetailContractsManage::getUserId, userIdList);
        queryWrapper.eq(VppRetailContractsManage::getSigningMonth, DateUtil.beginOfMonth(date));
        queryWrapper.eq(VppRetailContractsManage::getIsDelete, 0);
        queryWrapper.eq(VppRetailContractsManage::getType, 2);
        return baseMapper.retailContractsManageDetail(queryWrapper);
    }

    @Override
    public List<VppRetailContractsManage> getUserDividendList(VppUserDTO.SearchDTO param) {
        return baseMapper.getUserDividendList(param);
    }

    @Override
    public List<VppRetailContractsManageListVO> getAllList(QueryVppRetailContractsDTO param) {
        List<VppRetailContractsManageListVO> list = baseMapper.getList(param);
        //计算预估结算电价（元/MWh）
        for (VppRetailContractsManageListVO vo : list) {
            //获取用户的全部电量 电价
            LambdaQueryWrapper<VppRetailContractsManage> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(VppRetailContractsManage::getUserId, vo.getUserId());
            queryWrapper.eq(VppRetailContractsManage::getSigningMonth, vo.getSigningMonth());
            queryWrapper.eq(VppRetailContractsManage::getName, vo.getName());
            queryWrapper.eq(VppRetailContractsManage::getIsDelete, 0);
            queryWrapper.orderByDesc(VppRetailContractsManage::getSigningMonth);
            List<VppRetailContractsManage> vppRetailContractsManages = baseMapper.selectList(queryWrapper);
            Result result = getResult(vppRetailContractsManages);

            if (result.powerMap.size() > 0) {
                BigDecimal totalPower = BigDecimal.ZERO;
                BigDecimal total = BigDecimal.ZERO;
                for (String s : result.powerMap.keySet()) {
                    //电量
                    BigDecimal power = result.powerMap.get(s);
                    //电价
                    BigDecimal powerPrice = result.powerPriceMap.get(s);

                    total = DecimalPlaceUtil.add(total, DecimalPlaceUtil.multiply(power, powerPrice, DecimalPlaceEnum.LOAD));

                    totalPower = DecimalPlaceUtil.add(totalPower, power);
                }
                BigDecimal price = DecimalPlaceUtil.divide(total, totalPower, DecimalPlaceEnum.PRICE);
                vo.setPrice(price);
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataResult<Void> updateDividendCoefficient(UpdateRetailContractsManageDTO param) {
        List<UpdateRetailContractsManageDTO.RetailContractsManageDTO> list = param.getList();
        if (ObjectUtil.isNotEmpty(list)) {
            Map<String, List<UpdateRetailContractsManageDTO.RetailContractsManageDTO>> collect = list.stream().collect(Collectors.groupingBy(o -> DateUtil.formatDate(DateUtil.beginOfMonth(o.getSigningMonth()))));
            for (Map.Entry<String, List<UpdateRetailContractsManageDTO.RetailContractsManageDTO>> entrys : collect.entrySet()) {
                String key = entrys.getKey();
                List<UpdateRetailContractsManageDTO.RetailContractsManageDTO> retailContractsManageDTOS1 = collect.get(key);
                //获取数据
                LambdaQueryWrapper<VppRetailContractsManage> queryWrapper = new LambdaQueryWrapper();
                queryWrapper.eq(VppRetailContractsManage::getSigningMonth, DateUtil.parseDate(key));
                queryWrapper.eq(VppRetailContractsManage::getIsDelete, 0);
                queryWrapper.eq(VppRetailContractsManage::getTenantId, param.getTenantId());
                List<VppRetailContractsManage> vppRetailContractsManages = baseMapper.selectList(queryWrapper);

                List<VppRetailContractsManage> resultList = new ArrayList<>();

                Map<Long, List<UpdateRetailContractsManageDTO.RetailContractsManageDTO>> userMap = retailContractsManageDTOS1.stream().collect(Collectors.groupingBy(UpdateRetailContractsManageDTO.RetailContractsManageDTO::getUserId));
                Map<Long, List<VppRetailContractsManage>> map = vppRetailContractsManages.stream().collect(Collectors.groupingBy(VppRetailContractsManage::getUserId));
                for (Map.Entry<Long, List<VppRetailContractsManage>> entry : map.entrySet()) {
                    Long userId = entry.getKey();
                    List<UpdateRetailContractsManageDTO.RetailContractsManageDTO> retailContractsManageDTOS = userMap.get(userId);
                    List<VppRetailContractsManage> vppRetailContractsManagesList = map.get(userId);
                    if (ObjectUtil.isNotEmpty(retailContractsManageDTOS)) {
                        for (VppRetailContractsManage manage : vppRetailContractsManagesList) {
                            manage.setDividendSharCoefficient(retailContractsManageDTOS.get(0).getDividendSharCoefficient());
                        }
                    }
                    resultList.addAll(vppRetailContractsManagesList);
                }
                //删除之前的数据 添加
                if (ObjectUtil.isNotEmpty(resultList.size())) {
                    Date signingMonth = DateUtil.parseDate(key);
                    Long retailContractsId = resultList.get(0).getRetailContractsId();
                    Long tenantId = resultList.get(0).getTenantId();
                    //删除
                    baseMapper.removeData(signingMonth, retailContractsId, tenantId);
                }
                saveBatch(resultList);
            }


        }
        return DataResult.success();
    }

    @Override
    public DataResult<List<VppRetailContractsManageListVO>> exportManageList(QueryVppRetailContractsDTO param) {
        List<VppRetailContractsManageListVO> list = baseMapper.getList(param);
        //计算预估结算电价（元/MWh）
        for (VppRetailContractsManageListVO vo : list) {
            //获取用户的全部电量 电价
            LambdaQueryWrapper<VppRetailContractsManage> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(VppRetailContractsManage::getUserId, vo.getUserId());
            queryWrapper.eq(VppRetailContractsManage::getSigningMonth, vo.getSigningMonth());
            queryWrapper.eq(VppRetailContractsManage::getName, vo.getName());
            queryWrapper.eq(VppRetailContractsManage::getIsDelete, 0);
            queryWrapper.orderByDesc(VppRetailContractsManage::getSigningMonth);
            List<VppRetailContractsManage> vppRetailContractsManages = baseMapper.selectList(queryWrapper);
            Result result = getResult(vppRetailContractsManages);

            if (result.powerMap.size() > 0) {
                BigDecimal totalPower = BigDecimal.ZERO;
                BigDecimal total = BigDecimal.ZERO;
                for (String s : result.powerMap.keySet()) {
                    //电量
                    BigDecimal power = result.powerMap.get(s);
                    //电价
                    BigDecimal powerPrice = result.powerPriceMap.get(s);

                    total = DecimalPlaceUtil.add(total, DecimalPlaceUtil.multiply(power, powerPrice, DecimalPlaceEnum.LOAD));

                    //   total = total.add(power.multiply(powerPrice));
                    totalPower = DecimalPlaceUtil.add(totalPower, power);
                }
                BigDecimal price = DecimalPlaceUtil.divide(total, totalPower, DecimalPlaceEnum.PRICE);
                //   total.divide(totalPower, BigDecimal.ROUND_CEILING, BigDecimal.ROUND_HALF_UP);
                vo.setPrice(price);
                vo.setMonth(DateUtil.format(vo.getSigningMonth(), "yyyy-MM"));
            }

        }
        if (ObjectUtil.isNotNull(param.getType())) {
            if (param.getType().equals(1)) {
                list = list.stream().filter(o -> o.getDividendSharCoefficient() == null).collect(Collectors.toList());
            } else if (param.getType().equals(2)) {
                list = list.stream().filter(o -> o.getDividendSharCoefficient() != null).collect(Collectors.toList());
            }
        }
        return DataResult.success(list);
    }


    private static Result getResult(List<VppRetailContractsManage> vppRetailContractsManages) {
        Map<Integer, List<VppRetailContractsManage>> map = vppRetailContractsManages.stream().collect(Collectors.groupingBy(VppRetailContractsManage::getType));
        //获取电量
        List<VppRetailContractsManage> powerList = new ArrayList<>();
        if (ObjectUtil.isNotNull(map.get(VppConstant.ONE_NUMBER))) {
            powerList = map.get(VppConstant.ONE_NUMBER);
        }
        //获取电价
        List<VppRetailContractsManage> powerPriceList = new ArrayList<>();
        if (ObjectUtil.isNotNull(map.get(VppConstant.TWO_NUMBER))) {
            powerPriceList = map.get(VppConstant.TWO_NUMBER);
        }
        //计算预估结算电价
        Map<String, BigDecimal> powerMap = powerList.stream().collect(Collectors.toMap(VppRetailContractsManage::getTimeFrame, VppRetailContractsManage::getVal));
        Map<String, BigDecimal> powerPriceMap = powerPriceList.stream().collect(Collectors.toMap(VppRetailContractsManage::getTimeFrame, VppRetailContractsManage::getVal));
        powerPriceMap.replaceAll((k, v) -> v.setScale(2, BigDecimal.ROUND_HALF_UP));
        Result result = new Result(powerMap, powerPriceMap);
        return result;
    }

    private static class Result {
        public final Map<String, BigDecimal> powerMap;
        public final Map<String, BigDecimal> powerPriceMap;

        public Result(Map<String, BigDecimal> powerMap, Map<String, BigDecimal> powerPriceMap) {
            this.powerMap = powerMap;
            this.powerPriceMap = powerPriceMap;
        }
    }

    public static List<String> convertTimeFrames(List<String> timeFrames) {
        List<String> convertedTimeFrames = new ArrayList<>();

        for (String timeFrame : timeFrames) {
            String startTime = timeFrame;
            String endTime = incrementTime(timeFrame);

            String convertedTimeFrame = startTime + "-" + endTime;
            convertedTimeFrames.add(convertedTimeFrame);
        }

        return convertedTimeFrames;
    }

    public static String incrementTime(String time) {
        int hours = Integer.parseInt(time.substring(0, 2));
        int incrementedHours = (hours + 1) % 24;
        String incrementedTime = String.format("%02d", incrementedHours) + ":00";
        return incrementedTime;
    }
}
