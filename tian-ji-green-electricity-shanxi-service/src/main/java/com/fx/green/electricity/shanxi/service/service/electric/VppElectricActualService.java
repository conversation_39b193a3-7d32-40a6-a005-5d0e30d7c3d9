package com.fx.green.electricity.shanxi.service.service.electric;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.excel.vo.ImportExcelDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.*;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActual;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricQuantity;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 实际用电量数据 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppElectricActualService extends IService<VppElectricActual> {
    /**
     * 实际申报电量数据
     *
     * @param vppElectricActualDTOList 实际电量数据
     * @param vppElectricQuantity      电量数据
     * @return 导入结果
     */
    ImportExcelDetailVO importVppElectricActualList(List<VppElectricActualDTO> vppElectricActualDTOList, VppElectricQuantity vppElectricQuantity);

    /**
     * 删除表旧数据
     *
     * @param electricId 电量id
     */
    void deleteByElectricId(Long electricId);

    /**
     * 批量插入实际用电量
     *
     * @param eleList 实际电量列表
     */
    void insertBatch(List<VppElectricActual> eleList);

    /**
     * 获取用户的实际用电量(某一天)
     *
     * @param param 分析DTO
     * @param dateDay 日期
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 实际用电量列表
     */
    List<VppBiddingClearVO.ResolveLoad> getList(ResultAnalysisDTO param, Date dateDay, String startTime, String endTime);

    /**
     * 获取用户的实际用电量(时间段)
     *
     * @param param 分析DTO 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 实际用电量列表
     */
    List<VppElectricActualVO> getUserRealityPower(ResultAnalysisDTO param, String startTime, String endTime);

    /**
     * 获取实时电量数据 通过用户编码 和 日期 和 时间聚合
     *
     * @param param 电量数据参数
     * @return 实时电量数据列表
     */
    List<VppElectricActualSimpleVO> electricListByDateTimeUser(VppBatteryInformationDTO param);

    /**
     * 获取时间段内的全部数据
     *
     * @param param 分析DTO
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 实际用电量列表
     */
    List<VppElectricActualVO> electricActualList(ResultAnalysisDTO param, String startTime, String endTime);

    /**
     * 获取时间段内的数据
     *
     * @param param 分析DTO
     * @param userId 用户ID
     * @return 实际用电量列表
     */
    List<PieChartOfUserVO.RatioVO> getAllInfo(ControllableLoadDTO param, Long userId, List<Long> userIdList);

    /**
     * 获取时段的电量总和
     *
     * @param param 分析DTO
     * @return 电量总和
     */
    BigDecimal getAllPower(ControllableLoadDTO param, List<Long> userIdList);

    /**
     * 获取用户的实际用电量
     *
     * @param userId 用户ID
     * @param monthStart 开始日期
     * @param monthEnd 结束日期
     * @return 实际用电量列表
     */
    List<VppElectricActualForUserVO> getElectricActual(Long userId, Date monthStart, Date monthEnd);


    /**
     * 获取时间段内的全部实际用电量  多个用户  可以分时段
     *
     * @param param 分析DTO
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 实际用电量列表
     */
    List<VppElectricActualVO> electricActualAllList(ResultAnalysisDTO param, List<Long> userId, String startTime, String endTime);

    /**
     * 通过tenantId 获取某一天的全部数据
     *
     * @param resultAnalysisDTO 分析DTO
     * @return 实际用电量列表
     */
    List<DividendVO.UserElectricSqlVO> electricActualAll(ResultAnalysisDTO resultAnalysisDTO);

    /**
     * 获取实际用电量更新时间
     *
     * @param param 获取更新时间DTO
     * @return 更新时间
     */
    String getUpdateTime(GetUpdateTimeDTO param);

    /**
     * 获取多天的用户的实际用电量
     *
     * @param param 分析DTO
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 实际用电量列表
     */
    List<VppBiddingClearVO.ResolveLoad> getElecList(ResultAnalysisDTO param, String startTime, String endTime);

    /**
     * 获取多天的全部用户的数据
     *
     * @param resultAnalysisDTO 分析DTO
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param getAllUserId 全部用户ID
     * @param periodDetails 周期详情
     * @return 实际用电量列表
     */
    List<VppBiddingClearVO.ResolveLoad> getElecAllList(ResultAnalysisDTO resultAnalysisDTO, String startTime,
                                                       String endTime, List<Long> getAllUserId, List<String> periodDetails);

    /**
     * 获取日期范围内的实际用电量
     * @param tenantId 租户ID
     * @param dateDay 日期
     * @return 实际用电量列表
     */
    List<VppBiddingClearVO.ResolveLoad> getListByTenantId(Long tenantId, Date dateDay);

    /**
     * 删除日期范围内的实际用电量
     *
     * @param dateDay  日期
     * @param tenantId 租户ID
     */
    void removeByParam(Date dateDay, Long tenantId);

    /**
     * 获取24小时用户的实际用电量
     *
     * @param startDay 开始日期
     * @param endDay 结束日期
     * @param tenantId 租户ID
     * @param userCodes 用户编码
     * @return 实际用电量列表
     */
    Map<String, List<BigDecimal>> getActualTwentyFourForUser(Date startDay, Date endDay, Long tenantId, List<String> userCodes);

    /**
     * 批量添加实际用电量 
     * @param allActualDTOs 实际电量数据
     * @param params 电量数据
     * @param updateDTOs 数据维护更新DTO
     * @return 导入结果
     */
    ImportExcelDetailVO importVppElectricActualListBatch(List<VppElectricActualDTO> allActualDTOs, List<VppElectricQuantityDTO> params, List<DataMaintenanceUpdateDTO> updateDTOs);

    /**
     * 获取日期范围内的实际用电量
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @param tenantId  租户ID
     * @param consNo    用户编码
     * @return 实际用电量列表
     */
    List<VppElectricActual> getElectricByDateList(@NotNull(message = "开始日期不能为空") Date startTime, @NotNull(message = "结束日期不能为空") Date endTime, Long tenantId, String consNo);
}
