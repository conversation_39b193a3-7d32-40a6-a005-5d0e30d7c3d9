package com.fx.green.electricity.shanxi.service.service.user.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDeviceAndMeterDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDeviceAndMeterDTO.VppUserDeviceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDeviceAndMeterDTO.VppUserDeviceDTO.VppUserMeterDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserDeviceAndMeterVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserDevice;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserMeter;
import com.fx.green.electricity.shanxi.service.mapper.user.VppUserDeviceMapper;
import com.fx.green.electricity.shanxi.service.mapper.user.VppUserMeterMapper;
import com.fx.green.electricity.shanxi.service.service.user.VppUserDeviceService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户设备及电表管理 Service 实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppUserDeviceServiceImpl extends ServiceImpl<VppUserDeviceMapper, VppUserDevice> implements VppUserDeviceService {


    @Resource
    private VppUserMeterMapper vppUserMeterMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(VppUserDeviceAndMeterDTO param) {
        try {
            // 1. 参数校验
            if (param == null || CollectionUtil.isEmpty(param.getDeviceList())) {
                throw new FxServiceException("参数不能为空");
            }

            String consNo = param.getDeviceList().get(0).getConsNo();

            // 2. 每次进来删除原来的数据（根据户号删除相关设备和电表数据）
            if (consNo != null) {
                deleteExistingData(consNo);
            }

            // 3. 根据是否记录设备进行不同的处理逻辑
            if (param.getIsRecordDevice() != null && param.getIsRecordDevice() == 1) {
                // 3.1 记录设备：保存 户号-设备-电表 关系
                saveWithDevice(param);
            } else {
                // 3.2 不记录设备：只保存 户号-电表 关系
                saveWithoutDevice(param);
            }

            log.debug("saveOrUpdate: 户号{}的设备电表信息保存完成", consNo);
        } catch (Exception e) {
            log.error("saveOrUpdate失败", e);
            throw new RuntimeException("保存用户设备和电表信息失败", e);
        }
    }

    /**
     * 删除指定户号的现有数据
     *
     * @param consNo 户号
     */
    private void deleteExistingData(String consNo) {
        // 1. 先删除电表数据
        vppUserMeterMapper.delete(new LambdaQueryWrapper<VppUserMeter>()
                .eq(VppUserMeter::getConsNo, consNo));

        // 2. 再删除设备数据
        this.remove(new LambdaQueryWrapper<VppUserDevice>()
                .eq(VppUserDevice::getConsNo, consNo));
    }

    /**
     * 保存设备和电表信息（户号-设备-电表关系）
     *
     * @param param 参数
     */
    private void saveWithDevice(VppUserDeviceAndMeterDTO param) {
        List<VppUserMeter> meterList = new ArrayList<>();

        for (VppUserDeviceDTO deviceDTO : param.getDeviceList()) {
            // 1. 保存设备信息
            VppUserDevice userDevice = BeanCopyUtils.copy(deviceDTO, VppUserDevice.class);
            userDevice.setId(null); // 确保是新增
            this.save(userDevice);

            // 2. 保存该设备下的电表信息
            if (CollectionUtil.isNotEmpty(deviceDTO.getMeterList())) {
                for (VppUserMeterDTO meterDTO : deviceDTO.getMeterList()) {
                    VppUserMeter userMeter = BeanCopyUtils.copy(meterDTO, VppUserMeter.class);
                    userMeter.setId(null); // 确保是新增
                    userMeter.setDeviceId(userDevice.getId());
                    userMeter.setConsNo(deviceDTO.getConsNo());
                    meterList.add(userMeter);
                }
            }
        }

        // 3. 批量保存电表信息
        if (CollectionUtil.isNotEmpty(meterList)) {
            for (VppUserMeter meter : meterList) {
                vppUserMeterMapper.insert(meter);
            }
        }

    }

    /**
     * 只保存电表信息（户号-电表关系）
     *
     * @param param 参数
     */
    private void saveWithoutDevice(VppUserDeviceAndMeterDTO param) {
        VppUserDeviceDTO deviceDTO = param.getDeviceList().get(0);
        // 1. 批量保存电表信息
        List<VppUserMeter> meterList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(deviceDTO.getMeterList())) {
            for (VppUserMeterDTO meterDTO : deviceDTO.getMeterList()) {
                VppUserMeter userMeter = BeanCopyUtils.copy(meterDTO, VppUserMeter.class);
                userMeter.setId(null);
                userMeter.setConsNo(deviceDTO.getConsNo());
                meterList.add(userMeter);
            }

            for (VppUserMeter meter : meterList) {
                vppUserMeterMapper.insert(meter);
            }
        }

    }

    @Override
    public VppUserDeviceAndMeterVO getUserDeviceAndMeterByConsNo(VppUserDeviceAndMeterDTO.VppUserDeviceDTO param) {
        String consNo = param.getConsNo();

        try {
            VppUserDeviceAndMeterVO result = new VppUserDeviceAndMeterVO();

            // 1. 查询该户号下的设备列表
            List<VppUserDevice> deviceList = this.list(new LambdaQueryWrapper<VppUserDevice>()
                    .eq(VppUserDevice::getConsNo, consNo));

            // 2. 查询该户号下的电表列表  
            List<VppUserMeter> meterList = vppUserMeterMapper.selectList(new LambdaQueryWrapper<VppUserMeter>()
                    .eq(VppUserMeter::getConsNo, consNo));

            // 3. 判断是否记录设备模式
            if (CollectionUtil.isNotEmpty(deviceList)) {
                // 记录设备模式：有设备数据，构建设备-电表关系
                result.setIsRecordDevice(1);
                result.setDeviceList(buildDeviceVOList(deviceList, meterList));
            } else if (CollectionUtil.isNotEmpty(meterList)) {
                // 不记录设备模式：只有电表数据，不返回设备信息
                result.setIsRecordDevice(0);
                result.setDeviceList(buildMeterOnlyVOList(meterList));
            } else {
                // 无数据
                result.setIsRecordDevice(0);
                result.setDeviceList(new ArrayList<>());
            }

            log.debug("getUserDeviceAndMeterByConsNo: 户号{}查询到{}个设备，{}个电表",
                    consNo, deviceList.size(), meterList.size());
            return result;

        } catch (Exception e) {
            log.error("getUserDeviceAndMeterByConsNo失败，户号：{}", consNo, e);
            throw new RuntimeException("查询用户设备和电表信息失败", e);
        }
    }

    /**
     * 构建记录设备模式的VO列表（设备-电表关系）
     *
     * @param deviceList 设备列表
     * @param meterList  电表列表
     * @return 设备VO列表
     */
    private List<VppUserDeviceAndMeterVO.VppUserDeviceVO> buildDeviceVOList(
            List<VppUserDevice> deviceList, List<VppUserMeter> meterList) {

        List<VppUserDeviceAndMeterVO.VppUserDeviceVO> deviceVOList = new ArrayList<>();

        for (VppUserDevice device : deviceList) {
            VppUserDeviceAndMeterVO.VppUserDeviceVO deviceVO = BeanCopyUtils.copy(device, VppUserDeviceAndMeterVO.VppUserDeviceVO.class);

            // 查找该设备关联的电表
            List<VppUserDeviceAndMeterVO.VppUserDeviceVO.VppUserMeterVO> meterVOList = new ArrayList<>();
            for (VppUserMeter meter : meterList) {
                if (device.getId().equals(meter.getDeviceId())) {
                    VppUserDeviceAndMeterVO.VppUserDeviceVO.VppUserMeterVO meterVO =
                            BeanCopyUtils.copy(meter, VppUserDeviceAndMeterVO.VppUserDeviceVO.VppUserMeterVO.class);
                    meterVOList.add(meterVO);
                }
            }
            deviceVO.setMeterList(meterVOList);
            deviceVOList.add(deviceVO);
        }

        return deviceVOList;
    }

    /**
     * 构建不记录设备模式的 VO 列表（只有电表数据）
     *
     * @param meterList 电表列表
     * @return 设备VO列表（只包含电表信息）
     */
    private List<VppUserDeviceAndMeterVO.VppUserDeviceVO> buildMeterOnlyVOList(List<VppUserMeter> meterList) {
        List<VppUserDeviceAndMeterVO.VppUserDeviceVO> deviceVOList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(meterList)) {
            // 创建一个虚拟设备 VO 来承载电表数据
            VppUserDeviceAndMeterVO.VppUserDeviceVO deviceVO = new VppUserDeviceAndMeterVO.VppUserDeviceVO();

            // 从第一个电表获取基本信息
            VppUserMeter firstMeter = meterList.get(0);
            deviceVO.setConsNo(firstMeter.getConsNo());
            deviceVO.setUserId(firstMeter.getUserId());
            deviceVO.setAccountId(firstMeter.getAccountId());
            deviceVO.setTenantId(firstMeter.getTenantId());

            // 转换所有电表数据
            List<VppUserDeviceAndMeterVO.VppUserDeviceVO.VppUserMeterVO> meterVOList = new ArrayList<>();
            for (VppUserMeter meter : meterList) {
                VppUserDeviceAndMeterVO.VppUserDeviceVO.VppUserMeterVO meterVO =
                        BeanCopyUtils.copy(meter, VppUserDeviceAndMeterVO.VppUserDeviceVO.VppUserMeterVO.class);
                meterVOList.add(meterVO);
            }
            deviceVO.setMeterList(meterVOList);
            deviceVOList.add(deviceVO);
        }

        return deviceVOList;
    }
}
