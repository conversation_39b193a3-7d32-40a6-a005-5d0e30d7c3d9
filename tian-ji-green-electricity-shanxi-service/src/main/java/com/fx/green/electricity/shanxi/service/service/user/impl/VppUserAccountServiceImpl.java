package com.fx.green.electricity.shanxi.service.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppLoadUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserAccountDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserAccountVO;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserAccount;
import com.fx.green.electricity.shanxi.service.mapper.user.VppUserAccountMapper;
import com.fx.green.electricity.shanxi.service.service.user.VppUserAccountService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

import static com.fx.green.electricity.shanxi.service.enums.VirtualPowerPlantServiceCodeEnum.USER_CONS_NO_ERROR;


/**
 * 用户户号 Service 实现类
 *
 * <AUTHOR>
 **/
@Service
public class VppUserAccountServiceImpl extends ServiceImpl<VppUserAccountMapper, VppUserAccount> implements VppUserAccountService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateData(VppLoadUserDTO param) {
        if (param.getAccountList() == null || param.getAccountList().isEmpty()) {
            return;
        }

        // 1. 判断户号是否已经绑定了其他虚拟电厂或者用户
        boolean userAccountFlag = this.existsUserAccount(param.getAccountList());
        if (userAccountFlag) {
            throw new FxServiceException(USER_CONS_NO_ERROR);
        }

        // 2. 保存或更新户号
        List<VppUserAccountDTO> accountList = param.getAccountList();
        for (VppUserAccountDTO vppUserAccountDTO : accountList) {
            VppUserAccount vppUserAccount = BeanCopyUtils.copy(vppUserAccountDTO, VppUserAccount.class);
            vppUserAccount.setUserId(param.getId());
            vppUserAccount.setTenantId(param.getTenantId());
            saveOrUpdate(vppUserAccount);
        }
    }

    @Override
    public List<VppUserAccount> getList(Long tenantId) {
        LambdaQueryWrapper<VppUserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(VppUserAccount::getTenantId, tenantId);
        queryWrapper.in(VppUserAccount::getIsDelete, 0);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void deleteByUserId(Long vppUserId) {
        LambdaQueryWrapper<VppUserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(VppUserAccount::getUserId, vppUserId);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public List<VppUserAccountVO> getUserAccount(Long id) {
        LambdaQueryWrapper<VppUserAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(VppUserAccount::getUserId, id);
        List<VppUserAccount> vppUserAccounts = baseMapper.selectList(queryWrapper);
        return BeanCopyUtils.copyList(vppUserAccounts, VppUserAccountVO.class);
    }


    /**
     * 判断户号是否已经绑定了其他虚拟电厂或者用户
     *
     * @param accountList 户号列表
     * @return 是否已经绑定了其他虚拟电厂或者用户
     */
    public boolean existsUserAccount(List<VppUserAccountDTO> accountList) {
        // 1. 获取户号列表
        List<String> consNoList = accountList.stream()
                .map(VppUserAccountDTO::getConsNo)
                .collect(Collectors.toList());

        // 2. 获取户号ID列表
        List<Long> idList = accountList.stream()
                .map(VppUserAccountDTO::getId)
                .collect(Collectors.toList());

        // 3. 查询户号是否已经绑定了其他虚拟电厂或者用户
        LambdaQueryWrapper<VppUserAccount> userLqw = new LambdaQueryWrapper<>();
        userLqw.in(VppUserAccount::getConsNo, consNoList);
        userLqw.notIn(VppUserAccount::getId, idList);

        // 4. 根据不同的租户进行添加也是有问题的，库里面如果有两条他是不知道改进谁的
        int count = this.count(userLqw);
        return count > 0;
    }
}
