package com.fx.green.electricity.shanxi.service.entity.user;

import com.fx.green.electricity.shanxi.service.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 负荷用户
 *
 *  <AUTHOR>
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("负荷用户")
public class VppUser extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("负荷用户 ID")
    private Long id;

    @ApiModelProperty("统一社会信用代码")
    private String userCode;

    @ApiModelProperty("用户部门 ID")
    private Long deptId;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("负荷用户名称")
    private String name;

    @ApiModelProperty("是否第一次登录")
    private Integer isFirst;

    @ApiModelProperty("联系人姓名")
    private String contactName;

    @ApiModelProperty("联系人电话")
    private String contactPhone;

    @ApiModelProperty("微信绑定 ID")
    private String openId;

    @ApiModelProperty("红利系数")
    private BigDecimal dividendRatio;

    @ApiModelProperty("建议上调用电日前价格")
    private BigDecimal suggestionUpForecastPrice;

    @ApiModelProperty("建议下调用电日前价格")
    private BigDecimal suggestionDownForecastPrice;

    @ApiModelProperty("地区编号")
    private String areaCode;

    @ApiModelProperty("所在区县")
    private String districtAddr;

    @ApiModelProperty("地区详细信息")
    private String areaCodeDetail;

    @ApiModelProperty("详细地址(地理位置)")
    private String deptAddress;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String lat;

    @ApiModelProperty("用电类型(1.大工业 2.一般工商业 3.普通工业 4.其他)")
    private Integer electricityType;

    @ApiModelProperty("租户 ID")
    private Long tenantId;

    @ApiModelProperty("企业简介")
    private String enterpriseIntroduction;

    @ApiModelProperty("企业照片")
    private String enterprisePhotos;

}