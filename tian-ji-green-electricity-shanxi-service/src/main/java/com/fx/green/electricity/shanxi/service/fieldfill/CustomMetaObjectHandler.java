package com.fx.green.electricity.shanxi.service.fieldfill;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.fx.common.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.ReflectionException;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 自定义sql字段填充器，自动填充创建修改相关字段
 *
 * <AUTHOR>
 */

@Slf4j
@Component
public class CustomMetaObjectHandler implements MetaObjectHandler {

    private static final String CREATE_USER = "createUser";

    private static final String CREATE_TIME = "createTime";

    private static final String UPDATE_USER = "updateUser";

    private static final String UPDATE_TIME = "updateTime";

    @Override
    public void insertFill(MetaObject metaObject) {
        try {
            //设置createUser（BaseEntity)
            Object createUser = metaObject.getValue(CREATE_USER);
            if (ObjectUtil.isNull(createUser)) {
                setFieldValByName(CREATE_USER, JwtUtil.getUserIdNotException(), metaObject);
                setFieldValByName(UPDATE_USER, JwtUtil.getUserIdNotException(), metaObject);
            }

        } catch (ReflectionException e) {
            log.warn(">>> CustomMetaObjectHandler处理过程中无{}字段，不做处理", CREATE_USER);
        }
        try {
            //为空则设置createTime（BaseEntity)
            Object createTime = metaObject.getValue(CREATE_TIME);
            if (ObjectUtil.isNull(createTime)) {
                setFieldValByName(CREATE_TIME, new Date(), metaObject);
                setFieldValByName(UPDATE_TIME, new Date(), metaObject);
            }
        } catch (ReflectionException e) {
            log.warn(">>> CustomMetaObjectHandler处理过程中无{}字段，不做处理", CREATE_TIME);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            //设置updateUser（BaseEntity)
            Object updateUser = metaObject.getValue(UPDATE_USER);
            if (ObjectUtil.isNull(updateUser)) {
                setFieldValByName(UPDATE_USER, JwtUtil.getUserIdNotException(), metaObject);
            }
        } catch (ReflectionException e) {
            log.warn(">>> CustomMetaObjectHandler处理过程中无{}字段，不做处理", UPDATE_USER);
        }
        try {
            //为空则设置updateTime（BaseEntity)
            Object updateTime = metaObject.getValue(UPDATE_TIME);
            if (ObjectUtil.isNull(updateTime)) {
                setFieldValByName(UPDATE_TIME, new Date(), metaObject);
            }
        } catch (ReflectionException e) {
            log.warn(">>> CustomMetaObjectHandler处理过程中无{}字段，不做处理", UPDATE_TIME);
        }
    }

}
