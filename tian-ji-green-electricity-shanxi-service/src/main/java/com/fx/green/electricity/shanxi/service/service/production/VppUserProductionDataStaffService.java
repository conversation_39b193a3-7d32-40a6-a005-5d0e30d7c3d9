package com.fx.green.electricity.shanxi.service.service.production;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.service.entity.production.VppUserProductionDataStaff;

import java.util.List;

/**
 * 用户生产数据 - 沟通人员表 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppUserProductionDataStaffService extends IService<VppUserProductionDataStaff> {

    /**
     * 根据用户生产数据id删除
     *
     * @param id 用户生产数据id
     */
    void removeByDataId(Long id);

    /**
     * 根据用户生产数据id获取
     * @param id 用户生产数据id
     * @return 用户生产数据-沟通人员表列表
     */
    List<VppUserProductionDataStaff> getByDataId(Long id);

}
