package com.fx.green.electricity.shanxi.service.service.dict.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.GetVppDictionaryDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.dictionary.VppDictionaryVO;
import com.fx.green.electricity.shanxi.service.entity.dictionary.VppDictionary;
import com.fx.green.electricity.shanxi.service.mapper.dictionary.VppDictionaryMapper;
import com.fx.green.electricity.shanxi.service.service.dict.VppDictionaryService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典 Service 实现类
 *
 *  <AUTHOR>
 **/
@Service
public class VppDictionaryServiceImpl extends ServiceImpl<VppDictionaryMapper, VppDictionary> implements VppDictionaryService {

    @Override
    public List<VppDictionaryVO> getDictionary(GetVppDictionaryDTO param) {
        List<VppDictionaryVO> vppDictionaryVOS = new ArrayList<>();
        //获取type对应的字典信息
        LambdaQueryWrapper<VppDictionary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppDictionary::getType, param.getType());
        queryWrapper.orderByAsc(VppDictionary::getId);
        List<VppDictionary> list = list(queryWrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            Map<Long, List<VppDictionary>> dataMap = list.stream().collect(Collectors.groupingBy(VppDictionary::getParentId));
            List<VppDictionary> vppDictionaries = dataMap.get(0L);
            if (ObjectUtil.isNotEmpty(vppDictionaries)) {
                for (VppDictionary vppDictionary : vppDictionaries) {
                    VppDictionaryVO vppDictionaryVO = new VppDictionaryVO();
                    BeanCopyUtils.copy(vppDictionary, vppDictionaryVO);
                    //获取当前下面是否存在数据
                    Long id = vppDictionary.getId();
                    List<VppDictionary> vppDictionaries1 = dataMap.get(id);
                    List<VppDictionaryVO.DataInfo> dataInfoAll = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(vppDictionaries1)) {
                        for (VppDictionary dictionary : vppDictionaries1) {
                            VppDictionaryVO.DataInfo dataInfo = new VppDictionaryVO.DataInfo();
                            BeanCopyUtils.copy(dictionary, dataInfo);
                            //获取当前下面是否存在数据
                            Long cid = dictionary.getId();
                            List<VppDictionary> vppDictionaries2 = dataMap.get(cid);
                            List<VppDictionaryVO.DataInfos> dataInfos1 = new ArrayList<>();
                            if (ObjectUtil.isNotNull(vppDictionaries2) && ObjectUtil.isNotEmpty(vppDictionaries2)) {
                                for (VppDictionary dictionary1 : vppDictionaries2) {
                                    VppDictionaryVO.DataInfos dataInfos = new VppDictionaryVO.DataInfos();
                                    BeanCopyUtils.copy(dictionary1, dataInfos);
                                    dataInfos1.add(dataInfos);
                                }
                                dataInfo.setChildren(dataInfos1);
                            }
                            dataInfoAll.add(dataInfo);
                        }
                        vppDictionaryVO.setChildren(dataInfoAll);
                    }
                    vppDictionaryVOS.add(vppDictionaryVO);
                }
            }
        }
        return vppDictionaryVOS;
    }
}
