package com.fx.green.electricity.shanxi.server.service.data;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.GetUpdateTimeDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.ImportExcelDetailPageListVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 电量数据 Service 接口
 *
 * <AUTHOR>
 */
public interface VppElectricQuantityService {

    /**
     * 查询实际用电量导入记录
     *
     * @param param
     * @return
     */
    DataResult<FxPage<ImportExcelDetailPageListVO>> queryImportRecord(VppElectricQuantityDTO.QueryDTO param);

    /**
     * 日前申报导入
     *
     * @param file
     * @param time
     * @return
     */
    DataResult<ImportExcelVO> importRecord(MultipartFile file, String time);

    /**
     * 获取数据更新时间
     *
     * @return
     */
    DataResult<String> getUpdateTime(GetUpdateTimeDTO param);

    /**
     * 删除申报数据
     *
     * @param deleteRecordDTO
     * @return
     */
    DataResult<Void> deleteRecordData(DeleteRecordDTO deleteRecordDTO);

    /**
     * 下载申报数据
     *
     * @param param
     * @return
     */
    DataResult<Void> downloadRecord(CommonDTO.DateDTO param);
}
