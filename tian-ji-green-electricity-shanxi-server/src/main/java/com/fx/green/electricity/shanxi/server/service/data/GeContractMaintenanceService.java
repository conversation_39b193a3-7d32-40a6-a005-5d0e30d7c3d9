package com.fx.green.electricity.shanxi.server.service.data;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeContractMaintenanceVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 合同维护数据
 */
public interface GeContractMaintenanceService {

    /**
     * 新增合同数据
     *
     * @param param
     */
    DataResult<Void> insertContract(GeContractMaintenanceDTO param);

    /**
     * 删除合同数据
     *
     * @param param
     */
    DataResult<Void> deleteContract(IdDTO param);

    /**
     * 合同数据分页
     *
     * @param param
     * @return
     */
    DataResult<List<GeContractMaintenanceVO>> contractList(GeContractMaintenanceDTO param);

    /**
     * 导入机组实际发电曲线、功率预测、节点价格
     *
     * @param file
     */
    DataResult<Void> importUnitRealElectricityNodePower(MultipartFile file, Integer type);

}
