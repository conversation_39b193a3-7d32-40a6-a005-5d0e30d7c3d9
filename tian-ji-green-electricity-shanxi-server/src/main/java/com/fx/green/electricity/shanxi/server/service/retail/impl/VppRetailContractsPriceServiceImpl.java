package com.fx.green.electricity.shanxi.server.service.retail.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.api.VppRetailContractsPriceApi;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsPriceDeriveVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsPriceQueryVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsPriceVO;
import com.fx.green.electricity.shanxi.server.service.retail.VppRetailContractsPriceService;
import com.fx.green.electricity.shanxi.server.utils.DownloadUtil;
import com.fx.green.electricity.shanxi.server.utils.HttpServletUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 *
 **/
@Service
public class VppRetailContractsPriceServiceImpl implements VppRetailContractsPriceService {

    @Resource
    private VppRetailContractsPriceApi vppRetailContractsPriceApi;

    @Override
    public DataResult<List<VppRetailContractsPriceVO>> getPriceList(VppRetailContractsPriceQueryVO param) {
        return vppRetailContractsPriceApi.getPriceList(param);
    }

    @Override
    public DataResult<Void> dataDerive(VppRetailContractsPriceQueryVO param) {
        DataResult<List<VppRetailContractsPriceVO>> priceList = vppRetailContractsPriceApi.getPriceList(param);
        List<VppRetailContractsPriceVO> data = priceList.getData();
        if (ObjectUtil.isNotEmpty(data)) {
            List<VppRetailContractsPriceDeriveVO> dataList = new ArrayList<>();
            for (VppRetailContractsPriceVO vppRetailContractsPriceVO : data) {
                VppRetailContractsPriceDeriveVO vppRetailContractsPriceDeriveVO = new VppRetailContractsPriceDeriveVO();
                vppRetailContractsPriceDeriveVO.setSigningMonth(DateUtil.format(vppRetailContractsPriceVO.getSigningMonth(), "yyyy-MM"));
                vppRetailContractsPriceDeriveVO.setType(vppRetailContractsPriceVO.getType() == 1 ? "(分时)年、月及旬6:4加权出清电价" : "（分时）年、月、旬及日前现货加权出清电价");
                vppRetailContractsPriceDeriveVO.setTimeFrame(vppRetailContractsPriceVO.getTimeFrame());
                vppRetailContractsPriceDeriveVO.setPrice(vppRetailContractsPriceVO.getPrice());
                dataList.add(vppRetailContractsPriceDeriveVO);
            }

            String fileName = "(" + DateUtil.format(param.getSigningMonth(), "yyyy-MM") + ")" + "浮动价格值" + ".xlsx";
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            EasyExcelFactory.write(stream, VppRetailContractsPriceDeriveVO.class)
                    .sheet()
                    .doWrite(dataList);
            byte[] fileBytes = stream.toByteArray();
            DownloadUtil.downloadJsonType(fileName, fileBytes, HttpServletUtil.getResponse());

        }
        return DataResult.success();
    }
}
