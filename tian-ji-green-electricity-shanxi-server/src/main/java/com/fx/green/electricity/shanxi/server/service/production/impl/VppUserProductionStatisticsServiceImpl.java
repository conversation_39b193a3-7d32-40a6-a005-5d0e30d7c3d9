package com.fx.green.electricity.shanxi.server.service.production.impl;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.api.production.VppUserProductionStatisticsApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.production.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionDataDeviceListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatisticsListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatisticsVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatusVO;
import com.fx.green.electricity.shanxi.server.service.production.VppUserProductionStatisticsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户生产统计 Service 实现类
 *
 * <AUTHOR>
 **/
@Service
public class VppUserProductionStatisticsServiceImpl implements VppUserProductionStatisticsService {

    @Resource
    private VppUserProductionStatisticsApi vppUserProductionStatisticsApi;


    @Override
    public DataResult<Void> saveUserProductionPlan(VppUserProductionStatisticsDTO param) {
        return vppUserProductionStatisticsApi.saveUserProductionPlan(param);
    }

    @Override
    public DataResult<VppUserProductionStatisticsListVO> getProductionStatisticsByDay(VppUserProductionStatisticsQueryDTO param) {
        return vppUserProductionStatisticsApi.getProductionStatisticsByDay(param);
    }

    @Override
    public DataResult<VppUserProductionStatisticsVO> getProductionStatisticsByUserId(VppUserProductionStatisticsQueryDetailDTO param) {
        return vppUserProductionStatisticsApi.getProductionStatisticsByUserId(param);
    }

    @Override
    public DataResult<List<VppUserProductionDataDeviceListVO>> getDeviceListByType(VppUserProductionDataDeviceQueryDTO param) {
        return vppUserProductionStatisticsApi.getDeviceListByType(param);
    }


    @Override
    public DataResult<List<VppUserProductionStatusVO>> getUserProductionStatus(VppUserProductionStatusQueryDTO param) {
        return vppUserProductionStatisticsApi.getUserProductionStatus(param);
    }
}
