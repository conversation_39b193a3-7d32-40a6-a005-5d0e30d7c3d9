package com.fx.green.electricity.shanxi.server.advice;


import com.fx.auth.util.AuthUtil;
import com.fx.auth.vo.TenantUserVO;
import com.fx.common.dto.BaseDTO;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.common.vo.HeaderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;

import java.io.IOException;
import java.lang.reflect.Type;


/**
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Order(1)
@ControllerAdvice
@Slf4j
public class AuthRequestBodyAdvice implements RequestBodyAdvice {
    @Override
    public boolean supports(MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return true;
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) throws IOException {
        return httpInputMessage;
    }

    @Override
    public Object afterBodyRead(Object o, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        TenantUserVO tenantUserVO = AuthUtil.currentUser();
        HeaderVO headerVO = RequestHeadersUtil.getRequestHeaders();
        if (tenantUserVO != null) {
            if (o instanceof BaseDTO) {
                ((BaseDTO) o).setTenantUserId(tenantUserVO.getId());
                if (headerVO != null) {
                    ((BaseDTO) o).setTenantId(headerVO.getTenantId());
                } else {
                    ((BaseDTO) o).setTenantId(tenantUserVO.getTenantId());
                }
            }
        }
        return o;
    }

    @Override
    public Object handleEmptyBody(Object o, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return o;
    }
}
