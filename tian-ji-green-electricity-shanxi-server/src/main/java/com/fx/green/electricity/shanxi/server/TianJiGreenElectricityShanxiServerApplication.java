package com.fx.green.electricity.shanxi.server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 */

@SpringBootApplication
@EnableDiscoveryClient
@ServletComponentScan
@EnableFeignClients("com.fx")
@ComponentScan("com.fx")
public class TianJiGreenElectricityShanxiServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(TianJiGreenElectricityShanxiServerApplication.class, args);
    }

}
