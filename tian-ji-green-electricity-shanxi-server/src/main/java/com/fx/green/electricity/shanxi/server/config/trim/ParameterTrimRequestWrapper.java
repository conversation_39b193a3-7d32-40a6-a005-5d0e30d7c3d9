package com.fx.green.electricity.shanxi.server.config.trim;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.util.*;

/**
 * <AUTHOR>
 */
public class ParameterTrimRequestWrapper extends HttpServletRequestWrapper {

    private Map<String, String[]> params = new HashMap<>();

    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request The request to wrap
     * @throws IllegalArgumentException if the request is null
     */
    public ParameterTrimRequestWrapper(HttpServletRequest request) {
        super(request);
        params.putAll(request.getParameterMap());
        modifyParameterValues();
    }

    private void modifyParameterValues() {//将parameter的值去除空格后重写回去
        Set<Map.Entry<String, String[]>> paramSet = params.entrySet();
        for (Map.Entry<String, String[]> p : paramSet) {
            String[] values = p.getValue();
            for (int i = 0; i < values.length; i++) {
                values[i] = values[i].trim();
            }
            this.params.put(p.getKey(), values);
        }
    }

    @Override
    public Enumeration<String> getParameterNames() {//重写getParameterNames()
        return new Vector<>(params.keySet()).elements();
    }


    @Override
    public String getParameter(String name) {//重写getParameter()
        String[] values = params.get(name);
        if (values == null || values.length == 0) {
            return null;
        }
        return values[0];
    }

    @Override
    public String[] getParameterValues(String name) {//重写getParameterValues()
        return params.get(name);
    }

    @Override
    public Map<String, String[]> getParameterMap() { //重写getParameterMap()
        return this.params;
    }

}
