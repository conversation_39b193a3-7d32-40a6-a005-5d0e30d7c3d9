package com.fx.green.electricity.shanxi.server.config.trim;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@WebFilter
public class StringTrimFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        ParameterTrimRequestWrapper parameterTrimRequestWrapper = new ParameterTrimRequestWrapper((HttpServletRequest) request);
        chain.doFilter(parameterTrimRequestWrapper, response);
    }

}
