package com.fx.green.electricity.shanxi.server.controller.user;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppLoadUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserInfoVO;
import com.fx.green.electricity.shanxi.server.service.user.VppLoadUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 负荷用户管理 Controller
 *
 * <AUTHOR>
 **/
@RestController
@Api(tags = "用户管理 - 负荷用户管理")
@RequestMapping("/vppLoadUser")
public class VppLoadUserController {

    @Resource
    private VppLoadUserService vppLoadUserService;

    @CommonNoRepeat
    @ApiOperation("保存或修改基础信息")
    @PostMapping("/saveOrUpdate")
    public DataResult<Long> saveOrUpdate(@RequestBody @Valid VppLoadUserDTO param) {
        return vppLoadUserService.saveOrUpdate(param);
    }

    @ApiOperation("查询负荷用户详情")
    @PostMapping("/findById")
    public DataResult<VppUserInfoVO> findById(@RequestBody IdDTO param) {
        return vppLoadUserService.findById(param);
    }

    @ApiOperation("根据id删除负荷用户")
    @PostMapping("/delete")
    public DataResult<Void> delete(@RequestBody IdDTO param) {
        return vppLoadUserService.delete(param);
    }

    @ApiOperation("分页列表")
    @PostMapping("/pageList")
    public DataResult<FxPage<VppLoadUserVO>> pageList(@RequestBody VppUserDTO.SearchDTO param) {
        return vppLoadUserService.pageList(param);
    }

//    @ApiOperation("负荷用户树级列表-多日查询")
//    @PostMapping("/queryTreeListByDate")
//    public DataResult<VppLoadUserVO.TreeVO> queryTreeListByDate(@RequestBody QueryDateDTO param) {
//        return vppLoadUserService.queryTreeListByDate(param);
//    }

    @ApiOperation("重置密码")
    @PostMapping("/resetPassword")
    public DataResult<Void> resetPassword(@RequestBody VppUserDTO param) {
        return vppLoadUserService.resetPassword(param);
    }

}
