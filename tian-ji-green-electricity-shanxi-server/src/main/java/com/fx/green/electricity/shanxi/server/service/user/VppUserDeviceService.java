package com.fx.green.electricity.shanxi.server.service.user;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDeviceAndMeterDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserDeviceAndMeterVO;

/**
 * 用户设备及电表管理 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppUserDeviceService {

    /**
     * 保存或修改用户设备及电表
     *
     * @param param 用户设备及电表DTO
     */
    DataResult<Void> saveOrUpdate(VppUserDeviceAndMeterDTO param);

    /**
     * 根据用户户号查询用户设备及电表
     *
     * @param param 用户户号
     * @return 用户设备及电表VO
     */
    DataResult<VppUserDeviceAndMeterVO> getUserDeviceAndMeterByConsNo(VppUserDeviceAndMeterDTO.VppUserDeviceDTO param);
}
