package com.fx.green.electricity.shanxi.server.service.retail;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.RetailContractsManageDetailDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.UpdateRetailContractsManageDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageListVO;

/**
 *
 **/
public interface VppRetailContractsManageService {


    /**
     * 零售合同管理列表
     *
     * @param param
     * @return
     */
    DataResult<FxPage<VppRetailContractsManageListVO>> getList(QueryVppRetailContractsDTO param);

    /**
     * 获取合同详情
     *
     * @param retailContractsManageDetailDTO
     * @return
     */
    DataResult<VppRetailContractsManageDetailVO> retailContractsManageDetail(RetailContractsManageDetailDTO retailContractsManageDetailDTO);

    /**
     * 导出列表
     *
     * @param retailContractsManageDetailDTO
     * @return
     */
    DataResult<Void> exportList(RetailContractsManageDetailDTO retailContractsManageDetailDTO);

    /**
     * 修改用户的红利系数
     *
     * @param param
     * @return
     */
    DataResult<Void> updateDividendCoefficient(UpdateRetailContractsManageDTO param);

    /**
     * 导出零售合同列表
     *
     * @param param
     * @return
     */
    DataResult<Void> exportManageList(QueryVppRetailContractsDTO param);
}
