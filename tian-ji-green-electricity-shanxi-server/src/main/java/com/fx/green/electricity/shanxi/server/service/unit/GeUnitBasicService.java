package com.fx.green.electricity.shanxi.server.service.unit;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeUnitBasicDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeUnitBasicVO;

import java.util.List;

/**
 * 绿电机组信息
 */
public interface GeUnitBasicService {

    /**
     * 新增机组信息
     *
     * @param param
     */
    DataResult<Void> insertUnitBase(GeUnitBasicDTO param);

    /**
     * 根据租户id查询对应机组
     *
     * @param param
     * @return
     */
    DataResult<List<GeUnitBasicVO>> unitBaseList(IdDTO param);

    /**
     * 删除机组信息
     *
     * @param param
     */
    DataResult<Void> deleteUnitBase(IdDTO param);
}
