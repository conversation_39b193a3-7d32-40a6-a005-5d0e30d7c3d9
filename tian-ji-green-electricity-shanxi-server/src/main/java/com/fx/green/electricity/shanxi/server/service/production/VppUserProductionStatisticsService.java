package com.fx.green.electricity.shanxi.server.service.production;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.production.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionDataDeviceListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatisticsListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatisticsVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatusVO;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户生产统计（主表） Service 接口
 *
 * <AUTHOR>
 **/
public interface VppUserProductionStatisticsService {


    /**
     * 用户生产统计新增/编辑
     * @param param 用户生产统计DTO
     * @return 成功/失败
     */
    DataResult<Void> saveUserProductionPlan(VppUserProductionStatisticsDTO param);

    /**
     * 根据日期查询用户生产计划列表
     * @param param 用户生产统计查询DTO
     * @return 用户生产统计列表VO
     */
    DataResult<VppUserProductionStatisticsListVO> getProductionStatisticsByDay(VppUserProductionStatisticsQueryDTO param);

    /**
     * 根据用户id查询用户生产计划列表
     * @param param 用户生产统计查询DTO
     * @return 用户生产统计VO
     */
    DataResult<VppUserProductionStatisticsVO> getProductionStatisticsByUserId(VppUserProductionStatisticsQueryDetailDTO param);


    /**
     * 根据用户id和设备类型获取设备列表
     * @param param 用户生产统计查询DTO
     * @return 用户生产统计设备列表VO
     */
    DataResult<List<VppUserProductionDataDeviceListVO>> getDeviceListByType(VppUserProductionDataDeviceQueryDTO param);

    /**
     * 获取用户的生产状态列表
     * @param param 用户生产统计查询DTO
     * @return 用户生产统计状态列表VO
     */
    DataResult<List<VppUserProductionStatusVO>> getUserProductionStatus(@Valid VppUserProductionStatusQueryDTO param);
}
