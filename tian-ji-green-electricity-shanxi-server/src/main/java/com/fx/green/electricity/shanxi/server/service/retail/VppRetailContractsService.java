package com.fx.green.electricity.shanxi.server.service.retail;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 **/
public interface VppRetailContractsService {

    /**
     * 零售合同导入
     *
     * @param file
     * @param runMonth
     * @param retailContractsId
     * @return
     */
    DataResult<Void> queryImportRecord(MultipartFile file, String runMonth, Long retailContractsId, String tenantId);

    /**
     * 获取零售列表
     *
     * @param param
     * @return
     */
    DataResult<FxPage<VppRetailContractsVO>> getList(QueryVppRetailContractsDTO param);
}