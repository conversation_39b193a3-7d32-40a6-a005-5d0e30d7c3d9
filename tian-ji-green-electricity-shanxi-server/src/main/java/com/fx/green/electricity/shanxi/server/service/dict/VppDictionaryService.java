package com.fx.green.electricity.shanxi.server.service.dict;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.GetVppDictionaryDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.dictionary.VppDictionaryVO;

import java.util.List;

/**
 * 字典 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppDictionaryService {

    /**
     * 获取字典信息
     *
     * @param param 获取字典信息 DTO
     * @return 字典信息列表
     */
    DataResult<List<VppDictionaryVO>> getDictionary(GetVppDictionaryDTO param);
}
