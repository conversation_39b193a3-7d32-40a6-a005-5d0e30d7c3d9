package com.fx.green.electricity.shanxi.server.service.user;


import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.AdjustDeclareDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.QueryDateDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppLoadUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserInfoVO;

/**
 * 负荷用户 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppLoadUserService {

    /**
     * 添加负荷用户
     *
     * @param param 负荷用户 DTO
     * @return 成功/失败
     */
    DataResult<Long> saveOrUpdate(VppLoadUserDTO param);

    /**
     * 根据id删除
     *
     * @param param 负荷用户 ID
     * @return 成功/失败
     */
    DataResult<Void> delete(IdDTO param);

    /**
     * 根据id查询负荷用户详情
     *
     * @param param 负荷用户 ID
     * @return 负荷用户详情
     */
    DataResult<VppUserInfoVO> findById(IdDTO param);

    /**
     * 分页查询负荷用户
     *
     * @param param 筛选条件
     * @return 负荷用户
     */
    DataResult<FxPage<VppLoadUserVO>> pageList(VppUserDTO.SearchDTO param);


    /**
     * 重置密码
     *
     * @param param 负荷用户 DTO
     * @return 成功/失败
     */
    DataResult<Void> resetPassword(VppUserDTO param);

    /**
     * 获取多天的多个用户的绑定周期
     * @param param 查询用户树形结构 DTO
     * @return 成功/失败
     */
    DataResult<VppLoadUserVO.TreeVO> queryTreeListByDate(QueryDateDTO param);

    /**
     * 查询用户树形结构列表
     *
     * @param param 查询用户树形结构 DTO
     * @return 用户树形结构列表
     */
    DataResult<VppLoadUserVO.TreeVO> queryTreeList(AdjustDeclareDTO.QueryUserTreeDTO param);

}
