package com.fx.green.electricity.shanxi.server.service.retail.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.api.VppRetailContractsManageApi;
import com.fx.green.electricity.shanxi.api.constant.VppConstant;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.RetailContractsManageDetailDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.UpdateRetailContractsManageDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageListVO;
import com.fx.green.electricity.shanxi.server.service.retail.VppRetailContractsManageService;
import com.fx.green.electricity.shanxi.server.utils.DownloadUtil;
import com.fx.green.electricity.shanxi.server.utils.HttpServletUtil;
import com.fx.green.electricity.shanxi.server.utils.MergeSameRowsStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Service
public class VppRetailContractsManageServiceImpl implements VppRetailContractsManageService {


    @Resource
    private VppRetailContractsManageApi vppRetailContractsManageApi;

    @Override
    public DataResult<FxPage<VppRetailContractsManageListVO>> getList(QueryVppRetailContractsDTO param) {
        return vppRetailContractsManageApi.getList(param);
    }

    @Override
    public DataResult<VppRetailContractsManageDetailVO> retailContractsManageDetail(RetailContractsManageDetailDTO param) {
        return vppRetailContractsManageApi.retailContractsManageDetail(param);
    }

    @Override
    public DataResult<Void> exportList(RetailContractsManageDetailDTO param) {
        DataResult<VppRetailContractsManageDetailVO> vppRetailContractsManageDetailVODataResult = vppRetailContractsManageApi.retailContractsManageDetail(param);
        VppRetailContractsManageDetailVO data = vppRetailContractsManageDetailVODataResult.getData();
        List<List<String>> header;
        List<List<String>> formatTableDataList = new ArrayList<>();
        if (ObjectUtil.isNotNull(data.getTimeFrame())) {
            header = header(data.getIntervalTimeFrame());
        } else {
            header = header(null);
        }

        if (ObjectUtil.isNotNull(data.getPowerList())) {
            List<BigDecimal> powerList = data.getPowerList();
            // 表格数据
            formatTableDataList.add(this.formatTableDataList(param, powerList, data, VppConstant.ONE_NUMBER));
        } else {
            // 表格数据
            formatTableDataList.add(this.formatTableDataList(param, null, data, VppConstant.ONE_NUMBER));
        }

        if (ObjectUtil.isNotNull(data.getPriceList())) {
            List<BigDecimal> priceList = data.getPriceList();
            // 表格数据
            formatTableDataList.add(this.formatTableDataList(param, priceList, data, VppConstant.TWO_NUMBER));
        } else {
            // 表格数据
            formatTableDataList.add(this.formatTableDataList(param, null, data, VppConstant.TWO_NUMBER));
        }

        // 查询数据并处理格式
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        // easyExcel导出
        EasyExcelFactory.write(stream)
                .head(header)
                .registerWriteHandler(new MergeSameRowsStrategy(1, new int[]{0, 1}))
                .sheet("sheet")
                .doWrite(formatTableDataList);
        byte[] fileBytes = stream.toByteArray();
        DownloadUtil.downloadJsonType(param.getName() + param.getSigningMonth() + "零售合同详情" + ".xlsx", fileBytes, HttpServletUtil.getResponse());
        return DataResult.success();
    }

    @Override
    public DataResult<Void> updateDividendCoefficient(UpdateRetailContractsManageDTO param) {
        return vppRetailContractsManageApi.updateDividendCoefficient(param);
    }

    @Override
    public DataResult<Void> exportManageList(QueryVppRetailContractsDTO param) {
        DataResult<List<VppRetailContractsManageListVO>> listDataResult = vppRetailContractsManageApi.exportManageList(param);
        if (ObjectUtil.isNotNull(listDataResult.getData())) {
            List<VppRetailContractsManageListVO> list = listDataResult.getData();
            String fileName = "零售合同信息" + DateUtil.formatDate(param.getStartDate()) + "-" + DateUtil.formatDate(param.getEndDate()) + ".xlsx";
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            EasyExcel.write(stream, VppRetailContractsManageListVO.class)
                    .sheet()
                    .doWrite(list);
            byte[] fileBytes = stream.toByteArray();
            DownloadUtil.downloadJsonType(fileName, fileBytes, HttpServletUtil.getResponse());
        }
        return DataResult.success();
    }


    private List<List<String>> header(List<String> timeFrame) {
        List<List<String>> headers = new ArrayList<>();
        headers.add(Arrays.asList("用户名称", "用户名称"));
        headers.add(Arrays.asList("红利分享系数", "红利分享系数"));
        headers.add(Arrays.asList("周期", "周期"));
        headers.add(Arrays.asList("类型", "类型"));
        if (ObjectUtil.isNotNull(timeFrame)) {
            for (String s : timeFrame) {
                headers.add(Arrays.asList(s, s));
            }
        }
        return headers;
    }

    private List<String> formatTableDataList(RetailContractsManageDetailDTO param, List<BigDecimal> tableDataList, VppRetailContractsManageDetailVO data, Integer type) {
        List<String> listList = new ArrayList<>();
        listList.add(param.getName());
        if (ObjectUtil.isNotNull(data.getDividendSharCoefficient())) {
            listList.add(data.getDividendSharCoefficient().setScale(BigDecimal.ROUND_CEILING, BigDecimal.ROUND_UP).toString());
        } else {
            listList.add(null);
        }
        listList.add("按月");
        if (type.equals(VppConstant.ONE_NUMBER)) {
            listList.add("电量");
        } else if (type.equals(VppConstant.TWO_NUMBER)) {
            listList.add("电价");
        }

        if (CollUtil.isEmpty(tableDataList)) {
            return new ArrayList<>();
        }
        // 数据格式转换
        for (BigDecimal tableVO : tableDataList) {
            toStringList(param, listList, tableVO);
        }
        return listList;
    }

    private List<String> toStringList(RetailContractsManageDetailDTO param, List<String> listList, BigDecimal tableVO) {
        listList.add(tableVO.toString());
        return listList;
    }
}
