package com.fx.green.electricity.shanxi.server.utils;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;


/**
 * <AUTHOR>
 * 自定义合并策略 该类继承了AbstractMergeStrategy抽象合并策略，需要重写merge()方法
 */
public class CustomMergeStrategy extends AbstractMergeStrategy {

    /**
     * 合并行数index
     */
    private int mergeRowIndex;

    /**
     * 合并开始列
     */
    private int mergeColumnStartIndex;

    /**
     * 合并结束列
     */
    private int mergeColumnEndIndex;

    /**
     * 需要合并的sheetName
     */
    private final String sheetName;


    /**
     * exportDataList为待合并目标列的值
     *
     * @param mergeRowIndex
     * @param mergeColumnStartIndex
     * @param mergeColumnEndIndex
     * @param sheetName
     */
    public CustomMergeStrategy(Integer mergeRowIndex, Integer mergeColumnStartIndex, Integer mergeColumnEndIndex, String sheetName) {
        this.mergeRowIndex = mergeRowIndex;
        this.mergeColumnStartIndex = mergeColumnStartIndex;
        this.mergeColumnEndIndex = mergeColumnEndIndex;
        this.sheetName = sheetName;
    }


    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        //当前行
        int curRowIndex = cell.getRowIndex();
        //当前列
        int curColIndex = cell.getColumnIndex();
        //合并2列好用
        if (curRowIndex == mergeRowIndex && curColIndex >= mergeColumnStartIndex && curColIndex < mergeColumnEndIndex) {
            sheet.addMergedRegion(new CellRangeAddress(mergeRowIndex, mergeRowIndex, mergeColumnStartIndex, mergeColumnEndIndex));
        }

    }

}

