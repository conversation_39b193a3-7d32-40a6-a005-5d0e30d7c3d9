package com.fx.green.electricity.shanxi.server.controller.data;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeContractMaintenanceVO;
import com.fx.green.electricity.shanxi.server.service.data.GeContractMaintenanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 合同数据维护 Controller
 */
@RestController
@Api(tags = "数据维护 - 合同数据维护")
@RequestMapping("/geContractMaintenance")
public class GeContractMaintenanceController {

    @Resource
    private GeContractMaintenanceService geContractMaintenanceService;

    @ApiOperation("新增合同数据")
    @PostMapping("insertContract")
    public DataResult<Void> insertContract(@RequestBody GeContractMaintenanceDTO param) {
        return geContractMaintenanceService.insertContract(param);
    }

    @ApiOperation("删除合同数据")
    @PostMapping("deleteContract")
    public DataResult<Void> deleteContract(@RequestBody IdDTO param) {
        return geContractMaintenanceService.deleteContract(param);
    }

    @ApiOperation("合同数据列表")
    @PostMapping("contractList")
    public DataResult<List<GeContractMaintenanceVO>> contractList(@RequestBody GeContractMaintenanceDTO param) {
        return geContractMaintenanceService.contractList(param);
    }

    @ApiOperation("导入机组实际发电量、功率预测、节点价格")
    @PostMapping("importUnitRealElectricityNodePower")
    public DataResult<Void> importUnitRealElectricityNodePower(@RequestPart("file") MultipartFile file, @RequestParam("type") Integer type) {
        return geContractMaintenanceService.importUnitRealElectricityNodePower(file, type);
    }

}
