package com.fx.green.electricity.shanxi.server.controller.unit;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeUnitBasicDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeUnitBasicVO;
import com.fx.green.electricity.shanxi.server.service.unit.GeUnitBasicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 机组信息维护
 */
@Api(tags = "机组信息维护")
@RestController
@RequestMapping("geUnitBasic")
public class GeUnitBasicController {

    @Autowired
    private GeUnitBasicService geUnitBasicService;

    /**
     * 新增机组信息
     *
     * @param param
     */
    @ApiOperation("新增机组信息")
    @PostMapping("insertUnitBase")
    public DataResult<Void> insertUnitBase(@RequestBody GeUnitBasicDTO param) {
        return geUnitBasicService.insertUnitBase(param);
    }

    /**
     * 删除机组信息
     *
     * @param param
     */
    @ApiOperation("删除机组信息")
    @PostMapping("deleteUnitBase")
    public DataResult<Void> deleteUnitBase(@RequestBody IdDTO param) {
        return geUnitBasicService.deleteUnitBase(param);
    }

    /**
     * 根据租户id查询对应机组
     *
     * @param param
     * @return
     */
    @ApiOperation("根据租户id查询对应机组")
    @PostMapping("unitBaseList")
    public DataResult<List<GeUnitBasicVO>> unitBaseList(@RequestBody IdDTO param) {
        return geUnitBasicService.unitBaseList(param);
    }
}
