package com.fx.green.electricity.shanxi.server.controller.production;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.production.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionDataDeviceListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatisticsListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatisticsVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionStatusVO;
import com.fx.green.electricity.shanxi.server.service.production.VppUserProductionStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 用户生产统计 Controller
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "用户生产统计 - 用户生产统计")
@RequestMapping("/VppUserProductionStatistics")
public class VppUserProductionStatisticsController {

    @Resource
    private VppUserProductionStatisticsService vppUserProductionStatisticsService;

    @ApiOperation("根据用户id查询用户生产计划")
    @PostMapping("getProductionStatisticsByUserId")
    public DataResult<VppUserProductionStatisticsVO> getProductionStatisticsByUserId(@RequestBody @Valid VppUserProductionStatisticsQueryDetailDTO param) {
        return vppUserProductionStatisticsService.getProductionStatisticsByUserId(param);
    }

    @ApiOperation("查询某一天的用户生产计划")
    @PostMapping("getProductionStatisticsByDay")
    public DataResult<VppUserProductionStatisticsListVO> getProductionStatisticsByDay(@RequestBody @Valid VppUserProductionStatisticsQueryDTO param) {
        return vppUserProductionStatisticsService.getProductionStatisticsByDay(param);
    }

    @ApiOperation("根据生产计划类型获取设备列表")
    @PostMapping("getDeviceListByType")
    public DataResult<List<VppUserProductionDataDeviceListVO>> getDeviceListByType(@RequestBody @Valid VppUserProductionDataDeviceQueryDTO param) {
        return vppUserProductionStatisticsService.getDeviceListByType(param);
    }

    @CommonNoRepeat
    @ApiOperation("添加/修改用户生产计划")
    @PostMapping("saveUserProductionPlan")
    public DataResult<Void> saveUserProductionPlan(@RequestBody @Valid VppUserProductionStatisticsDTO param) {
        return vppUserProductionStatisticsService.saveUserProductionPlan(param);
    }

    @ApiOperation("获取用户的生产状态列表")
    @PostMapping("getUserProductionStatus")
    public DataResult<List<VppUserProductionStatusVO>> getUserProductionStatus(@RequestBody @Valid VppUserProductionStatusQueryDTO param) {
        return vppUserProductionStatisticsService.getUserProductionStatus(param);
    }

}
