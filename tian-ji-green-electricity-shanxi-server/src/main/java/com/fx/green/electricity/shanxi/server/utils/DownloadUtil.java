package com.fx.green.electricity.shanxi.server.utils;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.AbstractRowHeightStyleStrategy;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.enums.VppRetailUserPriceModeEnum;
import com.fx.green.electricity.shanxi.api.enums.VppServiceCodeEnum;
import io.netty.util.CharsetUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;

/**
 * 文件下载工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class DownloadUtil {


    public static void download(String fileName, byte[] fileBytes, HttpServletResponse response) {
        try {
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncodeUtil.encode(fileName) + "\"");
            response.addHeader("Content-Length", "" + fileBytes.length);
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("filename", URLEncodeUtil.encode(fileName));
            response.setContentType("application/octet-stream;charset=UTF-8");
            IoUtil.write(response.getOutputStream(), true, fileBytes);
        } catch (IOException e) {
            log.error("下载文件异常，具体信息为：{}", e.getMessage());
            throw new FxServiceException(VppServiceCodeEnum.DOWNLOAD_FILE_ERROR);

        }

    }

    public static void downloadJsonType(String fileName, byte[] fileBytes, HttpServletResponse response) {
        try {
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncodeUtil.encode(fileName) + "\"");
            response.addHeader("Content-Length", "" + fileBytes.length);
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("filename", URLEncodeUtil.encode(fileName));
            response.setContentType("application/json;charset=UTF-8");
            IoUtil.write(response.getOutputStream(), true, fileBytes);
        } catch (IOException e) {
            log.error(">>> 下载文件异常，具体信息为：{}", e.getMessage());
            throw new FxServiceException(VppServiceCodeEnum.DOWNLOAD_FILE_ERROR);

        }

    }

    public static void downloadDynamicHeadExcel(String fileName, List<List<String>> heads, List<List<String>> dataList
            , List<CellWriteHandler> cellWriteHandlerList, AbstractRowHeightStyleStrategy rowHeightStyleStrategy, HttpServletResponse response, List<CustomMergeStrategy> customMergeStrategyList) {
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncodeUtil.encode(fileName) + ".xlsx");
            response.setHeader("fileName", URLEncodeUtil.encode(fileName) + ".xlsx");
            response.setContentType("application/json");
            ExcelWriterBuilder builder = EasyExcelFactory.write(outputStream).head(heads);
            builder.registerWriteHandler(new AbstractColumnWidthStyleStrategy() {
                @Override
                protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<CellData> list, Cell cell, Head head, Integer integer, Boolean aBoolean) {
                    Sheet sheet = writeSheetHolder.getSheet();
                    sheet.setColumnWidth(cell.getColumnIndex(), 5000);
                }
            }).registerWriteHandler(rowHeightStyleStrategy);
            if (ObjectUtil.isNotEmpty(customMergeStrategyList)) {
                for (CustomMergeStrategy customMergeStrategy : customMergeStrategyList) {
                    builder.registerWriteHandler(customMergeStrategy);
                }
            }
            if (ObjectUtil.isNotEmpty(cellWriteHandlerList)) {
                for (CellWriteHandler cellWriteHandler : cellWriteHandlerList) {
                    builder.registerWriteHandler(cellWriteHandler);
                }
            }
            //automaticMergeHead禁用easyExcle自动合并相同表格内容
            builder.automaticMergeHead(false).sheet("sheet0").doWrite(dataList);
        } catch (Exception e) {
            log.error(">> 下载文件异常，具体信息为：{}", e.getMessage());
            throw new FxServiceException(VppServiceCodeEnum.DOWNLOAD_FILE_ERROR);
        }
    }

    public static void downloadDynamicHeadExcel(List<List<String>> heads, List<List<String>> dataList
            , List<CellWriteHandler> cellWriteHandlerList, AbstractRowHeightStyleStrategy rowHeightStyleStrategy, OutputStream outputStream, List<CustomMergeStrategy> customMergeStrategyList) {

        ExcelWriterBuilder builder = EasyExcelFactory.write(outputStream).head(heads);
        builder.registerWriteHandler(new AbstractColumnWidthStyleStrategy() {
            @Override
            protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<CellData> list, Cell cell, Head head, Integer integer, Boolean aBoolean) {
                Sheet sheet = writeSheetHolder.getSheet();
                sheet.setColumnWidth(cell.getColumnIndex(), 5000);
            }
        }).registerWriteHandler(rowHeightStyleStrategy);
        if (ObjectUtil.isNotEmpty(customMergeStrategyList)) {
            for (CustomMergeStrategy customMergeStrategy : customMergeStrategyList) {
                builder.registerWriteHandler(customMergeStrategy);
            }
        }
        if (ObjectUtil.isNotEmpty(cellWriteHandlerList)) {
            for (CellWriteHandler cellWriteHandler : cellWriteHandlerList) {
                builder.registerWriteHandler(cellWriteHandler);
            }
        }
        //automaticMergeHead禁用easyExcle自动合并相同表格内容
        builder.automaticMergeHead(false).sheet("sheet0").doWrite(dataList);
    }

    /**
     * 下载文件
     *
     * @param file     要下载的文件
     * @param response 响应
     * <AUTHOR>
     */
    public static void download(File file, HttpServletResponse response) {
        // 获取文件字节
        byte[] fileBytes = FileUtil.readBytes(file);
        //获取文件名称
        String fileName;
        try {
            fileName = URLEncoder.encode(file.getName(), String.valueOf(CharsetUtil.UTF_8));
        } catch (UnsupportedEncodingException e) {
            log.error(">>>  下载文件异常，具体信息为：{}", e.getMessage());
            throw new FxServiceException(VppServiceCodeEnum.DOWNLOAD_FILE_ERROR);
        }
        //下载文件
        download(fileName, fileBytes, response);
    }

    /**
     * 将url的文件下载到目标文件
     *
     * @param url  下载url
     * @param file 目标文件
     * <AUTHOR>
     */
    public static void downloadToFile(String url, File file) {
        if (url == null || url.isEmpty()) {
            throw new FxServiceException(VppServiceCodeEnum.DOWNLOAD_FILE_ERROR);
        }
        if (file == null) {
            throw new FxServiceException(VppServiceCodeEnum.DOWNLOAD_FILE_ERROR);
        }

        try {
            URL uri = new URL(url);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) uri.openConnection();
            InputStream stream = connection.getInputStream();

            if (stream == null) {
                throw new FxServiceException(VppServiceCodeEnum.DOWNLOAD_FILE_ERROR);
            }
            FileUtil.writeFromStream(stream, file);
            connection.disconnect();
        } catch (Exception e) {
            log.error(">>> 下载文件异常，具体信息为：{}", e.getMessage());
            throw new FxServiceException(VppServiceCodeEnum.DOWNLOAD_FILE_ERROR);
        }
    }

    /**
     * 自定义下载模板
     *
     * @param fileName
     * @param heads
     * @param dataList
     * @param cellWriteHandlerList
     * @param rowHeightStyleStrategy
     * @param response
     * @param customMergeStrategyList
     * @param pricingType
     */
    public static void retailContractDownloadDynamicHeadExcel(String fileName, List<List<String>> heads, List<List<String>> dataList
            , List<CellWriteHandler> cellWriteHandlerList, AbstractRowHeightStyleStrategy rowHeightStyleStrategy, HttpServletResponse response, List<CustomMergeStrategy> customMergeStrategyList, Integer pricingType) {
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncodeUtil.encode(fileName) + ".xlsx");
            response.setHeader("fileName", URLEncodeUtil.encode(fileName) + ".xlsx");
            response.setContentType("application/json");
            ExcelWriterBuilder builder = EasyExcelFactory.write(outputStream).head(heads);

            // 设置表头样式队列【先进先出】
            ArrayBlockingQueue<ComplexHeadStyles> complexHeadStylesArrayBlockingQueue = new ArrayBlockingQueue<>(heads.size());
            /**
             *    (1,i）背景色为白色
             *    写Excel是一行一行写的，因此入队顺序是这样
             */
            for (int i = 0; i < heads.size(); i++) {
                if (pricingType == VppRetailUserPriceModeEnum.TIME_SHARING_PRICE_CONTRACT_ELECTRICITY.getCode()) {
                    //分时约定结算(约定电量)
                    if (ObjectUtil.isNull(heads.get(i).get(1)) || ObjectUtil.isEmpty(heads.get(i).get(1))) {
                        complexHeadStylesArrayBlockingQueue.add(new ComplexHeadStyles(1, i, IndexedColors.WHITE.getIndex()));
                    }
                } else if (pricingType == VppRetailUserPriceModeEnum.CENTRALIZED_BIDDING_CONTRACT_ELECTRICITY.getCode()
                        || pricingType == VppRetailUserPriceModeEnum.MONTH_AND_TEN_DAYS_CENTRALIZED_BIDDING_CONTRACT_ELECTRICITY.getCode()) {
                    //月度集中竞价+价差分时约定结算(约定电量) 月度及各旬集中竞价按日历天数加权出清电价+价差（约定电量）
                    complexHeadStylesArrayBlockingQueue.add(new ComplexHeadStyles(2, i + 1, IndexedColors.WHITE.getIndex()));
                } else if (pricingType == VppRetailUserPriceModeEnum.CENTRALIZED_BIDDING.getCode()
                        || pricingType == VppRetailUserPriceModeEnum.MONTH_AND_TEN_DAYS_CENTRALIZED_BIDDING.getCode()) {
                    //月度集中竞价+价差分时约定(不约定电量)  月度及各旬集中竞价按日历天数加权出清电价+价差（不约定电量）
                    complexHeadStylesArrayBlockingQueue.add(new ComplexHeadStyles(1, i + 1, IndexedColors.WHITE.getIndex()));
                }
            }

            // 自定义表头策略
            HeadStyleWriteHandler headStyleWriteHandler = new HeadStyleWriteHandler(complexHeadStylesArrayBlockingQueue);

            builder.registerWriteHandler(new RetailContractExcelCellWidthStyleStrategy()).registerWriteHandler(rowHeightStyleStrategy).registerWriteHandler(headStyleWriteHandler);

            if (ObjectUtil.isNotEmpty(customMergeStrategyList)) {
                for (CustomMergeStrategy customMergeStrategy : customMergeStrategyList) {
                    builder.registerWriteHandler(customMergeStrategy);
                }
            }
            if (ObjectUtil.isNotEmpty(cellWriteHandlerList)) {
                for (CellWriteHandler cellWriteHandler : cellWriteHandlerList) {
                    builder.registerWriteHandler(cellWriteHandler);
                }
            }
            //automaticMergeHead禁用easyExcle自动合并相同表格内容
            builder.automaticMergeHead(false).sheet("sheet0").doWrite(dataList);
        } catch (Exception e) {
            log.error(">>> 下载文件异常，具体信息为：{}", e.getMessage());
            throw new FxServiceException("OperatorServiceCodeEnum.DOWNLOAD_FILE_ERROR");
        }
    }
}

