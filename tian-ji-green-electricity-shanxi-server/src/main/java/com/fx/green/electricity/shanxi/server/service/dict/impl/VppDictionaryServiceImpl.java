package com.fx.green.electricity.shanxi.server.service.dict.impl;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.api.dict.VppDictApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.GetVppDictionaryDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.dictionary.VppDictionaryVO;
import com.fx.green.electricity.shanxi.server.service.dict.VppDictionaryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 字典 Service 实现类
 *
 * <AUTHOR>
 **/
@Service
public class VppDictionaryServiceImpl implements VppDictionaryService {

    @Resource
    private VppDictApi vppDictApi;

    @Override
    public DataResult<List<VppDictionaryVO>> getDictionary(GetVppDictionaryDTO param) {
        return vppDictApi.getDictionary(param);
    }
}
