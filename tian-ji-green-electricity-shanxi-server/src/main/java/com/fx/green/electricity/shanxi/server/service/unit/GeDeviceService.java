package com.fx.green.electricity.shanxi.server.service.unit;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeDeviceSaveDTO;

/**
 * 绿电设备信息
 */
public interface GeDeviceService {

    /**
     * 新增设备信息
     *
     * @param param
     */
    DataResult<Void> insertDevice(GeDeviceSaveDTO param);

    /**
     * 删除设备
     *
     * @param param
     */
    DataResult<Void> deleteDevice(IdDTO param);
}
