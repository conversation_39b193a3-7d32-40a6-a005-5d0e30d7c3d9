package com.fx.green.electricity.shanxi.server.service.retail;


import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsPriceQueryVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsPriceVO;

import java.util.List;

/**
*
**/
public interface VppRetailContractsPriceService {

    DataResult<List<VppRetailContractsPriceVO>> getPriceList(VppRetailContractsPriceQueryVO param);

    DataResult<Void> dataDerive(VppRetailContractsPriceQueryVO param);
}
