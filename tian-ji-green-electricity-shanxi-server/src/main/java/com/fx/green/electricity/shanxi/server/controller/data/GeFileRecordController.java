package com.fx.green.electricity.shanxi.server.controller.data;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeFileRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeDayRecordVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO;
import com.fx.green.electricity.shanxi.server.service.data.GeFileRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 文件记录数据 Controller
 *
 */
@RestController
@Api(tags = "数据维护 - 文件记录数据")
@RequestMapping("/geFileRecord")
public class GeFileRecordController {

    @Resource
    private GeFileRecordService geFileRecordService;

    @ApiOperation("文件维护记录")
    @PostMapping("getFileRecordByDay")
    public DataResult<List<GeDayRecordVO>> getFileRecordByDay(@RequestBody GeFileRecordDTO param) {
        return geFileRecordService.getFileRecordByDay(param);
    }

    @ApiOperation("文件列表状态")
    @PostMapping("fileStatus")
    public DataResult<List<GeFileRecordVO>> fileStatus(@RequestBody GeFileRecordDTO param) {
        return geFileRecordService.fileStatus(param);
    }

    @ApiOperation("获取当日下数据")
    @PostMapping("getInfoDataValue")
    public DataResult<Map<String, String>> getInfoDataValue(@RequestBody GeFileRecordDTO param) {
        return geFileRecordService.getInfoDataValue(param);
    }
}
