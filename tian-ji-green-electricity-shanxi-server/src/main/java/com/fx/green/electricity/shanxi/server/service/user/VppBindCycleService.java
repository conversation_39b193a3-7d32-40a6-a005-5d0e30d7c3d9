package com.fx.green.electricity.shanxi.server.service.user;


import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserBindCycleDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserBindCycleVO;

import java.util.List;

/**
 * 绑定周期 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppBindCycleService {

    /**
     * 添加绑定周期
     *
     * @param param 绑定周期DTO
     * @return 成功/失败
     */
    DataResult<Void> addVppBindCycle(VppUserBindCycleDTO param);

    /**
     * 获取列表
     *
     * @param param 绑定周期id
     * @return 绑定周期列表
     */
    DataResult<List<VppUserBindCycleVO>> selectList(IdDTO param);

    /**
     * 删除
     *
     * @param param 绑定周期id
     * @return 成功/失败
     */
    DataResult<Void> delete(IdDTO param);
}
