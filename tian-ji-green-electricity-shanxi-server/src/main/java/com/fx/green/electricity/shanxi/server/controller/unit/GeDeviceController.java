package com.fx.green.electricity.shanxi.server.controller.unit;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeDeviceSaveDTO;
import com.fx.green.electricity.shanxi.server.service.unit.GeDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备信息维护
 */
@Api(tags = "设备信息维护")
@RestController
@RequestMapping("geDevice")
public class GeDeviceController {

    @Autowired
    private GeDeviceService geDeviceService;

    /**
     * 新增设备信息
     *
     * @param param
     */
    @ApiOperation("新增设备信息")
    @PostMapping("insertDevice")
    public DataResult<Void> insertDevice(@RequestBody GeDeviceSaveDTO param) {
        return geDeviceService.insertDevice(param);
    }

    /**
     * 删除设备
     *
     * @param param
     */
    @ApiOperation("删除设备")
    @PostMapping("deleteDevice")
    public DataResult<Void> deleteDevice(@RequestBody IdDTO param) {
        return geDeviceService.deleteDevice(param);
    }
}
