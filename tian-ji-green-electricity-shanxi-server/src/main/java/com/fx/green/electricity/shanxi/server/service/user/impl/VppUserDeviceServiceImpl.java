package com.fx.green.electricity.shanxi.server.service.user.impl;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.api.user.VppUserDeviceApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDeviceAndMeterDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserDeviceAndMeterVO;
import com.fx.green.electricity.shanxi.server.service.user.VppUserDeviceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 用户设备及电表管理 Service 实现类
 *
 * <AUTHOR>
 **/
@Service
public class VppUserDeviceServiceImpl implements VppUserDeviceService {

    @Resource
    private VppUserDeviceApi vppUserDeviceApi;

    @Override
    public DataResult<Void> saveOrUpdate(VppUserDeviceAndMeterDTO param) {
        return vppUserDeviceApi.saveOrUpdate(param);
    }

    @Override
    public DataResult<VppUserDeviceAndMeterVO> getUserDeviceAndMeterByConsNo(VppUserDeviceAndMeterDTO.VppUserDeviceDTO param) {
        return vppUserDeviceApi.getUserDeviceAndMeterByConsNo(param);
    }
}
