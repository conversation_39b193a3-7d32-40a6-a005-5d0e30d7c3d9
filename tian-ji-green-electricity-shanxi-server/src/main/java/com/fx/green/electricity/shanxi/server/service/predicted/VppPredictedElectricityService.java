package com.fx.green.electricity.shanxi.server.service.predicted;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.QueryPredictedElectricityContrastVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.QueryPredictedElectricityVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.VppPredictedExcludeDateVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.VppPredictedReferenceDateVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;

import javax.servlet.http.HttpServletResponse;

/**
 *<AUTHOR>
 **/
public interface VppPredictedElectricityService {

    DataResult<VppLoadUserVO.TreeVO> queryTreeListNew(QueryTreeListDTO param);

    DataResult<QueryPredictedElectricityVO> queryPredictedElectricity(QueryPredictedElectricityDTO param);

    DataResult<VppPredictedReferenceDateVO> getReferenceDate(QueryDateOneDTO param);

    DataResult<Void> saveOrUpdateElectricity(UpdatePredictedElectricityDTO param);

    DataResult<Void> confirmPrediction(ConfirmPredictionDTO param);

    DataResult<Void> resetPrediction(ResetPredictedElectricityDTO param);

    DataResult<Void> exportData(QueryPredictedElectricityDTO param, HttpServletResponse response);

    DataResult<QueryPredictedElectricityContrastVO> getPredictedContrastData(QueryPredictedElectricityDTO param);

    DataResult<Void> exportContrastData(QueryPredictedElectricityDTO param, HttpServletResponse response);

    DataResult<VppPredictedExcludeDateVO> getExcludeDay(QueryDateOneDTO param);
}
