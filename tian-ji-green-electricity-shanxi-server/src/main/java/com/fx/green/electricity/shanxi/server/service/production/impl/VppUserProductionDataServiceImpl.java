package com.fx.green.electricity.shanxi.server.service.production.impl;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.api.production.VppUserProductionDataApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.production.VppUserProductionDataDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionDataVO;
import com.fx.green.electricity.shanxi.server.service.production.VppUserProductionDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 用户生产数据 Service 实现类
 *
 * <AUTHOR>
 **/
@Service
public class VppUserProductionDataServiceImpl implements VppUserProductionDataService {

    @Resource
    private VppUserProductionDataApi vppUserProductionDataApi;


    @Override
    public void saveOrUpdate(VppUserProductionDataDTO param) {
        vppUserProductionDataApi.saveOrUpdate(param);
    }

    @Override
    public DataResult<VppUserProductionDataVO> getProductionDataByUserId(IdDTO param) {
        return vppUserProductionDataApi.getProductionDataByUserId(param);
    }
}
