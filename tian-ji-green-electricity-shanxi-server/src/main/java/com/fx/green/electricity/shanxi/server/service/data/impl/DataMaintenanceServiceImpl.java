package com.fx.green.electricity.shanxi.server.service.data.impl;

import com.fx.common.constant.DataResult;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.api.api.data.DataMaintenanceApi;
import com.fx.green.electricity.shanxi.api.api.data.VppRetailContractsApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ExpireUserVO;
import com.fx.green.electricity.shanxi.server.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.server.service.data.VppElectricQuantityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据维护 Service 实现类
 */
@Service
@Slf4j
public class DataMaintenanceServiceImpl implements DataMaintenanceService {

    @Resource
    private DataMaintenanceApi dataMaintenanceApi;

    @Resource
    private VppRetailContractsApi vppRetailContractsApi;

    @Resource
    private VppElectricQuantityService vppElectricQuantityService;


    @Override
    public DataResult<List<DataMaintenanceVO>> queryList(DataMaintenanceDTO param) {
        return dataMaintenanceApi.queryList(param);
    }

    @Override
    public DataResult<List<ExpireUserVO>> getExpireUserList(DataMaintenanceDTO param) {
        return dataMaintenanceApi.getExpireUserList(param);
    }

    @Override
    public DataResult<Integer> geUserCount(DataMaintenanceDTO param) {
        return dataMaintenanceApi.geUserCount(param);
    }

    @Override
    public DataResult<Void> importRetailContract(MultipartFile file, String runMonth) {
        return vppRetailContractsApi.importRetailContract(file, runMonth);
    }

    @Override
    public DataResult<Void> updateStatus(DataMaintenanceUpdateDTO param) {
        return dataMaintenanceApi.updateStatus(param);
    }

    @Override
    public DataResult<ImportExcelVO> importActualElectricityConsumption(MultipartFile file) {
        return dataMaintenanceApi.importActualElectricityConsumption(file);
    }

    @Override
    public DataResult<List<ElectricityDetailVO>> getActualElectricityConsumptionDetail(DataMaintenanceQueryDTO param) {
        return dataMaintenanceApi.getActualElectricityConsumptionDetail(param);
    }

    @Override
    public DataResult<Void> deleteActualElectricityConsumption(DataMaintenanceQueryDTO param) {
        return dataMaintenanceApi.deleteActualElectricityConsumption(param);
    }

}
