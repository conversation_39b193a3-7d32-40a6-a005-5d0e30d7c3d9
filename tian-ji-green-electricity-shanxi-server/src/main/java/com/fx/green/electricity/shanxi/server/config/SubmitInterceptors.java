package com.fx.green.electricity.shanxi.server.config;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.ContentType;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fx.auth.config.FxAuthProperties;
import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 */
@Configuration
public class SubmitInterceptors implements WebMvcConfigurer {

    @Autowired
    private FxAuthProperties fxAuthProperties;

    private static final String REPEAT_PARAM = "repeatParam";

    private static final String REPEAT_TIME = "repeatTime";

    private static final String REPEAT_DATA = "repeatData";

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        /**
         * 节流防抖
         */
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
                                     @NonNull Object handler) throws Exception {
                if (handler instanceof HandlerMethod) {
                    HandlerMethod handlerMethod = (HandlerMethod) handler;
                    Method method = handlerMethod.getMethod();
                    CommonNoRepeat annotation = method.getAnnotation(CommonNoRepeat.class);
                    if (ObjectUtil.isNotEmpty(annotation) && (this.isRepeatSubmit(request, annotation))) {
                            response.setCharacterEncoding(CharsetUtil.UTF_8);
                            response.setContentType(ContentType.JSON.toString());
                            response.getWriter().write(JSONUtil.toJsonStr(DataResult.failed("请求过于频繁，请稍后再试")));
                            return false;

                    }
                }
                return true;
            }

            public boolean isRepeatSubmit(HttpServletRequest request, CommonNoRepeat annotation) {
                JSONObject jsonObject = JSONUtil.createObj();
                jsonObject.set(REPEAT_PARAM, JSONUtil.toJsonStr(request.getParameterMap()));
                jsonObject.set(REPEAT_TIME, DateUtil.current());
                String url = request.getRequestURI();
                HttpSession session = request.getSession();
                Object sessionObj = session.getAttribute(REPEAT_DATA);
                if (ObjectUtil.isNotEmpty(sessionObj)) {
                    JSONObject sessionJsonObject = JSONUtil.parseObj(sessionObj);
                    if(sessionJsonObject.containsKey(url)) {
                        JSONObject existRepeatJsonObject = sessionJsonObject.getJSONObject(url);
                        if (jsonObject.getStr(REPEAT_PARAM).equals(existRepeatJsonObject.getStr(REPEAT_PARAM)) &&
                                jsonObject.getLong(REPEAT_TIME) - existRepeatJsonObject.getLong(REPEAT_TIME) < annotation.interval()) {
                            return true;
                        }
                    }
                }
                session.setAttribute(REPEAT_DATA, JSONUtil.createObj().set(url, jsonObject));
                return false;
            }
        }).addPathPatterns("/**");
    }

}
