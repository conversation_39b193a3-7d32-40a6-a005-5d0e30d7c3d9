package com.fx.green.electricity.shanxi.server.service.production;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.production.VppUserProductionDataDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionDataVO;

/**
 * 用户生产数据 Service 接口
 *
 * <AUTHOR>
 **/
public interface VppUserProductionDataService {

    /**
     * 添加/修改用户生产数据
     *
     * @param param 用户生产数据DTO
     */
    void saveOrUpdate(VppUserProductionDataDTO param);

    /**
     * 根据用户id查询用户生产数据
     *
     * @param param 用户id
     * @return 用户生产数据VO
     */
    DataResult<VppUserProductionDataVO> getProductionDataByUserId(IdDTO param);

}
