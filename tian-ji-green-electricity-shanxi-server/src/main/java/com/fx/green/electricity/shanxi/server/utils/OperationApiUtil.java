package com.fx.green.electricity.shanxi.server.utils;

import com.fx.common.constant.DataResult;
import com.fx.common.enums.CommonCodeEnum;
import com.fx.operation.api.api.OperationTenantApi;
import com.fx.operation.api.vo.TenantVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 运营平台接口
 */
@Component
@Slf4j
public class OperationApiUtil {

    @Autowired
    private OperationTenantApi operationTenantApi;

    /**
     * 获取绿电直连公司和机组
     *
     * @return
     */
    public List<TenantVO> getTenantUnitList() {
        DataResult<List<TenantVO>> dataResult = operationTenantApi.getGreenLinkTenant();
        log.debug("【运营平台】获取绿电直连公司和机组返参：{}", dataResult);
        if (null != dataResult && CommonCodeEnum.SUCCESS.getCode() == dataResult.getStatus() && null != dataResult.getData()) {
            return dataResult.getData();
        }
        return Collections.emptyList();
    }


}
