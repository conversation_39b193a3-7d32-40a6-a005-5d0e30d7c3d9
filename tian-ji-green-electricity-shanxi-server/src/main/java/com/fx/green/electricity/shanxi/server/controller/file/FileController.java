package com.fx.green.electricity.shanxi.server.controller.file;


import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.vo.file.VppFileUploadVO;
import com.fx.green.electricity.shanxi.server.service.file.VppFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文件上传 Controller
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "文件管理 - 文件上传")
@RequestMapping("/file")
public class FileController {

    @Resource
    private VppFileService vppFileService;

    @ApiOperation("上传文件")
    @PostMapping("/uploadFile")
    public DataResult<VppFileUploadVO> uploadFile(@RequestPart("file") MultipartFile file) {
        return vppFileService.uploadFile(file);
    }

    @ApiOperation("上传多个文件")
    @PostMapping("/uploadFiles")
    public DataResult<List<VppFileUploadVO>> uploadFiles(@RequestPart("files") MultipartFile[] files) {
        return vppFileService.uploadFiles(files);
    }

}

