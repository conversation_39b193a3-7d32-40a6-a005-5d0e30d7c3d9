package com.fx.green.electricity.shanxi.server.controller.retail;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import com.fx.green.electricity.shanxi.server.service.retail.VppRetailContractsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * 零售
 * <AUTHOR>
 **/
@Api(tags = "零售Controller")
@RestController
@RequestMapping("vppRetailContractsController")
public class VppRetailContractsController {


    @Autowired
    private VppRetailContractsService vppRetailContractsService;


    @ApiOperation("获取零售列表")
    @PostMapping("list")
    public DataResult<FxPage<VppRetailContractsVO>> getList(@RequestBody QueryVppRetailContractsDTO param) {
        return vppRetailContractsService.getList(param);
    }

    @ApiOperation("零售合同导入")
    @PostMapping("queryImport")
    public DataResult<Void> queryImportRecord(@RequestPart("file") MultipartFile file,
                                              @ApiParam("运行月份") String runMonth,
                                              @ApiParam("id") Long retailContractsId, HttpServletRequest request) {
        String tenantId = request.getHeader("tenantId");
        return vppRetailContractsService.queryImportRecord(file, runMonth, retailContractsId, tenantId);
    }

}
