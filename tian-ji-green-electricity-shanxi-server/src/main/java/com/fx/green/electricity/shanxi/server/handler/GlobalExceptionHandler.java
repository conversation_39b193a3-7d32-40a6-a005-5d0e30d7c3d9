package com.fx.green.electricity.shanxi.server.handler;

import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.fx.common.constant.DataResult;
import com.fx.common.enums.CommonCodeEnum;
import com.fx.common.exception.FxAccessException;
import com.fx.common.exception.FxAuthException;
import com.fx.common.exception.FxServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 */

@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ResponseBody
    @ExceptionHandler(FlowException.class)
    public DataResult<String> flowException() {
        return DataResult.failed(CommonCodeEnum.TO_OFTEN);
    }

    @ResponseBody
    @ExceptionHandler(FxAuthException.class)
    public DataResult<String> fxAuthException() {
        return DataResult.failed(CommonCodeEnum.UN_AUTHORIZED);
    }

    @ResponseBody
    @ExceptionHandler(FxAccessException.class)
    public DataResult<String> fxAccessException() {
        return DataResult.failed(CommonCodeEnum.NO_ACCESS);
    }

    @ResponseBody
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public DataResult<String> httpRequestMethodNotSupportedException() {
        return DataResult.failed(CommonCodeEnum.METHOD_NOT_ALLOWED);
    }

    @ResponseBody
    @ExceptionHandler(FxServiceException.class)
    public DataResult<String> fxServiceException(FxServiceException e) {
        return DataResult.failed(e.getCode(), e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(ExcelAnalysisException.class)
    public DataResult<String> excelAnalysisException(ExcelAnalysisException e) {
        log.error("异常信息: ", e);
        return DataResult.failed(CommonCodeEnum.FAIL.getCode(), e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(IllegalStateException.class)
    public DataResult<String> illegalStateException(IllegalStateException e) {
        log.error(" 异常信息:", e);
        return DataResult.failed(CommonCodeEnum.FAIL.getCode(), e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public DataResult<String> httpMessageNotReadableException(Exception e) {
        log.error("异常信息:==", e);
        return DataResult.failed(CommonCodeEnum.PARAM_ERROR);
    }

    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public DataResult<String> httpMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error("异常信息:===", e);
        return DataResult.failed(e.getBindingResult().getAllErrors().get(0).getDefaultMessage());
    }

    @ResponseBody
    @ExceptionHandler(Exception.class)
    public DataResult<String> exception(Exception e) {
        log.error("异常信息:====", e);
        return DataResult.failed(CommonCodeEnum.FAIL);
    }
}
