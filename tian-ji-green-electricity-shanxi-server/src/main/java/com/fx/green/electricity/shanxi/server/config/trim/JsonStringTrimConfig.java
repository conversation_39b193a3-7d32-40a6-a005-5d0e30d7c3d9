package com.fx.green.electricity.shanxi.server.config.trim;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdScalarDeserializer;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 仅对json请求处理
 * <AUTHOR>
 */
@Component
public class JsonStringTrimConfig extends SimpleModule {
    private static final long serialVersionUID = -2692974852356867764L;

    public JsonStringTrimConfig() {
        addDeserializer(String.class, new StdScalarDeserializer<String>(String.class) {
            private static final long serialVersionUID = 2450292377307463168L;

            @Override
            public String deserialize(JsonParser jsonParser, DeserializationContext ctx) throws IOException {
                return jsonParser.getValueAsString().trim();
            }
        });
    }
}
