package com.fx.green.electricity.shanxi.server.service.data.impl;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.api.GeMaintenanceApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeFileRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeDayRecordVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO;
import com.fx.green.electricity.shanxi.server.service.data.GeFileRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 文件导入记录
 */
@Service
public class GeFileRecordServiceImpl implements GeFileRecordService {

    @Autowired
    private GeMaintenanceApi geMaintenanceApi;

    /**
     * 文件维护记录
     *
     * @param param
     * @return
     */
    @Override
    public DataResult<List<GeDayRecordVO>> getFileRecordByDay(GeFileRecordDTO param) {
        return geMaintenanceApi.getFileRecordByDay(param);
    }

    /**
     * 文件列表状态
     *
     * @param param
     * @return
     */
    @Override
    public DataResult<List<GeFileRecordVO>> fileStatus(GeFileRecordDTO param) {
        return geMaintenanceApi.fileStatus(param);
    }

    /**
     * 获取当日下数据
     *
     * @param param
     * @return
     */
    @Override
    public DataResult<Map<String, String>> getInfoDataValue(GeFileRecordDTO param) {
        return geMaintenanceApi.getInfoDataValue(param);
    }
}
