package com.fx.green.electricity.shanxi.server.service.user.impl;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.api.user.VppLoadUserApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.AdjustDeclareDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.QueryDateDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppLoadUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserInfoVO;
import com.fx.green.electricity.shanxi.server.service.user.VppLoadUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 负荷用户 Service 实现类
 *
 * <AUTHOR>
 **/
@Service
public class VppLoadUserServiceImpl implements VppLoadUserService {

    @Resource
    private VppLoadUserApi vppLoadUserApi;

    @Override
    public DataResult<Long> saveOrUpdate(VppLoadUserDTO param) {
        return vppLoadUserApi.vppLoadUserSaveOrUpdate(param);
    }

    @Override
    public DataResult<Void> delete(IdDTO param) {
        return vppLoadUserApi.delete(param);
    }

    @Override
    public DataResult<VppUserInfoVO> findById(IdDTO param) {
        return vppLoadUserApi.findById(param);
    }

    @Override
    public DataResult<FxPage<VppLoadUserVO>> pageList(VppUserDTO.SearchDTO param) {
        return vppLoadUserApi.pageList(param);
    }

    @Override
    public DataResult<Void> resetPassword(VppUserDTO param) {
        return vppLoadUserApi.resetPassword(param);
    }

    @Override
    public DataResult<VppLoadUserVO.TreeVO> queryTreeListByDate(QueryDateDTO param) {
        return vppLoadUserApi.queryTreeListByDate(param);
    }

    @Override
    public DataResult<VppLoadUserVO.TreeVO> queryTreeList(AdjustDeclareDTO.QueryUserTreeDTO param) {
        return vppLoadUserApi.queryTreeList(param);
    }
}
