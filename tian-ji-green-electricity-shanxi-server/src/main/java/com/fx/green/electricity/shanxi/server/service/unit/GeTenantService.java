package com.fx.green.electricity.shanxi.server.service.unit;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeTenantDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeTenantVO;
import com.fx.operation.api.vo.TenantVO;

import java.util.List;

/**
 * 维护租户数据
 */
public interface GeTenantService {

    /**
     * 获取绿电直连租户和机组
     *
     * @return
     */
    List<TenantVO> getGreenTenantUnit();

    /**
     * 新增绿电租户
     *
     * @param param
     */
    DataResult<Void> insertTenant(GeTenantDTO param);

    /**
     * 删除绿电租户
     *
     * @param param
     */
    DataResult<Void> deleteTenant(IdDTO param);

    /**
     * 绿电租户分页
     *
     * @param param
     * @return
     */
    DataResult<List<GeTenantVO>> tenantList(GeTenantDTO param);

    /**
     * 获取所有租户和机组对应信息
     *
     * @return
     */
    DataResult<List<GeTenantVO>> getTenantUnitDetail();
}
