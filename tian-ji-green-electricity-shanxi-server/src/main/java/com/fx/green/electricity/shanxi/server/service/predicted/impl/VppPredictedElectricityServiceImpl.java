package com.fx.green.electricity.shanxi.server.service.predicted.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.api.VppPredictedElectricityApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.QueryPredictedElectricityContrastVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.QueryPredictedElectricityVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.VppPredictedExcludeDateVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.VppPredictedReferenceDateVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.server.service.predicted.VppPredictedElectricityService;
import com.fx.green.electricity.shanxi.server.utils.DownloadUtil;
import com.fx.green.electricity.shanxi.server.utils.HttpServletUtil;
import com.fx.green.electricity.shanxi.server.utils.MergeSameRowsStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Service
public class VppPredictedElectricityServiceImpl implements VppPredictedElectricityService {

    @Resource
    private VppPredictedElectricityApi vppPredictedElectricityApi;

    @Override
    public DataResult<VppLoadUserVO.TreeVO> queryTreeListNew(QueryTreeListDTO param) {
        return vppPredictedElectricityApi.queryTreeListNew(param);
    }

    @Override
    public DataResult<QueryPredictedElectricityVO> queryPredictedElectricity(QueryPredictedElectricityDTO param) {
        return vppPredictedElectricityApi.queryPredictedElectricity(param);
    }

    @Override
    public DataResult<VppPredictedReferenceDateVO> getReferenceDate(QueryDateOneDTO param) {
        return vppPredictedElectricityApi.getReferenceDate(param);
    }

    @Override
    public DataResult<Void> saveOrUpdateElectricity(UpdatePredictedElectricityDTO param) {
        return vppPredictedElectricityApi.saveOrUpdateElectricity(param);
    }

    @Override
    public DataResult<Void> confirmPrediction(ConfirmPredictionDTO param) {
        return vppPredictedElectricityApi.confirmPrediction(param);
    }

    @Override
    public DataResult<Void> resetPrediction(ResetPredictedElectricityDTO param) {
        return vppPredictedElectricityApi.resetPrediction(param);
    }


    @Override
    public DataResult<QueryPredictedElectricityContrastVO> getPredictedContrastData(QueryPredictedElectricityDTO param) {
        return vppPredictedElectricityApi.getPredictedContrastData(param);
    }

    @Override
    public DataResult<Void> exportContrastData(QueryPredictedElectricityDTO param, HttpServletResponse response) {
        DataResult<QueryPredictedElectricityContrastVO> dataResult = vppPredictedElectricityApi.getPredictedContrastData(param);
        if (ObjectUtil.isNotNull(dataResult.getData())) {
            QueryPredictedElectricityContrastVO data = dataResult.getData();
            List<List<String>> header = headers(data);
            List<List<String>> formatTableDataList = this.formatTableDataLists(data);
            // 查询数据并处理格式
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            // easyExcel导出
            EasyExcelFactory.write(stream)
                    .head(header)
                    .registerWriteHandler(new MergeSameRowsStrategy(1, new int[]{0}))
                    .sheet("sheet")
                    .doWrite(formatTableDataList);
            byte[] fileBytes = stream.toByteArray();
            String fileName = DateUtil.formatDate(param.getQueryDate()) + "预测电量对比";
            DownloadUtil.downloadJsonType(fileName + ".xlsx", fileBytes, HttpServletUtil.getResponse());
        }
        return DataResult.success();
    }

    @Override
    public DataResult<VppPredictedExcludeDateVO> getExcludeDay(QueryDateOneDTO param) {
        return vppPredictedElectricityApi.getExcludeDay(param);
    }


    @Override
    public DataResult<Void> exportData(QueryPredictedElectricityDTO param, HttpServletResponse response) {
        DataResult<QueryPredictedElectricityVO> dataResult = vppPredictedElectricityApi.queryPredictedElectricity(param);
        if (ObjectUtil.isNotNull(dataResult.getData())) {
            QueryPredictedElectricityVO data = dataResult.getData();
            List<List<String>> header = header(data);
            List<List<String>> formatTableDataList = this.formatTableDataList(data);
            // 查询数据并处理格式
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            // easyExcel导出
            EasyExcelFactory.write(stream)
                    .head(header)
                    .registerWriteHandler(new MergeSameRowsStrategy(1, new int[]{0}))
                    .sheet("sheet")
                    .doWrite(formatTableDataList);
            byte[] fileBytes = stream.toByteArray();
            String fileName = DateUtil.formatDate(param.getQueryDate()) + "预测电量";
            DownloadUtil.downloadJsonType(fileName + ".xlsx", fileBytes, HttpServletUtil.getResponse());
        }

        return DataResult.success();
    }


    private List<List<String>> header(QueryPredictedElectricityVO data) {
        List<List<String>> headers = new ArrayList<>();
        headers.add(Collections.singletonList("用户名称"));
        headers.add(Collections.singletonList("预测类型"));
        headers.add(Collections.singletonList("总电量"));
        List<String> list = data.getTimeFrame();
        if (ObjectUtil.isNotEmpty(list)) {
            for (String s : list) {
                headers.add(Collections.singletonList(s));
            }
        }
        return headers;
    }

    private List<List<String>> headers(QueryPredictedElectricityContrastVO data) {
        List<List<String>> headers = new ArrayList<>();
        headers.add(Collections.singletonList("用户名称"));
        headers.add(Collections.singletonList("类型"));
        headers.add(Collections.singletonList("总电量(准确率)"));
        List<String> list = data.getTimeFrame();
        if (ObjectUtil.isNotEmpty(list)) {
            for (String s : list) {
                headers.add(Collections.singletonList(s + "(准确率)"));
            }
        }
        return headers;
    }

    private List<List<String>> formatTableDataLists(QueryPredictedElectricityContrastVO data) {
        List<List<String>> listList = new ArrayList<>();
        if (CollUtil.isEmpty(data.getDataList())) {
            return new ArrayList<>();
        }
        // 数据格式转换
        dataConversions(data.getDataList(), listList);
        return listList;
    }


    private List<List<String>> formatTableDataList(QueryPredictedElectricityVO data) {
        List<List<String>> listList = new ArrayList<>();
        if (CollUtil.isEmpty(data.getDataList())) {
            return new ArrayList<>();
        }
        // 数据格式转换
        dataConversion(data.getDataList(), listList);
        return listList;
    }


    private void dataConversion(List<QueryPredictedElectricityVO.PredictedElectricityInfo> dataList, List<List<String>> listList) {
        for (QueryPredictedElectricityVO.PredictedElectricityInfo tableVO : dataList) {
            String name = tableVO.getName();
            //修改预测电量
            List<BigDecimal> electricityList = tableVO.getElectricityList();
            //虚拟电厂预测电量
            List<BigDecimal> predictedElectricityList = tableVO.getPredictedElectricityList();

            if (ObjectUtil.isNotNull(predictedElectricityList)) {
                listList.add(toStringList(predictedElectricityList, "虚拟电厂预测", name));
            } else {
                listList.add(toStringList(new ArrayList<>(), "虚拟电厂预测", name));
            }
            if (ObjectUtil.isNotNull(electricityList)) {
                listList.add(toStringList(electricityList, "修改预测电量", name));
            } else {
                listList.add(toStringList(new ArrayList<>(), "修改预测电量", name));
            }

        }
    }

    private void dataConversions(List<QueryPredictedElectricityContrastVO.PredictedElectricityInfo> dataList, List<List<String>> listList) {
        for (QueryPredictedElectricityContrastVO.PredictedElectricityInfo tableVO : dataList) {
            String name = tableVO.getName();
            //修改预测电量
            List<BigDecimal> electricityList = tableVO.getElectricityList();
            //虚拟电厂预测电量
            List<BigDecimal> predictedElectricityList = tableVO.getPredictedElectricityList();
            //修改预测电量和实际用电量比例
            List<BigDecimal> electricityRatioList = tableVO.getElectricityRatioList();
            //虚拟电厂预测和实际用电量比例
            List<BigDecimal> predictedRatioList = tableVO.getPredictedRatioList();

            List<BigDecimal> realElectricityList = tableVO.getRealElectricityList();

            if (ObjectUtil.isNotNull(predictedElectricityList)) {
                listList.add(toStringLists(predictedElectricityList, predictedRatioList, "虚拟电厂预测", name));
            } else {
                listList.add(toStringLists(new ArrayList<>(), new ArrayList<>(), "虚拟电厂预测", name));
            }
            if (ObjectUtil.isNotNull(electricityList)) {
                listList.add(toStringLists(electricityList, electricityRatioList, "修改预测电量", name));
            } else {
                listList.add(toStringLists(new ArrayList<>(), new ArrayList<>(), "修改预测电量", name));
            }
            if (ObjectUtil.isNotNull(realElectricityList)) {
                listList.add(toStringLists(realElectricityList, new ArrayList<>(), "实际用电量", name));
            } else {
                listList.add(toStringLists(new ArrayList<>(), new ArrayList<>(), "实际用电量", name));
            }

        }
    }

    private List<String> toStringList(List<BigDecimal> bigDecimalList, String... params) {
        List<String> strings = new ArrayList<>();
        for (BigDecimal bigDecimal : bigDecimalList) {
            strings.add(ObjectUtil.isNull(bigDecimal) ? "-" : bigDecimal.toPlainString());
        }
        for (String param : params) {
            strings.add(0, param);
        }
        return strings;
    }

    private List<String> toStringLists(List<BigDecimal> bigDecimalList, List<BigDecimal> ratioList, String... params) {
        List<String> strings = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(bigDecimalList)) {
            for (int i = 0; i < bigDecimalList.size(); i++) {
                BigDecimal electricity = bigDecimalList.get(i);
                BigDecimal ratio = null;
                if (ObjectUtil.isNotEmpty(ratioList)) {
                    ratio = ratioList.get(i);
                }
                if (ObjectUtil.isNotNull(electricity)) {
                    String electricityPlainString = electricity.toPlainString();
                    if (ObjectUtil.isNotNull(ratio)) {
                        String ratioPlainString = ratio.toPlainString();
                        strings.add(electricityPlainString + "(" + ratioPlainString + "%)");
                    } else {
                        strings.add(electricityPlainString);
                    }
                } else {
                    strings.add("-");
                }
            }
        }

        for (String param : params) {
            strings.add(0, param);
        }

        return strings;
    }

}
