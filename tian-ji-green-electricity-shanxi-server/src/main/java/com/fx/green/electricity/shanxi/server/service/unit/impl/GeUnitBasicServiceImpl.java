package com.fx.green.electricity.shanxi.server.service.unit.impl;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.api.GeUnitApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeUnitBasicDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeUnitBasicVO;
import com.fx.green.electricity.shanxi.server.service.unit.GeUnitBasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 绿电机组信息
 */
@Service
public class GeUnitBasicServiceImpl implements GeUnitBasicService {

    @Autowired
    private GeUnitApi geUnitApi;

    /**
     * 新增机组信息
     *
     * @param param
     */
    @Override
    public DataResult<Void> insertUnitBase(GeUnitBasicDTO param) {
        return geUnitApi.insertUnitBase(param);
    }

    /**
     * 根据租户id查询对应机组
     *
     * @param param
     * @return
     */
    @Override
    public DataResult<List<GeUnitBasicVO>> unitBaseList(IdDTO param) {
        return geUnitApi.unitBaseList(param);
    }

    /**
     * 删除机组信息
     *
     * @param param
     */
    public DataResult<Void> deleteUnitBase(IdDTO param) {
        return geUnitApi.deleteUnitBase(param);
    }
}
