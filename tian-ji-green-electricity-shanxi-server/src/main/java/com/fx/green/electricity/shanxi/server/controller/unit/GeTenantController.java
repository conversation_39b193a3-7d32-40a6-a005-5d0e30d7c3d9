package com.fx.green.electricity.shanxi.server.controller.unit;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeTenantDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeTenantVO;
import com.fx.green.electricity.shanxi.server.service.unit.GeTenantService;
import com.fx.operation.api.vo.TenantVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 绿电租户信息维护
 */
@RestController
@Api(tags = "绿电租户信息维护")
@RequestMapping("geTenant")
public class GeTenantController {

    @Autowired
    private GeTenantService geTenantService;

    /**
     * 获取绿电直连租户和机组
     *
     * @return
     */
    @ApiOperation("获取绿电直连租户和机组")
    @PostMapping("getGreenTenantUnit")
    public DataResult<List<TenantVO>> getGreenTenantUnit() {
        List<TenantVO> greenTenantUnit = geTenantService.getGreenTenantUnit();
        return DataResult.success(greenTenantUnit);
    }

    /**
     * 新增绿电租户
     *
     * @param param
     */
    @ApiOperation("新增绿电租户")
    @PostMapping("insertTenant")
    public DataResult<Void> insertTenant(@RequestBody GeTenantDTO param) {
        return geTenantService.insertTenant(param);
    }

    /**
     * 删除绿电租户
     *
     * @param param
     */
    @ApiOperation("删除绿电租户")
    @PostMapping("deleteTenant")
    public DataResult<Void> deleteTenant(@RequestBody IdDTO param) {
        return geTenantService.deleteTenant(param);
    }

    /**
     * 绿电租户分页
     *
     * @param param
     * @return
     */
    @ApiOperation("绿电租户分页")
    @PostMapping("tenantList")
    public DataResult<List<GeTenantVO>> tenantList(@RequestBody GeTenantDTO param) {
        return geTenantService.tenantList(param);
    }

    /**
     * 获取所有租户和机组对应信息
     *
     * @return
     */
    @ApiOperation("获取所有租户和机组对应信息")
    @PostMapping("getTenantUnitDetail")
    public DataResult<List<GeTenantVO>> getTenantUnitDetail() {
        return geTenantService.getTenantUnitDetail();
    }
}
