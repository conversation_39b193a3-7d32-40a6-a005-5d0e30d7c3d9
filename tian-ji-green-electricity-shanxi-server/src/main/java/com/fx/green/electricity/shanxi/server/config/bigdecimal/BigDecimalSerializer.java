package com.fx.green.electricity.shanxi.server.config.bigdecimal;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.fx.green.electricity.shanxi.api.annotation.BigDecimalFormat;

import java.io.IOException;
import java.math.BigDecimal;

public class BigDecimalSerializer extends JsonSerializer<BigDecimal> implements ContextualSerializer {

    private BigDecimalFormat formatAnnotation;

    // 无参构造用于Jackson实例化
    public BigDecimalSerializer() {
    }

    // 带注解的构造
    public BigDecimalSerializer(BigDecimalFormat formatAnnotation) {
        this.formatAnnotation = formatAnnotation;
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property)
            throws JsonMappingException {

        if (property != null) {
            BigDecimalFormat ann = property.getAnnotation(BigDecimalFormat.class);
            if (ann != null) {
                return new BigDecimalSerializer(ann);
            }
        }
        return this;
    }

    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider provider)
            throws IOException {

        if (value == null) {
            gen.writeNull();
            return;
        }

        if (formatAnnotation == null) {
            // 如果没有注解，使用默认处理
            gen.writeNumber(value);
            return;
        }

        // 设置精度和舍入模式
        BigDecimal scaledValue = value.setScale(
                formatAnnotation.scale(),
                formatAnnotation.roundingMode()
        );

        // 是否转换为字符串形式（自动补零）
        if (formatAnnotation.autoZeroPadding()) {
            gen.writeString(scaledValue.toPlainString());
        } else {
            gen.writeNumber(scaledValue);
        }
    }
}