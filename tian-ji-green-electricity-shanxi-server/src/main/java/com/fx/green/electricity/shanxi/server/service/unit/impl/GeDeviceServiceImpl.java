package com.fx.green.electricity.shanxi.server.service.unit.impl;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.api.GeUnitApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeDeviceSaveDTO;
import com.fx.green.electricity.shanxi.server.service.unit.GeDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 绿电设备信息
 */
@Service
public class GeDeviceServiceImpl implements GeDeviceService {

    @Autowired
    private GeUnitApi geUnitApi;

    /**
     * 新增设备信息
     *
     * @param param
     */
    @Override
    public DataResult<Void> insertDevice(GeDeviceSaveDTO param) {
        return geUnitApi.insertDevice(param);
    }

    /**
     * 删除设备
     *
     * @param param
     */
    @Override
    public DataResult<Void> deleteDevice(IdDTO param) {
        return geUnitApi.deleteDevice(param);
    }
}
