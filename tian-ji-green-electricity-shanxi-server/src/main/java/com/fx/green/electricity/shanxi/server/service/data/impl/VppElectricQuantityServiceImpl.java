package com.fx.green.electricity.shanxi.server.service.data.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.common.exception.FxServiceException;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.api.VppApi;
import com.fx.green.electricity.shanxi.api.api.file.VppFileApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.GetUpdateTimeDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricDeclareDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.ImportExcelDetailPageListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.file.VppFileUploadVO;
import com.fx.green.electricity.shanxi.api.utils.FileTypeUtil;
import com.fx.green.electricity.shanxi.server.service.data.VppElectricQuantityService;
import com.fx.green.electricity.shanxi.server.utils.DownloadUtil;
import com.fx.green.electricity.shanxi.server.utils.HttpServletUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.List;

/**
 * 电量数据 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class VppElectricQuantityServiceImpl implements VppElectricQuantityService {

    @Resource
    private VppApi vppApi;

    @Resource
    private VppFileApi vppFileApi;

    @Override
    public DataResult<FxPage<ImportExcelDetailPageListVO>> queryImportRecord(VppElectricQuantityDTO.QueryDTO param) {
        return vppApi.queryImportRecord(param);
    }

    @Override
    public DataResult<ImportExcelVO> importRecord(MultipartFile file, String time) {
        // 校验文件类型
        boolean checkExcelFileFlag = FileTypeUtil.checkExcelFile(file);
        if (!checkExcelFileFlag) {
            throw new FxServiceException("请上传xls或xlsx类型文件");
        }
        try {
            Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
            //获取虚拟电厂信息
            InputStream inputStream = file.getInputStream();
            List<VppElectricDeclareDTO> importList = EasyExcelFactory.read(inputStream).head(VppElectricDeclareDTO.class).sheet().headRowNumber(1).doReadSync();
            for (VppElectricDeclareDTO vppElectricDeclareDTO : importList) {
                String timeFrame = vppElectricDeclareDTO.getTimeFrame().split("-")[1];
                vppElectricDeclareDTO.setTimeFrame(timeFrame);
                vppElectricDeclareDTO.setDateDay(DateUtil.parse(time));
                vppElectricDeclareDTO.setTenantId(tenantId);
            }

            //上传文件
            DataResult<VppFileUploadVO> fileUploadVODataResult = vppFileApi.uploadFile(file);
            String url = fileUploadVODataResult.getData().getUrl();

            //添加导入信息
            VppElectricQuantityDTO vppElectricQuantityDTO = new VppElectricQuantityDTO();
            vppElectricQuantityDTO.setName(file.getOriginalFilename());
            vppElectricQuantityDTO.setUrl(url);
            vppElectricQuantityDTO.setType(1);
            vppElectricQuantityDTO.setTenantId(tenantId);
            vppElectricQuantityDTO.setRunningDate(DateUtil.parse(time));
            vppElectricQuantityDTO.setUploadTime(new Date());
            vppElectricQuantityDTO.setStatus(Boolean.TRUE);
            vppElectricQuantityDTO.setElectricDeclareList(importList);
            return vppApi.importRecord(vppElectricQuantityDTO);
        } catch (Exception e) {
            log.error("数据解析失败" + e);
            return DataResult.failed("数据解析失败");
        }
    }

    @Override
    public DataResult<String> getUpdateTime(GetUpdateTimeDTO param) {
        return vppApi.getUpdateTime(param);
    }

    @Override
    public DataResult<Void> deleteRecordData(DeleteRecordDTO deleteRecordDTO) {
        return vppApi.deleteRecordData(deleteRecordDTO);
    }

    @Override
    public DataResult<Void> downloadRecord(CommonDTO.DateDTO param) {
        DataResult<List<SeElectricDeclareVO>> listDataResult = vppApi.downloadRecord(param);
        if (ObjectUtil.isNotNull(listDataResult.getData())) {
            List<SeElectricDeclareVO> data = listDataResult.getData();
            String fileName = "日前申报数据" + DateUtil.formatDate(param.getQueryDate()) + ".xlsx";
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            EasyExcel.write(stream, SeElectricDeclareVO.class)
                    .sheet()
                    .doWrite(data);
            byte[] fileBytes = stream.toByteArray();
            DownloadUtil.downloadJsonType(fileName, fileBytes, HttpServletUtil.getResponse());
        }
        return DataResult.success();
    }
}
