package com.fx.green.electricity.shanxi.server.service.user.impl;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.api.user.VppBindCycleApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserBindCycleDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserBindCycleVO;
import com.fx.green.electricity.shanxi.server.service.user.VppBindCycleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 绑定周期 Service 实现类
 *
 * <AUTHOR>
 **/
@Service
public class VppBindCycleServiceImpl implements VppBindCycleService {

    @Resource
    private VppBindCycleApi vppBindCycleApi;

    @Override
    public DataResult<Void> addVppBindCycle(VppUserBindCycleDTO param) {
        return vppBindCycleApi.addVppBindCycle(param);
    }

    @Override
    public DataResult<List<VppUserBindCycleVO>> selectList(IdDTO param) {
        return vppBindCycleApi.selectList(param);
    }

    @Override
    public DataResult<Void> delete(IdDTO param) {
        return vppBindCycleApi.delete(param);
    }
}
