<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" debug="false">
    <property name="log.path" value="/Users/<USER>/data/logs/"/>
    <springProperty name="applicationName" scope="context" source="spring.application.name"/>
    <springProperty name="springProfile" scope="context" source="spring.profiles.active"/>
    <!--console -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss:SSS} [%X{trace-id}] [%p] [%t] %c - %m%n</pattern>
        </encoder>
    </appender>
    <!-- file -->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件路径 -->
        <file>${log.path}/${applicationName}/${springProfile}/${applicationName}.log</file>
        <!-- 是否追加 默认为true -->
        <append>true</append>
        <!-- 滚动策略 日期+大小 策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/${applicationName}/${springProfile}/%d{yyyy-MM-dd}/info/info-%i.zip
            </fileNamePattern>
            <!-- 单个日志大小 -->
            <maxFileSize>50MB</maxFileSize>
            <!-- 日志保存周期 -->
            <maxHistory>7</maxHistory>
            <!-- 总大小 -->
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
        <!-- 格式化 -->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss:SSS} [%X{trace-id}] [%p] [%t] %c - %m%n</pattern>
        </encoder>
        <!-- 级别过滤 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <springProfile name="default,dev,beta">
                <level>DEBUG</level>
            </springProfile>
            <springProfile name="test">
                <level>DEBUG</level>
            </springProfile>
            <springProfile name="prod">
                <level>INFO</level>
            </springProfile>
            <springProfile name="release">
                <level>INFO</level>
            </springProfile>
            <springProfile name="sxgdt">
                <level>INFO</level>
            </springProfile>
        </filter>
    </appender>

    <springProfile name="default,dev,beta">
        <root level="debug">
            <appender-ref ref="console"/>
            <appender-ref ref="file"/>
        </root>
    </springProfile>
    <springProfile name="test,prod,release,sxgdt">
        <root level="info">
            <appender-ref ref="file"/>
        </root>
    </springProfile>

</configuration>