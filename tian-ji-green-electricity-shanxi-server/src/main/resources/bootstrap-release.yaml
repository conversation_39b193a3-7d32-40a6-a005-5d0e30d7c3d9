spring:
  config:
    activate:
      on-profile: release
  cloud:
    sentinel:
      transport:
        dashboard: 10.1.1.102:8849
    nacos:
      username: nacos
      password: 8inRpcNXFKZorhVg
      discovery:
        server-addr: 10.1.2.72:8848,10.1.2.73:8848,10.1.2.74:8848
        namespace: d076e53e-619f-40a2-b0ff-3bf5dbd57629
      config:
        # 配置中心地址
        server-addr: 10.1.2.72:8848,10.1.2.73:8848,10.1.2.74:8848
        namespace: d076e53e-619f-40a2-b0ff-3bf5dbd57629
