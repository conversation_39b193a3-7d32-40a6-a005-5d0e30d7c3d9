package com.fx.green.electricity.shanxi.api.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TikaFileTypeUtil 测试类
 *
 * <AUTHOR>
 */
@Slf4j
class FileTypeUtilTest {

    @Test
    void testDetectExcelFileType() {
        // 模拟 xlsx 文件的魔数（ZIP 文件头）
        byte[] xlsxMagicBytes = {0x50, 0x4B, 0x03, 0x04}; // ZIP 文件头

        MockMultipartFile xlsxFile = new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                xlsxMagicBytes
        );

        String detectedType = FileTypeUtil.detectFileType(xlsxFile);
        log.info("检测到的 xlsx 文件类型: {}", detectedType);

        // xlsx 文件可能被检测为 xlsx 或者因为是 ZIP 格式而被特殊处理
        assertTrue(detectedType != null, "应该能检测到文件类型");
    }

    @Test
    void testDetectPdfFileType() {
        // 模拟 PDF 文件的魔数
        byte[] pdfMagicBytes = {0x25, 0x50, 0x44, 0x46}; // %PDF

        MockMultipartFile pdfFile = new MockMultipartFile(
                "file",
                "test.pdf",
                "application/pdf",
                pdfMagicBytes
        );

        String detectedType = FileTypeUtil.detectFileType(pdfFile);
        log.info("检测到的 PDF 文件类型: {}", detectedType);

        assertEquals("pdf", detectedType, "应该检测为 PDF 文件");
    }

    @Test
    void testDetectImageFileType() {
        // 模拟 JPEG 文件的魔数
        byte[] jpegMagicBytes = {(byte) 0xFF, (byte) 0xD8, (byte) 0xFF, (byte) 0xE0};

        MockMultipartFile jpegFile = new MockMultipartFile(
                "file",
                "test.jpg",
                "image/jpeg",
                jpegMagicBytes
        );

        String detectedType = FileTypeUtil.detectFileType(jpegFile);
        log.info("检测到的 JPEG 文件类型: {}", detectedType);

        assertEquals("jpg", detectedType, "应该检测为 JPEG 文件");
    }

    @Test
    void testIsExcelFile() {
        // 测试 xlsx 文件
        byte[] xlsxMagicBytes = {0x50, 0x4B, 0x03, 0x04};
        MockMultipartFile xlsxFile = new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                xlsxMagicBytes
        );

        boolean isExcel = FileTypeUtil.isExcelFile(xlsxFile);
        log.info("xlsx 文件是否为 Excel: {}", isExcel);

        // 由于 xlsx 可能被检测为 zip，我们需要检查实际的检测结果
        String detectedType = FileTypeUtil.detectFileType(xlsxFile);
        log.info("实际检测类型: {}", detectedType);
    }

    @Test
    void testCheckExcelFileWithFileName() {
        // 测试基于文件名的检查
        assertTrue(FileTypeUtil.checkExcelFile("test.xlsx"), "应该识别 xlsx 文件名");
        assertTrue(FileTypeUtil.checkExcelFile("test.xls"), "应该识别 xls 文件名");
        assertFalse(FileTypeUtil.checkExcelFile("test.pdf"), "不应该识别 pdf 文件名");
        assertFalse(FileTypeUtil.checkExcelFile(""), "空文件名应该返回 false");
    }

    @Test
    void testCheckExcelFileWithMultipartFile() {
        // 测试基于文件内容的检查
        byte[] xlsxMagicBytes = {0x50, 0x4B, 0x03, 0x04};
        MockMultipartFile xlsxFile = new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                xlsxMagicBytes
        );

        boolean isExcel = FileTypeUtil.checkExcelFile(xlsxFile);
        log.info("checkExcelFile 结果: {}", isExcel);

        // 测试错误的文件名
        MockMultipartFile wrongNameFile = new MockMultipartFile(
                "file",
                "test.pdf",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                xlsxMagicBytes
        );

        boolean isExcelWrongName = FileTypeUtil.checkExcelFile(wrongNameFile);
        assertFalse(isExcelWrongName, "错误的文件名应该返回 false");
    }

    @Test
    void testIsImageFile() {
        // 测试 JPEG 文件
        byte[] jpegMagicBytes = {(byte) 0xFF, (byte) 0xD8, (byte) 0xFF, (byte) 0xE0};
        MockMultipartFile jpegFile = new MockMultipartFile(
                "file",
                "test.jpg",
                "image/jpeg",
                jpegMagicBytes
        );

        boolean isImage = FileTypeUtil.isImageFile(jpegFile);
        assertTrue(isImage, "应该检测为图片文件");
    }

    @Test
    void testValidateFileType() {
        // 测试 PDF 文件验证
        byte[] pdfMagicBytes = {0x25, 0x50, 0x44, 0x46};
        MockMultipartFile pdfFile = new MockMultipartFile(
                "file",
                "test.pdf",
                "application/pdf",
                pdfMagicBytes
        );

        boolean isValidPdf = FileTypeUtil.validateFileType(pdfFile, "pdf");
        assertTrue(isValidPdf, "应该验证为有效的 PDF 文件");

        boolean isValidExcel = FileTypeUtil.validateFileType(pdfFile, "xlsx");
        assertFalse(isValidExcel, "不应该验证为 Excel 文件");
    }

    @Test
    void testDetectFileTypeWithInputStream() throws IOException {
        // 测试使用 InputStream 的方法
        byte[] pdfMagicBytes = {0x25, 0x50, 0x44, 0x46, 0x2D, 0x31, 0x2E, 0x34}; // %PDF-1.4

        try (InputStream inputStream = new ByteArrayInputStream(pdfMagicBytes)) {
            String detectedType = FileTypeUtil.detectFileType(inputStream, "test.pdf");
            log.info("通过 InputStream 检测到的文件类型: {}", detectedType);

            assertEquals("pdf", detectedType, "应该检测为 PDF 文件");
        }
    }

    @Test
    void testNullAndEmptyFile() {
        // 测试 null 文件
        String detectedType = FileTypeUtil.detectFileType(null);
        assertNull(detectedType, "null 文件应该返回 null");

        // 测试空文件
        MockMultipartFile emptyFile = new MockMultipartFile(
                "file",
                "empty.txt",
                "text/plain",
                new byte[0]
        );

        detectedType = FileTypeUtil.detectFileType(emptyFile);
        log.info("空文件检测结果: {}", detectedType);

        boolean isExcel = FileTypeUtil.isExcelFile(emptyFile);
        assertFalse(isExcel, "空文件不应该被检测为 Excel");
    }
}
