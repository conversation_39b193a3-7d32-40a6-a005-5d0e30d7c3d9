package com.fx.green.electricity.shanxi.api.pojo.dto.production;

import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户生产信息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VppUserProductionDataDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 8445529111099945039L;
    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("负荷用户id")
    @NotNull( message = "用户id不能为空，请检查参数。")
    private Long userId;

    @ApiModelProperty("是否装表(0“已安装，1：未安装)")
    private Integer isInstall;

    @ApiModelProperty("生产流程")
    @Size(max = 500, message = "生产流程过长，请重新填写。")
    private String process;

    @ApiModelProperty("时间安排")
    @Size(max = 500, message = "时间安排过长，请重新填写。")
    private String timeSchedule;

    @ApiModelProperty("固定检修计划")
    @Size(max = 500, message = "固定检修计划过长，请重新填写。")
    private String fixedMaintenancePlan;

    @ApiModelProperty("最大运行负荷")
    @Digits(integer = 8, fraction = 2, message = "整数部分最多8位，小数部分最多2位")
    @DecimalMin(value = "0.00", inclusive = false, message = "最大运行负荷必须为正数")
    private BigDecimal maxOperatingLoad;

    @ApiModelProperty("最小运行负荷")
    @Digits(integer = 8, fraction = 2, message = "整数部分最多8位，小数部分最多2位")
    @DecimalMin(value = "0.00", inclusive = false, message = "最小运行负荷必须为正数")
    private BigDecimal minOperatingLoad;

    @ApiModelProperty("调节容量")
    @Digits(integer = 8, fraction = 2, message = "整数部分最多8位，小数部分最多2位")
    @DecimalMin(value = "0.00", inclusive = false, message = "调节容量必须为正数")
    private BigDecimal adjustingCapacity;

    @Valid
    @ApiModelProperty("用户生产数据-沟通人员")
    private List<VppUserProductionDataStaffVO> staffVOList;

    @Valid
    @ApiModelProperty("用户生产数据-主要调节设备/主要生产工艺段")
    private List<VppUserProductionDataAdjustDeviceVO> adjustDeviceVOList;

    @Valid
    @ApiModelProperty("用户生产数据-发电设备VO")
    private List<VppUserProductionDataPowerDeviceVO> powerDeviceVOList;

    @Data
    @ApiModel("用户生产数据-沟通人员VO")
    public static class VppUserProductionDataStaffVO {
        @ApiModelProperty("主键id")
        private Long id;

        @ApiModelProperty("用户生产数据id")
        private Long userProductionDataId;

        @ApiModelProperty("沟通人员姓名")
        @Size(max = 20, message = "沟通人员姓名过长，请重新填写。")
        private String name;

        @ApiModelProperty("沟通人员联系方式")
        @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式错误")
        private String contactInformation;

        @ApiModelProperty("职位")
        @Size(max = 20, message = "职位过长，请重新填写。")
        private String position;
    }

    @Data
    @ApiModel("用户生产数据-主要调节设备/主要生产工艺段VO")
    public static class VppUserProductionDataAdjustDeviceVO {
        @ApiModelProperty("主键id")
        private Long id;

        @ApiModelProperty("用户生产数据id")
        private Long userProductionDataId;

        @ApiModelProperty("设备名称/工艺段名称")
        @Size(max = 50, message = "设备名称/工艺段名称过长，请重新填写。")
        private String name;

        @ApiModelProperty("额定功率")
        @Digits(integer = 8, fraction = 2, message = "整数部分最多8位，小数部分最多2位")
        @DecimalMin(value = "0.00", inclusive = false, message = "额定功率必须为正数")
        private BigDecimal ratedPower;

        @ApiModelProperty("正常生产功率")
        @Digits(integer = 8, fraction = 2, message = "整数部分最多8位，小数部分最多2位")
        @DecimalMin(value = "0.00", inclusive = false, message = "正常生产功率必须为正数")
        private BigDecimal normalProductionPower;

        @ApiModelProperty("检修功率")
        @Digits(integer = 8, fraction = 2, message = "整数部分最多8位，小数部分最多2位")
        @DecimalMin(value = "0.00", inclusive = false, message = "检修功率必须为正数")
        private BigDecimal maintenancePower;

        @ApiModelProperty("停产功率")
        @Digits(integer = 8, fraction = 2, message = "整数部分最多8位，小数部分最多2位")
        @DecimalMin(value = "0.00", inclusive = false, message = "停产功率必须为正数")
        private BigDecimal shutdownPower;

        @ApiModelProperty("开机运行时长")
        @Digits(integer = 8, fraction = 2, message = "整数部分最多8位，小数部分最多2位")
        @DecimalMin(value = "0.00", inclusive = false, message = "开机运行时长必须为正数")
        private BigDecimal startupRuntime;
    }

    @Data
    @ApiModel("用户生产数据-发电设备VO")
    public static class VppUserProductionDataPowerDeviceVO {
        @ApiModelProperty("主键id")
        private Long id;

        @ApiModelProperty("用户生产数据id")
        private Long userProductionDataId;

        @ApiModelProperty("设备名称")
        @Size(max = 50, message = "设备名称过长，请重新填写。")
        private String deviceName;

        @ApiModelProperty("额定功率")
        @Digits(integer = 8, fraction = 2, message = "整数部分最多8位，小数部分最多2位")
        @DecimalMin(value = "0.00", inclusive = false, message = "额定功率必须为正数")
        private BigDecimal ratedPower;

        @ApiModelProperty("正常生产功率")
        @Digits(integer = 8, fraction = 2, message = "整数部分最多8位，小数部分最多2位")
        @DecimalMin(value = "0.00", inclusive = false, message = "正常生产功率必须为正数")
        private BigDecimal normalProductionPower;

    }

}
