package com.fx.green.electricity.shanxi.api.pojo.dto.unit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 设备批量导入DTO
 */
@Data
@ApiModel("设备批量导入DTO")
public class GeDeviceSaveDTO implements Serializable {
    private static final long serialVersionUID = -4064191284739132745L;

    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("机组id")
    private Long unitId;
    @ApiModelProperty("设备列表")
    private List<GeDeviceDTO> deviceList;
}
