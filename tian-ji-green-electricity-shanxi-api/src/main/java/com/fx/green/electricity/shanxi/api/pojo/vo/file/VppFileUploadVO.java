package com.fx.green.electricity.shanxi.api.pojo.vo.file;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *  文件上传VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("文件返回参数")
public class VppFileUploadVO implements Serializable {

    private static final long serialVersionUID = 7992117377064945044L;

    @ApiModelProperty("文件名称")
    private String name;

    @ApiModelProperty("文件路径")
    private String path;

    @ApiModelProperty("文件地址")
    private String url;

    @ApiModelProperty("文件大小")
    private Long size;

    @ApiModelProperty("文件读取进度")
    private String pace;

}
