package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 时间基类
 */
@Data
@ApiModel("时间基类")
public class TimeBaseDTO implements Serializable {

    private static final long serialVersionUID = -3298544818863087218L;
    @ApiModelProperty("时点")
    private String timeFrame;
    @ApiModelProperty("时点值")
    private BigDecimal value;
}
