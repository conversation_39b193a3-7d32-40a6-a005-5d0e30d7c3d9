package com.fx.green.electricity.shanxi.api.enums;

import com.fx.common.exception.FxServiceCode;

/**
 * 绿电直连异常码
 */
public enum GreenElectricityServiceCodeEnum implements FxServiceCode {
    /**
     * 22501-22530
     */
    ContractTargetError(22501, "新能源公司下机组合同标的时间重复，请检查"),
    TenantDeleteError(22502, "企业下绑定了场站，无法删除"),
    TenantInsertRepeatError(22503, "当前企业已经录入，请检查"),
    UnitBasicInertRepeatError(22504, "当前机组已经录入，请检查"),
    DeviceInsertRepeatError(22505, "当前设备已经录入，请检查"),
    UnitBasicDeleteError(22506, "机组下存在设备，无法删除"),
    FileUnitCatchBase(22507, "文件中机组名称与系统维护机组名称不匹配，请检查"),
    ;


    private final int code;
    private final String value;

    GreenElectricityServiceCodeEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.value;
    }

}
