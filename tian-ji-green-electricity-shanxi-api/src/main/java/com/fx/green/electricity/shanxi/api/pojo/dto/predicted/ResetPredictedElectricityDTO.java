package com.fx.green.electricity.shanxi.api.pojo.dto.predicted;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 重置虚拟电厂预测
 * <AUTHOR>
 **/
@Data
@ApiModel("重置虚拟电厂预测")
public class ResetPredictedElectricityDTO extends BaseDTO {

    @ApiModelProperty("用戶id")
    private Long userId;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("预测维度 1.日 2.上旬 3.中旬 4.下旬 5.月")
    private Integer dimension;

    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateDay;


}
