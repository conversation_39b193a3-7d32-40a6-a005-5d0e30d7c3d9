package com.fx.green.electricity.shanxi.api.pojo.vo.predicted;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
*不可选择日期
**/
@Data
@ApiModel("不可选择日期")
public class VppPredictedExcludeDateVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("不可选择日期")
    private List<String> dateDay;
    @ApiModelProperty("租户id")
    private Long tenantId;

}