package com.fx.green.electricity.shanxi.api.pojo.dto.electric;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("实际电量")
@ExcelIgnoreUnannotated
public class VppElectricActualDTO extends BaseDTO {

    private static final long serialVersionUID = 3433192211213400309L;

    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("名称")
    @ExcelProperty("市场成员名称")
    private String name;
    @ApiModelProperty("售电租户ID")
    private Long tenantId;
    @ApiModelProperty("统一社会信用代码")
    private String userCode;
    @ApiModelProperty("电量数据ID")
    private Long electricId;
    @ApiModelProperty("户号")
    @ExcelProperty("户号")
    private String registered;
    @ApiModelProperty("计量点")
    @ExcelProperty("计量点")
    private String meteringCode;
    @ApiModelProperty("日期")
    @ExcelProperty("日期")
    private Date dateDay;
    @ApiModelProperty("时点")
    @ExcelProperty("时点")
    private String timeFrame;
    @ApiModelProperty("电量(MWh)")
    @ExcelProperty("电量(MWh)")
    private BigDecimal electricity;

    @ApiModelProperty("负荷用户id")
    private Long userId;
}