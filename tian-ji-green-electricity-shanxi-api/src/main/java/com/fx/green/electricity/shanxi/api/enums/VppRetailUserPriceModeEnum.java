package com.fx.green.electricity.shanxi.api.enums;

import lombok.Getter;

/**
 * 新零售用户-定价方式
 *
 * <AUTHOR>
 **/
@Getter
public enum VppRetailUserPriceModeEnum {
    /**
     * 定价方式
     */
    TIME_SHARING_PRICE(1, "分时约定价格（不约定电量）"),
    CENTRALIZED_BIDDING(2, "月度集中竞价+价差（不约定电量）"),
    NO_TIME_SHARING_CONTRACT_PRICE(3, "不分时约定价格"),
    FIXED_PRICE_EMERGING(4, "固定价格（战新"),
    MONTH_AND_TEN_DAYS_CENTRALIZED_BIDDING(5, "月度及各旬集中竞价按日历天数加权出清电价+价差（不约定电量）"),
    MONTH_CENTRALIZED_BIDDING_AVERAGE(6, "月度集中竞价各时段出清电价算术平均值+价差"),
    POWER_PURCHASE_AGENT_OF_STATE_GRID(7, "国网代理购电价格+价差"),
    TIME_SHARING_PRICE_CONTRACT_ELECTRICITY(8, "分时约定价格（约定电量）"),
    CENTRALIZED_BIDDING_CONTRACT_ELECTRICITY(9, "月度集中竞价+价差（约定电量）"),
    MONTH_AND_TEN_DAYS_CENTRALIZED_BIDDING_CONTRACT_ELECTRICITY(10, "月度及各旬集中竞价按日历天数加权出清电价+价差（约定电量）"),
    COMBINATION_PRICING(11, "组合价格套餐");

    private final int code;
    private final String value;

    VppRetailUserPriceModeEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static VppRetailUserPriceModeEnum getVppUserTradingUnitByValue(String value) {
        for (VppRetailUserPriceModeEnum tradingUnit : VppRetailUserPriceModeEnum.values()) {
            if (tradingUnit.value.equals(value)) {
                return tradingUnit;
            }
        }
        return null;
    }

    public static String getVppUserTradingUnitValueByType(Integer type) {
        for (VppRetailUserPriceModeEnum value : VppRetailUserPriceModeEnum.values()) {
            if (value.code == type) {
                return value.value;
            }
        }
        return null;
    }
}
