package com.fx.green.electricity.shanxi.api.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.fx.green.electricity.shanxi.api.enums.VppElectricQuantityType;

/**
 * 电量数据
 *
 * <AUTHOR>
 */
public class VppElectricQuantityTypeConverter implements Converter<Integer> {
    @Override
    public Class supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (value.length() == 0) {
            return null;
        }
        VppElectricQuantityType vppElectricQuantityType = VppElectricQuantityType.getVppElectricQuantityTypeByValue(value);
        if (vppElectricQuantityType == null) {
            return null;
        }
        return vppElectricQuantityType.getCode();
    }


    @Override
    public CellData convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }
}
