package com.fx.green.electricity.shanxi.api.pojo.dto.production;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class VppUserProductionStatisticsQueryDetailDTO extends BaseDTO {

    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @ApiModelProperty("类型：1.预计生产计划，2.实际生产情况")
    @NotNull(message = "生产类型不能为空")
    private Integer productionType;

    @NotNull(message = "查询时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateDay;


}
