package com.fx.green.electricity.shanxi.api.api;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeFileRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeUnitSaveDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeContractMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeDayRecordVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 数据维护api
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "geMaintenanceApi", url = "${tj-dev.geMaintenanceApi:}")
public interface GeMaintenanceApi {

    /**
     * 新增合同数据
     *
     * @param param
     */
    @PostMapping("geContractMaintenance/insertContract")
    DataResult<Void> insertContract(@RequestBody GeContractMaintenanceDTO param);

    /**
     * 删除合同数据
     *
     * @param param
     */
    @PostMapping("geContractMaintenance/deleteContract")
    DataResult<Void> deleteContract(@RequestBody IdDTO param);

    /**
     * 合同数据列表
     *
     * @param param
     * @return
     */
    @PostMapping("geContractMaintenance/contractList")
    DataResult<List<GeContractMaintenanceVO>> contractList(@RequestBody GeContractMaintenanceDTO param);

    /**
     * 通过不同类型导入机组数据
     *
     * @param param
     */
    @PostMapping("geUnitNodePrice/importUnitData")
    DataResult<Void> importUnitData(@RequestBody GeUnitSaveDTO param);

    /**
     * 导入文件记录表
     *
     * @param param
     */
    @PostMapping("geFileRecord/insertFileRecord")
    DataResult<Void> insertFileRecord(@RequestBody List<GeFileRecordDTO> param);

    /**
     * 文件维护记录
     *
     * @param param
     * @return
     */
    @PostMapping("geFileRecord/getFileRecordByDay")
    DataResult<List<GeDayRecordVO>> getFileRecordByDay(@RequestBody GeFileRecordDTO param);

    /**
     * 文件列表状态
     *
     * @param param
     * @return
     */
    @PostMapping("geFileRecord/fileStatus")
    DataResult<List<GeFileRecordVO>> fileStatus(@RequestBody GeFileRecordDTO param);

    /**
     * 获取当日下数据
     *
     * @param param
     * @return
     */
    @PostMapping("geFileRecord/getInfoDataValue")
    DataResult<Map<String, String>> getInfoDataValue(@RequestBody GeFileRecordDTO param);

}
