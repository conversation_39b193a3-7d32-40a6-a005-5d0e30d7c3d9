package com.fx.green.electricity.shanxi.api.api.data;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryImportRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * 零售合同 Api 接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppRetailContractsApi")
public interface VppRetailContractsApi {

    /**
     * 获取零售列表
     *
     * @param param 查询参数
     * @return 零售列表
     */
    @PostMapping("/vppRetailContracts/getList")
    DataResult<FxPage<VppRetailContractsVO>> getList(@RequestBody QueryVppRetailContractsDTO param);

    /**
     * 导入零售合同
     *
     * @param queryImportRecordDTO 导入参数
     * @return 导入结果
     */
    @PostMapping("/vppRetailContracts/queryImport")
    DataResult<Void> queryImportRecord(@RequestBody QueryImportRecordDTO queryImportRecordDTO);


    /**
     * 导入零售合同
     *
     * @param file     文件
     * @param runMonth 运行月份
     * @return 导入结果
     */
    @PostMapping(value = "/vppRetailContracts/importRetailContract", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    DataResult<Void> importRetailContract(@RequestPart("file") MultipartFile file, @RequestParam("runMonth") String runMonth);
}
