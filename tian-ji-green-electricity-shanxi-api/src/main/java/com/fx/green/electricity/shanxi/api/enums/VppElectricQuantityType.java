package com.fx.green.electricity.shanxi.api.enums;

import lombok.Getter;

/**
 * 电量数据操作类型
 *
 * <AUTHOR>
 */
@Getter
public enum VppElectricQuantityType {
    /**
     * 电量数据类型
     */
    DECLARE(1, "日前申报电量（普通）"),

    ACTUAL(2, "实际用电量"),

    DECLARENEW(3, "日前申报电量（战新）");

    private final int code;
    private final String value;

    VppElectricQuantityType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static VppElectricQuantityType getVppElectricQuantityTypeByValue(String value) {
        for (VppElectricQuantityType quantityType : VppElectricQuantityType.values()) {
            if (quantityType.value.equals(value)) {
                return quantityType;
            }
        }
        return null;
    }


}
