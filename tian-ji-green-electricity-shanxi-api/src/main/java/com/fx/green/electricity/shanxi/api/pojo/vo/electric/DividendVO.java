package com.fx.green.electricity.shanxi.api.pojo.vo.electric;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 红利收益分析VO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel("红利收益分析VO")
public class DividendVO {


    @ApiModelProperty("红利均价")
    private BigDecimal dividendAvgPrice;

    @ApiModelProperty("红利总费用")
    private BigDecimal dividendTotalCharge;

    @ApiModelProperty("虚拟电厂红利")
    private BigDecimal vppDividend;

    @ApiModelProperty("用户红利/预估红利")
    private BigDecimal userDividend;

    @ApiModelProperty("调节成功次数")
    private Long successNum;


    /**
     * 用户实际用电量总和
     */
    @Data
    public static class UserElectricSqlVO {
        @ApiModelProperty("用户id")
        private Long userId;

        @ApiModelProperty("日期")
        private String dateDay;

        @ApiModelProperty("时间点")
        private String timeFrame;

        @ApiModelProperty("红利系数")
        private BigDecimal dividendRatio;

        @ApiModelProperty("实际用电量总和")
        private BigDecimal realElectricSum;
    }
}
