package com.fx.green.electricity.shanxi.api.enums;

import com.fx.common.exception.FxServiceCode;


/**
 * <AUTHOR>
 */
public enum VirtualPowerPlantServiceCodeEnum implements FxServiceCode {


    /**
     * 17000-17029 待添加
     */
    SHOULD_CONFIGURED_BASE_INFO_PERIOD(16999, "请先完善[基础信息管理]中的调节容量配置"),
    SHOULD_CONFIGURED_PERIOD(17000, "请先完成[调节时段管理]中的时段配置"),

    /**
     * 导入异常
     * 13001-13006
     */
    IMPORT_FILE_TYPE_ERROR(17001, "文件导入异常，请检查文件格式"),
    IMPORT_EMPTY_ERROR(17002, "导入文件为空"),
    IMPORT_TEMPLATE_ERROR(17003, "请检查上传的文件内容"),
    IMPORT_PARAM_ERROR(17004, "导入参数选择错误"),
    IMPORT_REPETITION_ERROR(17005, "文件中存在名称重复"),
    IMPORT_FILE_NAME_ERROR(17006, "文件名称错误"),
    QUERY_PARAM_ERROR(17007, "参数错误,请检查后重试"),
    DATA_NOT_EXIST(17007, "数据不存在,请检查后重试"),

    IMPORT_FILE_CODE_ERROR(14156, "导入文件统一社会信用代码重复"),
    IMPORT_FILE_HAVE_NULL(14157, "上传文件中有内容为空"),
    IMPORT_FILE_ERROR_DATE(14158, "导入文件与日期不符"),
    IMPORT_FILE_ERROR_TRADING_UNIT(14159, "交易序列导入错误"),
    IMPORT_FILE_ERROR_PRICE_MODE(14160, "定价方式错误"),


    IMPORT_FILE_UPLOAD_RUNNING(200, "上传中，请稍后查看结果"),

    /**
     * 电量数据
     * 14240-14269
     */
    ACTUAL_IMPORT_NOT_BOUND(17240, "用户未绑定"),
    ACTUAL_IMPORT_TIME_UNLIKE(17241, "用户日期错误与运行日期不一致"),
    IMPORT_EMPTY_BLANK(17242, "文件有信息空白，无法上传"),
    IMPORT_QUANTITY_ERROR(17243, "导入文件数量有误，无法上传"),
    IMPORT_ERROR(17244, "数据导入异常，请检查上传文件！"),
    IMPORT_NO_BIND_USER(17245, "不存在绑定用户"),

    /**
     * 交易安排
     * 17270-17299
     */
    TRANSACTION_SCHEDULE_OPE_NOT_FOUND(17270, "未找到运营平台交易安排明细数据"),
    TRANSACTION_SCHEDULE_MODE_NONSUPPORT_IMPORT(17271, "交易安排类型不支持导入"),
    TRANSACTION_SCHEDULE_IMPORT_IS_NULL(17272, "导入文件异常或者数据为空"),
    TRANSACTION_SCHEDULE_IMPORT_REPETITION(17273, "不能重复导入交易安排，请选择覆盖"),
    TRANSACTION_SCHEDULE_NOT_FIND(17274, "未找到交易安排明细"),
    TRANSACTION_SCHEDULE_FILE_ERROR(17275, "导入文件错误，请核对内容"),
    TRANSACTION_SCHEDULE_TIME_REPETITION(17276, "导入文件中时间重复"),
    TRANSACTION_SCHEDULE_SELL_NAME_ALREADY_EXIST(17277, "该售电用户已存在"),
    TRANSACTION_SCHEDULE_DUAL_LISTING_NOT_FOUND(17278, "双边协商详情未找到"),
    TRANSACTION_SCHEDULE_DUAL_EDIT_CURVE_ERROR(17279, "双边协商导入数据值异常，请检查"),
    TRANSACTION_SCHEDULE_DUAL_DATE_ERROR(17280, "时间月份超出交易安排时间"),
    TRANSACTION_SCHEDULE_TIME_OUT_OF_SCOPE(17281, "导入文件中有时间超出范围"),
    TRANSACTION_SCHEDULE_DUAL_LISTING_IS_NULL(17282, "双边协商导入有空数据"),
    TRANSACTION_SCHEDULE_DUAL_DATE_NOT_INCREASE(17283, "日期未递增"),

    /**
     * 双边协商
     * 14300-14329
     */
    DUAL_LISTING_NOT_FIND(14300, "未找到双边协商数据"),

    VPP_ELECTRIC_QUANTITY_TYPE_ERROR(14572, "类型不正确"),

    /**
     * 申报分析 模块
     * 15000-15100
     */
    ADJUST_DECLARE_CONFIRM_ERROR(15000, "确认失败，请检查数据是否正确"),
    ADJUST_DECLARE_EDIT_ERROR_NOT_FOUND(15001, "修改失败,请检查数据是否存在"),
    ADJUST_DECLARE_EDIT_ERROR_DATA_TOO_LONG(15002, "修改失败,数据时段超过限制"),
    ADJUST_DECLARE_UP_DECLARE_ERROR(15003, "确认申报上调节能力不能超过申报上调节能力"),
    ADJUST_DECLARE_DOWN_DECLARE_ERROR(15004, "确认申报下调节能力不能超过申报下调节能力"),
    ADJUST_DECLARE_DATA_INCOMPETENCE_ERROR(15005, "用户申报数据不全,无法确认"),
    ADJUST_DECLARE_DATA_REPEAT_CONFIRM(15006, "已确认,请勿重复确认"),

    /**
     * 基础信息模块
     * 15101 - 15150
     */
    BASE_INFO_ADD_ERROR(15101, "添加失败,请检查上传信息"),
    BASE_INFO_MAX_COMPARE_MIN_ERROR(15102, "添加失败,最小负荷不可以大于最大负荷"),

    /**
     * 申报策略模块
     * 15151 - 15200
     */
    DECLARATION_STRATEGY_ADD_ERROR(15151, "策略已确认,不可重新进行添加"),
    DECLARATION_STRATEGY_PARAM_ERROR(15152, "请检查全部时段数据是否完整"),

    /**
     * 负荷用户模块
     * 15201 - 15250
     */
    USER_BIND_DATA_ERROR(15201, "代理用户最低绑定期限为6个月"),
    USER_CONS_NO_ERROR(15202, "用户户号已存在或用户已和其他虚拟电厂绑定。"),
    USER_PHONE_REPEAT_ERROR(15203, "用户手机号重复或用户已和其他虚拟电厂绑定。"),
    UPDATE_PWD_ERROR(15204, "修改密码失败"),
    UPDATE_OLD_PWD_ERROR(15205, "旧密码不正确,请检查"),
    USER_CODE_REPEAT_ERROR(15206, "统一社会信用代码已存在或用户已和其他虚拟电厂绑定。"),
    USER_RESOLVE_DATA_ERROR(15207, "负荷用户全部无分解信息,请检查数据"),
    USER_EXIST_ERROR(15208, "用户已存在，请检查填写数据"),
    USER_BIND_WITH_OTHER_VPP_ERROR(15209, "用户已和其他虚拟电厂进行绑定"),
    USER_DATA_ERROR(15210, "用户数据异常"),

    /**
     * 小程序模块
     * 15201 - 15250
     */
    SMALL_ROUTINE_SPECIFIED(15201, "最大负荷,不能超过额定容量"),
    SMALL_ROUTINE_SPECIFIED_ZERO(15202, "最大负荷,不能小于0"),
    SMALL_ROUTINE_ZERO(15203, "最小负荷,不能小于0"),
    SMALL_ROUTINE_USER_ERROR(15204, "用户信息为空"),
    ADD_ERROR(15205, "用户信息为空"),
    SMALL_ROUTINE_SCHEME_AFFIRM(15206, "用户方案已确认,不可重新填报"),
    TIME_FRAME_ERROR(15207, "时段未申报"),
    TIME_FRAME_EMPTY(15208, "暂无时段"),


    /**
     * 响应能力分析模块
     * 15251 - 15300
     */
    ADJUST_DECLARE_SAVE_ERROR_OF_CONFIRM(15251, "数据已确认,无法进行修改"),
    //USER_CONS_NO_ERROR(15252, "用户户号已存在,请检查"),


    /**
     * 调节时段管理模块
     * 15301 - 15350
     */
    PERIOD_ERROR_VPP_LOSE(15301, "未找到虚拟电厂信息"),
    PERIOD_ADD_ERROR_PARAM_LOSE(15302, "时段添加失败,请选择时段配置"),
    PERIOD_QUERY_ERROR_DATA_DONT_EXIST(15303, "查询数据不存在,请检查后重试"),
    PERIOD_UPDATE_ERROR_ID_DONT_EXIST(15304, "修改失败,缺少ID"),
    PERIOD_UPDATE_ERROR_STATUS_ERROR(15305, "已申报配置,无法修改"),
    PERIOD_ERROR_BASE_LOSE(15306, "请先填写调节容量"),
    PERIOD_ERROR_PERIOD_NOT_FOUND(15307, "未找到对应时段,请检查后重试"),

    /**
     * 用户调节测试模块
     * 15351 - 15400
     */
    TEST_USER_USER_CODE_UNIQUE_ERROR(15351, "统一社会信用代码已存在,请检查后重试"),
    TEST_USER_LOAD_USER_CODE_UNIQUE_ERROR(15352, "统一社会信用代码已存在,请检查后重试"),
    TEST_USER_VPP_INFO_NOT_FOUND_ERROR(15353, "未找到虚拟电厂信息,请检查后重试"),
    TEST_USER_NOT_FOUND_ERROR(15354, "未找到测试用户信息,请检查后重试"),
    TEST_TASK_NOT_ADD_PAST_TASK_ERROR(15355, "无法添加小于等于当前时间的任务"),
    TEST_TASK_DATE_ERROR(15356, "请选择大于最新任务的执行时间"),
    TEST_TASK_EXCEL_ERROR(15357, "EXCEL文件解析错误,请检查文件是否正确"),
    TEST_TASK_EXCEL_EMPTY_ERROR(15358, "EXCEL内容为空,请检查后重试"),
    TEST_TASK_MAX_DONT_EQUALS_MIN(15359, "最大负荷测试时间不能等于最小负荷测试时间"),
    TEST_TASK_SPEED_TASK_NEED_KEEP_SAME(15360, "最大到最小测试时段或最小到最大测试时段需要与之前的设置保持一致"),
    TEST_TASK_EXCEL_DATA_LOSE(15361, "EXCEL数据不完整,请检查后重试"),

    /**
     * 用户绑定
     * 14090-14119
     */
    USER_IMPORT_MONTH_DATA_DISUNITY(14090, "绑定月份不统一，请检查导入信息"),
    USER_IMPORT_UNREGISTERED(14091, "该用户未注册"),
    USER_IMPORT_FILE_TOO_LONG(14092, "导入文件名称过长"),
    USER_IMPORT_FILE_IS_NULL(14093, "导入文件为空"),
    USER_IMPORT_NAME_IS_NULL(14094, "用户名称为空"),
    USER_IMPORT_CODE_IS_NULL(14095, "统一社会信用代码为空"),
    USER_IMPORT_CODE_ERROR(14096, "统一社会信用代码配置异常"),
    USER_LIST_DATE_IS_NOT_NULL(14097, "日期不能为空"),
    USER_LIST_START_AND_END_DATE_ERROR(14098, "开始日期要小于结束日期"),
    USER_LIST_DATE_ERROR(14099, "日期不能大于12个月"),
    USER_OLD_PLATFORM_USER_IMPORT_ERROR(14100, "旧平台绑定结算只能导入2022年12月31日前的用户"),
    USER_NEW_RETAIL_USER_IMPORT_ERROR(14101, "新零售签约用户只能导入2023年1月1日以后的用户"),
    USER_IMPORT_TRADING_UNIT_IS_NULL(14102, "交易序列为空"),
    USER_IMPORT_TIME_IS_NULL(14103, "生效或失效时间为空"),
    USER_IMPORT_PRICING_MODE_IS_NULL(14104, "定价方式为空"),
    USER_IMPORT_ORDER_IS_NULL(14105, "序号为空"),

    /**
     * 公有数据获取
     * 14120-14149
     */
    PUB_POWER_USER_ERROR(14120, "获取公有数据-电力用户列表失败"),

    /**
     * 用户负荷
     * 14330-14359
     */
    USER_NOT_EXIST(14330, "该用户不存在"),
    USER_LIST_NOT_EXIST(14331, "用户列表为空"),
    USER_FORCAST_DATA_ORIGIN_IS_UPDATE(14332, "预测数据源正在更新，请稍后再进行操作"),
    USER_PRODUCE_PLAN_IS_UPDATE(14333, "生产计划正在调整，请稍后再进行操作"),
    USER_FORCAST_ELE_IS_UPDATE(14334, "预测用电量正在调整，请稍后再进行操作"),

    /**
     * 用户结算
     * 14360-14389
     */
    SETTLEMENT_USER_NOT_EXIST(14360, "该用户结算信息不存在"),
    SETTLEMENT_TIME_RETAIL_PRICE_IS_NULL(14361, "请上传分时定价"),
    SETTLEMENT_OTHER_RETAIL_PRICE_IS_NULL(14362, "请完善电价"),
    FIXED_PRICE_TOO_BIG(14363, "固定价格超过上限价"),
    FIXED_PRICE_TOO_SMALL(14364, "固定价格小于下限价"),
    NEW_RETAIL_SETTLEMENT_ID_IS_NULL(14365, "结算用户为空"),
    NEW_RETAIL_SETTLEMENT_IS_NOT_BIND(14366, "未绑定结算数据"),
    NEW_RETAIL_SETTLEMENT_PRICE_TYPE_IS_NOT_SAME(14367, "请选择相同的定价方式"),
    NEW_RETAIL_SETTLEMENT_MONTH_IS_NOT_SAME(14368, "请选择同一月份进行完善"),
    NEW_RETAIL_SETTLEMENT_TRADING_UNIT_IS_NOT_SUPPORT(14369, "未支持的用户类型"),
    EXPORT_DATA_IS_NULL(14370, "导出数据为空"),
    NEW_RETAIL_CONTRACT_LIST_IS_NULL(14371, "零售合同管理用户为空"),

    /**
     * 结算管理
     * 14180-14209
     */
    SETTLEMENT_IMPORT_NOT_BOUND(14180, "用户未绑定"),
    SETTLEMENT_IMPORT_MONTH_DATA_DISUNITY(14181, "交易周期不统一，请检查导入信息"),
    SETTLEMENT_IMPORT_MONTH_ALREADY_EXIST(14182, "已存在该交易周期的结算方案，请先删除再进行导入"),
    SETTLEMENT_TIME_IMPORT_FILE_ERROR(14183, "导入文件异常，请检查文件表头"),
    SETTLEMENT_TIME_IMPORT_USERNAME_ERROR(14184, "用户名称错误"),
    SETTLEMENT_TIME_IMPORT_CYCLE_ERROR(14185, "导入周期异常"),
    SETTLEMENT_TIME_IMPORT_USERNAME_NOT_FIND(14186, "用户名称未找到"),
    SETTLEMENT_TIME_IMPORT_USERNAME_REPETITION(14187, "用户名称或周期文件内重复"),
    SETTLEMENT_TIME_IMPORT_TEN_DAYS_ERROR(14188, "未传入旬参数"),
    SETTLEMENT_USER_RETAIL_PRICE_NOT_COMPLETE(14189, "该结算用户零售电价未完善,请先进行完善"),
    DATA_EXISTENCE(14190, "当前月份已存在调节能力，请重新选择！"),
    CANNOT_DELETED(14191, "当前数据已应用于调节时段，不可删除!"),
    ;

    private final int code;
    private final String value;

    VirtualPowerPlantServiceCodeEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.value;
    }
}
