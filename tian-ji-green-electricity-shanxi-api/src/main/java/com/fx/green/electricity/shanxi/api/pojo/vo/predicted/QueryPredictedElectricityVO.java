package com.fx.green.electricity.shanxi.api.pojo.vo.predicted;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("用户调节能力详情VO")
public class QueryPredictedElectricityVO {

    @ApiModelProperty("时段")
    private List<String> timeFrame;

    @ApiModelProperty("数据")
    private List<PredictedElectricityInfo> dataList;

    @Data
    public static class PredictedElectricityInfo {


        @ApiModelProperty("用戶id")
        private Long userId;

        @ApiModelProperty("名字")
        private String name;

        @ApiModelProperty("1.虚拟电厂 2.用户")
        private Long type;

        @ApiModelProperty("虚拟电厂预测")
        private List<BigDecimal> predictedElectricityList;

        @ApiModelProperty("修改预测电量")
        private List<BigDecimal> electricityList;

    }

}
