package com.fx.green.electricity.shanxi.api.pojo.dto.production;

import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class VppUserProductionDataDeviceQueryDTO extends BaseDTO {

    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @ApiModelProperty("类型：adjust:调节设备，power:发电设备")
    @NotNull(message = "设备类型不能为空")
    private String type;

}
