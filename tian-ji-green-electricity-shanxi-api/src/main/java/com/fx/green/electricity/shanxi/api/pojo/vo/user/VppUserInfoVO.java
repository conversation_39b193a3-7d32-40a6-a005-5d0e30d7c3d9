package com.fx.green.electricity.shanxi.api.pojo.vo.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 用户信息 VO
 * 
 * <AUTHOR>
 */
@Data
public class VppUserInfoVO implements Serializable {

    private static final long serialVersionUID = 5091123814975496480L;

    @ApiModelProperty("负荷用户id")
    private Long id;

    @ApiModelProperty("负荷用户名称")
    private String name;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("统一社会信用代码")
    private String userCode;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @JsonFormat(pattern = "yyyy-MM")
    @ApiModelProperty("用户绑定周期开始")
    private Date bindCycleStart;

    @JsonFormat(pattern = "yyyy-MM")
    @ApiModelProperty("用户绑定周期结束")
    private Date bindCycleEnd;

    @ApiModelProperty("绑定状态")
    private Integer bindStatus;

    @ApiModelProperty("联系人姓名")
    private String contactName;

    @ApiModelProperty("联系人电话")
    private String contactPhone;

    @ApiModelProperty("微信绑定ID")
    private String openId;

    @ApiModelProperty("红利系数")
    private BigDecimal dividendRatio;

    @ApiModelProperty("户号信息")
    private List<VppUserAccountVO> accountList;

}
