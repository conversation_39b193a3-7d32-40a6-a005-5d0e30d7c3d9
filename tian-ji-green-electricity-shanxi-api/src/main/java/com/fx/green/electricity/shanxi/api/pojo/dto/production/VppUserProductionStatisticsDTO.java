package com.fx.green.electricity.shanxi.api.pojo.dto.production;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 用户生产统计
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VppUserProductionStatisticsDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 8445529111099945039L;
    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("负荷用户id")
    private Long userId;

    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateDay;

    @ApiModelProperty("类型：1.预计生产计划，2.实际生产情况")
    private Integer type;

    @ApiModelProperty("备注")
    @Size(max = 1000, message = "备注过长，请重新填写。")
    private String remark;

    @ApiModelProperty("生产状态：1.正常生产，2.检修，3.增产，4.减产，5.停产")
    @NotNull(message = "生产状态不能为空")
    private Integer productionStatus;

    @ApiModelProperty("生产状态详情：【月检】，【季检】，【年检】，【10%-100%】，【10%-100%】")
    private String productionStatusDetail;

    @Valid
    @ApiModelProperty("生产计划")
    private List<VppUserProductionStatisticsPlanDTO> planList;

    @Valid
    @ApiModelProperty("调节设备运行计划")
    private List<VppUserProductionStatisticsPlanDTO> adjustDevicePlanList;

    @Valid
    @ApiModelProperty("发电设备运行计划")
    private List<VppUserProductionStatisticsPlanDTO> powerDevicePlanList;

    @Data
    @ApiModel("用户生产统计-生产计划表VO")
    public static class VppUserProductionStatisticsPlanDTO {

        @ApiModelProperty("主键id")
        private Long id;

        @ApiModelProperty("用户生产统计主键id")
        private Long userProductionStatisticsId;

        @ApiModelProperty("计划类型：1.生产计划，2.调节设备运行计划，3.发电设备运行计划")
        private Integer planType;

        @ApiModelProperty("开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        @NotNull(message = "开始时间不能为空")
        private Date startTime;

        @ApiModelProperty("结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        @NotNull(message = "结束时间不能为空")
        private Date endTime;

        @ApiModelProperty("产品名称")
        @Size(max = 50, message = "产品名称过长，请重新填写。")
        private String name;

        @ApiModelProperty("规格")
        @Size(max = 50, message = "规格过长，请重新填写。")
        private String specifications;

        @ApiModelProperty("产量（吨）")
        @Digits(integer = 8, fraction = 2, message = "整数部分最多8位，小数部分最多2位")
        @DecimalMin(value = "0.00",  message = "产量必须为正数")
        private BigDecimal production;

        @ApiModelProperty("设备id")
        private Long deviceId;

        @ApiModelProperty("连续运行时长")
        @Digits(integer = 8, fraction = 2, message = "整数部分最多8位，小数部分最多2位")
        @DecimalMin(value = "0.00",  message = "连续运行时长必须为正数")
        private BigDecimal continuousOperationDuration;

        @ApiModelProperty("运行功率/发电功率")
        @Digits(integer = 8, fraction = 2, message = "整数部分最多8位，小数部分最多2位")
        @DecimalMin(value = "0.00",  message = "运行功率/发电功率必须为正数")
        private BigDecimal operatingPower;


    }

}
