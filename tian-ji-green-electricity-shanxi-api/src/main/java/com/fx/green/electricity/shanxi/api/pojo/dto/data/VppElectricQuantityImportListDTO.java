package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("批量导入数据")
public class VppElectricQuantityImportListDTO implements Serializable {

    private static final long serialVersionUID = 7986047323875189252L;

    @ApiModelProperty("主键")
    private List<VppElectricQuantityDTO> list;

}