package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 数据维护入参DTO
 */
@Data
@ApiModel("数据维护DTO")
@EqualsAndHashCode(callSuper = true)
public class DataMaintenanceDTO extends BaseDTO {

    @ApiModelProperty("数据查询月份")
    @NotNull(message = "数据查询月份不能为空")
    @JsonFormat(pattern = "yyyy-MM")
    private Date queryMonth;
    
}
