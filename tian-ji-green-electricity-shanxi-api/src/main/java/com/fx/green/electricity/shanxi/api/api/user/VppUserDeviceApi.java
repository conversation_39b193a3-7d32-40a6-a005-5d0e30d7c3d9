package com.fx.green.electricity.shanxi.api.api.user;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDeviceAndMeterDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserDeviceAndMeterVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 用户设备及电表管理 Api 接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppUserDeviceApi")
public interface VppUserDeviceApi {

    /**
     * 保存或修改
     *
     * @param param 用户设备及电表DTO
     * @return 成功/失败
     */
    @PostMapping("/vppUserDevice/saveOrUpdate")
    DataResult<Void> saveOrUpdate(@RequestBody VppUserDeviceAndMeterDTO param);

    /**
     * 查询绑定周期列表
     *
     * @param param 用户户号
     * @return 用户设备及电表VO
     */
    @PostMapping("/vppUserDevice/getUserDeviceAndMeterByConsNo")
    DataResult<VppUserDeviceAndMeterVO> getUserDeviceAndMeterByConsNo(@RequestBody VppUserDeviceAndMeterDTO.VppUserDeviceDTO param);

}
