package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("调节能力分析DTO")
public class AdjustDeclareDTO {

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("用户统计DTO")
    public static class UserCountDTO extends BaseDTO {

        @NotNull(message = "查询时间不能为空")
        @JsonFormat(pattern = "yyyy-MM-dd")
        @ApiModelProperty("查询时间")
        private Date queryDate;

        @NotNull(message = "查询类型不能为空")
        @ApiModelProperty("类型(1-虚拟电厂，2-负荷用户)")
        private Integer type;
        @ApiModelProperty("用户ID")
        private Long userId;

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("用户统计DTO")
    public static class QueryLineChartDTO extends UserCountDTO {

        @ApiModelProperty("时段全称")
        private String period;

        @ApiModelProperty("时段开始")
        private String periodStart;

        @ApiModelProperty("时段结束")
        private String periodEnd;

        @ApiModelProperty("是否确认(0-否，1-是)")
        private Integer confirm;

    }

    @Data
    @ApiModel("确认申报调节数据DTO")
    public static class ConfirmDataDTO {

        @NotNull(message = "查询时间不能为空")
        @JsonFormat(pattern = "yyyy-MM-dd")
        @ApiModelProperty("查询时间")
        private Date queryDate;
        @NotNull(message = "负荷用户Id不能为空")
        @ApiModelProperty("用户ID")
        private Long userId;
    }

    @Data
    @ApiModel("编辑申报调节数据DTO")
    public static class EditDataDTO {

        @NotEmpty(message = "请填写确认申报数据")
        @ApiModelProperty("数据详情")
        private List<DataDetailDTO> dataList;

    }

    @Data
    @ApiModel("数据详情")
    public static class DataDetailDTO {

        @ApiModelProperty("申报详情id")
        private Long id;
        @ApiModelProperty("确认申报上调节能力列表")
        private BigDecimal upDeclareList;

        @ApiModelProperty("确认申报下调节能力列表")
        private BigDecimal downDeclareList;

        @ApiModelProperty("时段")
        private Integer period;
    }

    @Data
    @ApiModel("查询用户树形结构DTO")
    @EqualsAndHashCode(callSuper = true)
    public static class QueryUserTreeDTO extends BaseDTO {

        @NotNull(message = "请选择时间")
        @ApiModelProperty("日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date queryDate;

        @ApiModelProperty("名称")
        private String name;

        @ApiModelProperty("用户id")
        private Long userId;
    }
}
