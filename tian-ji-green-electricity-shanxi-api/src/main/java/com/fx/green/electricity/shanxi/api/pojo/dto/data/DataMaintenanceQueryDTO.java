package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 数据维护入参DTO
 */
@Data
@ApiModel("数据维护入参DTO")
@EqualsAndHashCode(callSuper = true)
public class DataMaintenanceQueryDTO extends BaseDTO {

    @ApiModelProperty("日期")
    @NotNull(message = "日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateDay;
    
}
