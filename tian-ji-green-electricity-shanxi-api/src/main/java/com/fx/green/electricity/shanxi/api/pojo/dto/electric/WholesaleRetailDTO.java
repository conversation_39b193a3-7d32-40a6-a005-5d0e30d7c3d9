package com.fx.green.electricity.shanxi.api.pojo.dto.electric;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 批发市场分析DTO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel("批发市场分析DTO")
@EqualsAndHashCode(callSuper = true)
public class WholesaleRetailDTO extends BaseDTO {

    private static final long serialVersionUID = 826157579858065192L;
    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty("用户id")
    private Long userId;


    @ApiModelProperty("0全部时段 1调控时段 2非调控时段")
    private Integer type;

    @ApiModelProperty("是否计算红利 1是 2否")
    private Integer flag;

    @ApiModelProperty("多个用户id")
    private List<VppUserDTO> userList;

    @ApiModelProperty("1批零收益 2收益均价 3零售均价 4批发均价")
    private Integer rankType;

    @ApiModelProperty("是否参与分摊结算 1参与(开) 2不参与(关)")
    private Integer shareType;

}
