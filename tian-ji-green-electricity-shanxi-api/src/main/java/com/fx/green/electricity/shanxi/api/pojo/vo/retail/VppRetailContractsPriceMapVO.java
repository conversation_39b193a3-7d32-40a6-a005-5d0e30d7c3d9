package com.fx.green.electricity.shanxi.api.pojo.vo.retail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
*
**/
@Data
@ApiModel("2025零售合同导入使用价格")
public class VppRetailContractsPriceMapVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("零售合约月份")
    private Date signingMonth;
    @ApiModelProperty("时段")
    private String timeFrame;
    @ApiModelProperty("上旬出清电价")
    private BigDecimal firstDecadePrice;
    @ApiModelProperty("上旬成交电量")
    private BigDecimal firstDecadeClinchDeal;
    @ApiModelProperty("中旬出清电价")
    private BigDecimal secondDecadePrice;
    @ApiModelProperty("中旬成交电量")
    private BigDecimal secondDecadeClinchDeal;
    @ApiModelProperty("下旬出清电价")
    private BigDecimal thirdDecadePrice;
    @ApiModelProperty("下旬成交电量")
    private BigDecimal thirdDecadeClinchDeal;
    @ApiModelProperty("月度出清电价")
    private BigDecimal monthlyPrice;
    @ApiModelProperty("月度成交电量")
    private BigDecimal monthlyClinchDeal;
    @ApiModelProperty("全部天数")
    private Integer totalDays;
    @ApiModelProperty("上旬天数")
    private Integer firstDecadeDays;
    @ApiModelProperty("中旬天数")
    private Integer secondDecadeDays;
    @ApiModelProperty("下旬天数")
    private Integer thirdDecadeDays;

}