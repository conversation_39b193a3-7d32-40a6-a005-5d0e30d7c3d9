package com.fx.green.electricity.shanxi.api.pojo.dto.predicted;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("预测电量dto")
public class QueryPredictedElectricityDTO extends BaseDTO {

    @ApiModelProperty("预测维度 1.日 2.上旬 3.中旬 4.下旬 5.月")
    @NotNull(message = "预测维度不能为空")
    private Integer dimension;

    @ApiModelProperty("分时维度 1.96点 2.24点")
    @NotNull(message = "分时维度不能为空")
    private Integer timeSharing;

    @ApiModelProperty("预测日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "预测日期不能为空")
    private Date queryDate;

    @ApiModelProperty("参考日")
    private List<String> referenceDate;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("1.获取绑定周期用户 2.获取全部用户")
    private Integer type;

    @ApiModelProperty("1.预测数据 2.对比数据")
    private Integer flag;

}
