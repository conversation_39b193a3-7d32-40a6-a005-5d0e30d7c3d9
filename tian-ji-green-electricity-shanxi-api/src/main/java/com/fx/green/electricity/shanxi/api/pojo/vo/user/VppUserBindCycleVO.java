package com.fx.green.electricity.shanxi.api.pojo.vo.user;

import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 绑定周期 VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("绑定周期")
public class VppUserBindCycleVO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("绑定周期id")
    private Long id;

    @ApiModelProperty("负荷用户id")
    private Long userId;

    @ApiModelProperty("用户绑定周期开始")
    private Date bindCycleStart;

    @ApiModelProperty("用户绑定周期结束")
    private Date bindCycleEnd;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("创建时间")
    private Date createTime;

}