package com.fx.green.electricity.shanxi.api.pojo.dto.retail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("修改用户的红利系数")
public class UpdateRetailContractsManageDTO extends BaseDTO {


    private List<RetailContractsManageDTO> list;

    @Data
    public static class RetailContractsManageDTO {
        @JsonFormat(pattern = "yyyy-MM")
        private Date signingMonth;
        @ApiModelProperty("retailContractsId")
        private Long retailContractsId;
        @ApiModelProperty("用户id")
        private Long userId;
        @ApiModelProperty("名字")
        private String name;
        @ApiModelProperty("红利系数")
        private BigDecimal dividendSharCoefficient;
    }

}
