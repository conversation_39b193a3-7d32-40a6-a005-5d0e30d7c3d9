package com.fx.green.electricity.shanxi.api.pojo.dto.electric;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 中长期超额申报考核计算DTO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel("中长期超额申报考核计算DTO")
@EqualsAndHashCode(callSuper = true)
public class ExcessMLDeclareCalDTO extends BaseDTO {

    private static final long serialVersionUID = -9166979302608571839L;
    @ApiModelProperty("月份")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date month;


}
