package com.fx.green.electricity.shanxi.api.pojo.dto.predicted;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("确定虚拟电厂预测电量")
public class ConfirmPredictionDTO extends BaseDTO {

    @ApiModelProperty("用戶id")
    private Long userId;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("预测维度 1.日 2.上旬 3.中旬 4.下旬 5.月")
    private Integer dimension;

    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateDay;

    @ApiModelProperty("预测日期")
    private String predictionDate;

    @ApiModelProperty("虚拟电厂预测")
    private List<BigDecimal> electricityList;


}
