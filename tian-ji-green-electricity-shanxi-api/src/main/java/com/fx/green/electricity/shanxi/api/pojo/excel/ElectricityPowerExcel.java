package com.fx.green.electricity.shanxi.api.pojo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("电能量申报数据导入")
public class ElectricityPowerExcel implements Serializable {
    private static final long serialVersionUID = -6889251754683675726L;

    @ExcelProperty(value = "序号", index = 0)
    private Integer index;
    @ExcelProperty(value = "时段", index = 1)
    private String timeFrame;
    @ExcelProperty(value = "电量(MWh)", index = 2)
    private String electricity;

    @ApiModelProperty("申报日期")
    private String infoDate;
    @ApiModelProperty("标准时点数据")
    private String timePoint;
}
