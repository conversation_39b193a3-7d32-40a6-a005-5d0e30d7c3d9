package com.fx.green.electricity.shanxi.api.api.file;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.vo.file.VppFileUploadVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件上传 Api 接口
 *
 * <AUTHOR>
 **/
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppFileApi")
public interface VppFileApi {

    /**
     * 上传文件
     *
     * @param file 文件
     * @return 文件地址
     */
    @PostMapping(value = "/file/uploadFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    DataResult<VppFileUploadVO> uploadFile(@RequestPart("file") MultipartFile file);

    /**
     * 上传多个文件
     *
     * @param files 文件
     * @return 文件地址
     */
    @PostMapping(value = "/file/uploadFiles", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    DataResult<List<VppFileUploadVO>> uploadFiles(@RequestPart("files") MultipartFile[] files);

}
