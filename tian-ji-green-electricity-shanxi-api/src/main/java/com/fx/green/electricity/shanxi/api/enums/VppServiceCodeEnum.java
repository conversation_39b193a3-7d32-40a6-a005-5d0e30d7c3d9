package com.fx.green.electricity.shanxi.api.enums;

import com.fx.common.exception.FxServiceCode;


/**
 * <AUTHOR>
 */
public enum VppServiceCodeEnum implements FxServiceCode {


    /**
     * 12000-12029 运营用户与登录权限
     */
    TOKEN_NO_SET(12000, "token过期时间未设置"),
    USERNAME_EMPTY(12001, "用户名不能为空"),
    USERNAME_TO_LONG(12002, "用户名最多50位字符"),
    PASSWORD_EMPTY(12003, "密码不能为空"),
    GRANT_TYPE_EMPTY(12004, "认证类型为空"),
    GRANT_TYPE_NOT_SUPPORT(12005, "不支持的认证类型"),
    USERNAME_PWD_ERROR(12006, "用户名或密码错误"),
    USER_DISABLE(12007, "用户已被禁用,请联系管理员"),
    AFFIRM_PASSWORD_ERROR(12008, "确认密码输入错误"),
    PASSWORD_NOT_THE_SAME(12009, "新密码与旧密码不能相同"),
    LOGIN_FAIL_COUNT_TOO_MUCH(12010, "密码输入错误次数已达上限，请60分钟后再登录，或联系客服重置密码"),
    PASSWORD_ERROR(12011, "密码错误"),
    REQUEST_TOKEN_ERROR(12012, "token格式错误"),
    USERNAME_ERROR(120013, "用户名不存在"),
    USER_EXPIRE(120014, "用户已过期,请联系管理员"),


    /**
     * 12030-12059 运营菜单管理
     */
    MENU_PATH_REPETITION(12030, "菜单路由重复，请检查"),
    MENU_SELECT_IS_NULL(12031, "用户未配置菜单权限"),
    MENU_PERMS_CANT_SET_ADMIN(12032, "菜单权限不能设置为超级用户权限"),
    MENU_PERMS_REPETITION(12033, "菜单权限重复，请检查"),
    MENU_NOT_FOUND(12034, "菜单未找到"),

    /**
     * 12060-12089 运营用户管理
     */
    OPE_USER_PWD_ERROR(12060, "用户密码解析异常"),
    OPE_USER_PWD_EMPTY(12061, "密码不能为空，请检查密码参数"),
    OPE_USER_NONENTITY(12062, "用户不存在，请检查参数"),
    OPE_USERNAME_REPETITION(12063, "用户名重复，请修改用户名"),
    OPE_NAME_REPETITION(12064, "姓名重复，请修改"),
    OPE_USER_NOT_FOUND(12065, "编辑用户未找到"),
    OPE_EDIT_ADMIN_ERROR(12066, "超级用户不允许编辑"),

    /**
     * 12090-12119 企业管理 （租户管理）
     */
    TENANT_INFO_REPETITION(12090, "企业名称或企业信用代码已被占用"),
    TENANT_UNKNOWN_PARENT(12091, "未找到父节点企业"),
    TENANT_UNKNOWN_TENANT(12092, "未找到操作的企业"),
    TENANT_ASSOC_REPETITION(12093, "该企业已关联，请不要重复关联"),
    TENANT_NOT_FOUND(12094, "租户未找到"),
    TENANT_AREA_NOT_FOUND(12095, "未找到行政区域"),
    TENANT_TRADING_UNIT_NOT_FOUND(12096, "未找到该交易单元"),
    TENANT_TRADING_UNIT_REPETITION(12097, "交易单元名称已被占用"),
    TENANT_SUB_COMPANY_CANT_ADD_TENANT(12098, "分公司不能添加子公司"),
    TENANT_TRADING_UNIT_NAME_NOT_PUNCTUATION(12099, "交易单元名称中不能存在其他符号"),
    TENANT_ASSOC_NULL(12100,"添加集团账号前请先关联下属公司"),

    /**
     * 12120-12149 租户角色管理
     */
    TENANT_ROLE_EXIST(12120, "角色已存在，无法添加"),
    TENANT_ROLE_NOT_FOUND(12121, "角色不存在，请检查参数"),
    TENANT_PARENT_ROLE_NOT_FOUND(12122, "未找到上级岗位，请重新选择"),
    TENANT_ROLE_HAVE_SUB(12123, "角色存在下级岗位，不允许删除"),
    TENANT_ROLE_TENANT_NOT_FOUND(12124, "企业信息未找到"),
    TENANT_ROLE_USE_BY_USER(12125, "角色已被用户使用，不允许删除"),
    TENANT_ROLE_DELETE_ADMIN_ERROR(12125, "不允许删除超级管理员角色"),
    TENANT_ROLE_EDIT_ADMIN_ERROR(12126, "不允许编辑超级管理员角色"),
    TENANT_ROLE_EDIT_PARENT_ERROR(12127, "不能选择自己为上级岗位"),
    TENANT_VALID_PERIOD_OVER(12128,"企业服务已到期"),
    TENANT_NEED_PRODUCT_TYPE(12129,"企业未配置应用"),

    /**
     * 12150-12179 业态管理
     */
    OPE_BUSINESS_UNKNOWN_BUSINESS(12150, "未找到操作的业态"),
    OPE_BUSINESS_NAME_EXIST(12151, "业态名称重复"),
    OPE_BUSINESS_CODE_EXIST(12152, "业态编码重复"),

    /**
     * 12180-12209 售电接口管理
     */
    TENANT_INTERFACE_APP_ID_EMPTY(12180, "AppId不可为空"),
    TENANT_INTERFACE_APP_SECRET_EMPTY(12181, "AppSecret不可为空"),
    TENANT_INTERFACE_PUBLIC_KEY_EMPTY(12182, "公钥不可为空"),
    TENANT_INTERFACE_PRIVATE_KEY_EMPTY(12183, "AES密钥不可为空"),
    TENANT_INTERFACE_OFFSET_EMPTY(12184, "AES向量偏移量不可为空"),
    TENANT_INTERFACE_UNKNOWN_INTERFACE(12185, "该企业下没有接口，无法删除"),
    TENANT_INTERFACE_ID_EXIST(12186, "该企业已存在接口，无法重复添加"),
    TENANT_INTERFACE_CONFIGURATION_ERROR(12187, "访问参数解析异常"),
    TENANT_INTERFACE_ENCRYPT_ERROR(12188, "访问参数加密异常"),
    TENANT_INTERFACE_UNKNOWN_ERROR(12189, "未找到该接口"),


    /**
     * 12210-12239 用户账号管理
     */
    TENANT_USER_NOT_FOUND(12210, "编辑用户未找到"),
    TENANT_USER_NONENTITY(12211, "用户不存在，请检查参数"),
    TENANT_USER_USERNAME_REPETITION(12212, "用户名重复，请修改用户名"),
    TENANT_USER_NAME_REPETITION(12213, "姓名重复，请修改"),
    TENANT_USER_STATUS_ERROR(12214, "请输入正确的状态信息"),
    TENANT_USER_TENANT_STATUS_ERROR(12215, "所属企业被停用，不能启用该用户"),
    TENANT_USER_EXCEPTION_POSSIBLE_TOURIST(12216, "操作异常，可能的原因是游客登录"),
    TENANT_USER_DONT_HAVE_ROLE(12217, "用户没有该应用的权限"),
    TENANT_USER_TENANT_NOT_FOUND(12218, "用户租户不存在"),
    TENANT_SUB_TENANT_NOT_FOUND(12219, "选择分公司未找到"),
    TENANT_APP_NOT_FOUND(12220, "未找到应用"),

    /**
     * 12240-12269 子系统管理
     */
    OPE_APP_CODE_EMPTY(12240, "子系统编码不可为空"),
    OPE_APP_CODE_EXIST(12241, "子系统唯一编码已存在，不可重复添加"),
    OPE_APP_NOT_FOUND(12242, "未找到选择的子系统"),
    OPE_APP_USE_BY_USER(12243, "子系统下有关联角色，无法进行删除"),
    OPE_APP_TENANT_NOT_NULL(12244,"当前版本有企业在使用中不可删除"),

    /**
     * 12270-12299 运营菜单管理
     */
    TENANT_MENU_APP_NOT_FOUND(12270, "未找到子系统，无法删除"),
    TENANT_MENU_PERMS_CANT_SET_ADMIN(12271, "菜单权限不能设置为超级用户权限"),
    TENANT_MENU_PERMS_REPETITION(12272, "菜单权限重复，请检查"),
    TENANT_MENU_PATH_REPETITION(12273, "菜单路由重复，请检查"),
    TENANT_MENU_NOT_FOUND(12274, "未找到菜单"),
    TENANT_MENU_EXIST_KID_MENU(12275, "该菜单存在子菜单,无法进行删除"),
    TENANT_MENU_EXIST_ASSOC_USER(12276, "存在与菜单关联的用户，无法进行删除"),


    /**
     * 12200-12229 文件上传
     */
    FILE_TYPE_NOT_FOUND(12200, "文件类型未找到"),
    FILE_SIGNATURE_FAILED(12201, "签名获取失败"),
    FILE_LOSE_EFFICACY(12202, "文件已失效，请重新上传后再次提交"),

    /**
     * 12230-12259 出清异常
     */
    CLEARING_GENERATE_FAILED(12230, "生成当日出清信息失败"),
    CLEARING_EDIT_TIME_ERROR(12231, "修改时间参数异常"),
    CLEARING_TASK_ERROR(12232, "出清任务配置异常"),

    /**
     * 12260-12289 交易安排异常
     */
    ARRANGE_DETAIL_PERIOD(12260, "交易安排详情周期未选择"),
    ARRANGE_DETAIL_PERIOD_QUARTER(12261, "交易安排详情季度选择错误"),
    ARRANGE_DETAIL_NOT_FOUND(12262, "交易安排季详情未找到"),
    ARRANGE_DETAIL_TIME_ERROR(12263, "时间不能选择当前时间以前"),
    ARRANGE_DETAIL_NAME_REPETITION(12264, "交易安排详情名称重复"),
    ARRANGE_DETAIL_DB_TIME_ERROR(12265, "不能修改当前时间以前的数据"),
    ARRANGE_IMPORT_FILE_TYPE_ERROR(12266, "文件导入异常，请检查文件格式"),
    ARRANGE_IMPORT_EMPTY_ERROR(12267, "导入文件为空"),
    ARRANGE_IMPORT_TEMPLATE_ERROR(12268, "请检查上传的文件内容"),
    ARRANGE_IMPORT_PARAM_ERROR(12269, "导入参数选择错误"),
    UNIT_BASE_CAPACITY(12270,"机组信息装机容量不能为空"),
    UNIT_COVERY_DATA_ERROR(12271,"excel中机组名称重复，请检查"),

    /**
     * 12290-12319 接入关口表数据异常
     */
    METER_SERVER_ERROR(12290, "关口服务异常"),
    METER_INFO_IMPORT_FAILED(12291, "关口表导入失败,原因可能是上传的文件格式与模板格式不一致"),
    METER_INFO_IMPORT_NULL(12292, "关口表导入的数据为空，请检查文件重新导入"),
    METER_FILE_NOT_FOUND(12293, "关口表导入的文件内容为空，请检查文件内容"),
    METER_INFO_TEMPLATE_MISMATCHING(12294, "上传的文件格式与模板格式不一致，请检查后重新上传"),
    METER_INFO_FILE_NOT_EXCEL(12295, "文件不是Excel文件,请检查文件格式"),
    METER_INFO_FILE_TEMPLATE_ERROR(12296, "请检查上传的关口表文件内容"),
    METER_INFO_NOT_MATCHING_TENANT(12297, "未匹配到电力用户"),
    WHETHER_METER_NOT_NULL(12298, "未找到拥有关口表的电力用户"),

    /**
     * 12320-12349 中长期数据
     */
    ARRANGE_IMPORT_TYPE_ERROR(12320, "导入的详情类型异常"),
    DOWNLOAD_FILE_ERROR(12321, "文件下载失败"),

    /**
     * 12350-12379
     */
    UNIT_MAINTAIN_TIME_TO_REPEAT(12350, "导入开始时间重复"),
    UNIT_IMPORT_EMPTY_ERROR(12351, "导入文件为空"),

    /**
     * 12380-12409
     */
    IMPORT_IN_PROGRESS(12380, "导入正在进行"),
    IMPORT_IS_NOT_IN_PROGRESS(12381, "本类型导入状态当前不是正在进行"),

    /**
     * 12410-12439 市场化指标
     */
    ANALYSIS_POINTER_TYPE_ERROR(12410, "市场化指标类型错误"),
    GENERATE_RLECTRICITY_FIRM_NOT_FIND(12411, "未找到相关的发电企业"),

    /**
     * 12440-12469
     */
    NON_MARKETABLE_UNIT_NOT_FIND(12410, "未找到相关的非市场化机组"),

    /**
     * 12470-12499
     */
    MAINTENANCE_INFORMATION_DEVICE_INFO_NOT_FIND(12470, "未找到相关的设备信息"),

    /**
     * 12500-12529
     */
    REQUEST_EMPTY(12500, "当前请求参数为空或数据缺失，请联系管理员"),

    /**
     * 12530-12559 自定义规则模板
     */
    CODE_EXIST(12530, "code已存在"),
    ID_NOT_EXIST(12531, "ID不存在"),
    CODE_REPEAT(12532, "存在重复code"),
    MODEL_ERROR(12533, "自定义模板未找到版本名称与运行时间，请确认自定义规则内容是否正确!"),
    DATA_REPEAT(12533, "版本名称与运行时间重复"),

    /**
     * 12560-12589 交易单元详情
     */
    UNIT_NOT_EXIST(12560, "机组不存在"),
    UNIT_EXIST(12561, "机组已存在"),

    /**
     * 12590-12529 交易安排
     */
    TRANSACTION_NOT_EXIST(12590, "交易安排信息不存在"),
    TRANSACTION_EXIST(12591, "交易名称已存在"),
    TRANSACTION_QUARTER_ERROR(12592, "当交易周期为季度时，季度参数不可为空"),
    TRANSACTION_DATE_CONVERTER(12593, "标的日转换异常"),
    TRANSACTION_DATE_ERROR(12594, "不允许更新或删除今日之前的交易安排!"),

    /**
     * 12620-12649 角色权限
     */
    TENANT_ROLE_PERMISSION_EXIST(12649, "角色数据权限已存在,无法新增"),
    TENANT_ROLE_PERMISSION_NOT_EXIST(12650, "租户角色应用权限未找到"),

    /**
     * 12650-12679 租户用户应用权限
     */
    TENANT_USER_APP_NOT_EXIST(12650, "租户用户应用权限未找到"),

    /**
     * 12680-12709 缩减基准
     */
    OPERATION_TIME_NOT_REPETITION(12651,"运行时间不可重复"),

    /**
     * 12710-12739 公有数据
     */
    PUBLIC_DATA_PARAMETER_DTO_ERROR_DATE(12710,"时间搜索条件不可为空"),
    PUBLIC_DATA_PARAMETER_LIST_ERROR(12710,"参数错误"),


    /**
     * 12740-12769 火电
     */
    FIRE_DATA_ALREADY_EXISTS(12740,"数据已存在,请检查"),
    FIRE_DATA_EMPTY(12741,"参数为空"),
    FIRE_EXCEL_DATA_ERROR(12742,"最小技术出力应该小于额定出力"),
    FIRE_UNIT_EXIT(12743,"机组今日数据已配置"),
    FIRE_UNIT_LAST(12744,"此机组参数为最后一条数据，不可删除"),
    FIRE_HEARTING_COVER(12745,"机组供热周期重复，请检查"),
    FIRE_MIN_EXERT_ERROR(12746,"最小技术出力应该小于最大技术出力或者大于等于额定出力的30%"),
    FIRE_FM_PERFORMANCE_ERROR(12747,"调频性能指标数值在0-10之间"),
    FIRE_MAX_EXERT_ERROR(12748,"出力上限应该小于额定出力"),
    FIRE_HEARTING_START_TIME(12749,"供热机组开始时间应在今天之后"),
    FIRE_HEARTING_END_TIME(12750,"供热机组结束时间应在开始时间之后"),

    /**
     * 12770-12799 新能源
     */
    NEW_ENERGY_TYPE(12770,"交易周期为空，请检查"),
    NEW_ENERGY_TIME_EMPTY(12771,"标的日起始时间为空，请检查"),
    NEW_ENERGY_PARAM_EMPTY(12772,"参数为空"),
    NEW_ENERGY_NULL(12773,"查询无结果"),
    NEW_UNIT_EMPTY_ERROR(12774,"没有该交易单元，请配置"),
    NEW_USER_TRADING_EMPTY(12775,"该用户下没有交易单元"),
    NEW_TENANT_NULL(12776,"没有获取到租户"),


    /**
     * 12800-12829
     */
    ASSOC_TENANT_REPETITION(12800,"该用户已经配置"),
    ;

    private final int code;
    private final String value;

    VppServiceCodeEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.value;
    }
}



