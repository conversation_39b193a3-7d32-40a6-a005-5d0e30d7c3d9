package com.fx.green.electricity.shanxi.api.pojo.vo.production;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VppUserProductionStatisticsListVO extends BaseVO {

    private static final long serialVersionUID = 5091123814975496480L;

    @ApiModelProperty("用户数")
    private int userNum;

    @ApiModelProperty("已填生产计划")
    private String expectedIsWrite;

    @ApiModelProperty("未填生产计划")
    private String expectedIsNotWrite;

    @ApiModelProperty("已填实际生产")
    private String actualIsWrite;

    @ApiModelProperty("未填实际生产")
    private String actualIsNotWrite;

    @ApiModelProperty("用户生产统计")
    private List<UserProductionStatisticsPlanVO> list;

    @Data
    @ApiModel("用户生产统计")
    public static class UserProductionStatisticsPlanVO {

        @ApiModelProperty("负荷用户id")
        private Long userId;

        @ApiModelProperty("负荷用户名称")
        private String userName;

        @ApiModelProperty("日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date dateDay;

        @ApiModelProperty("预计已填：1.未填写，2.已填写")
        private Integer expectedIsWrite;

        @ApiModelProperty("实际已填：1.未填写，2.已填写")
        private Integer actualIsWrite;

        @ApiModelProperty("生产状态：1.正常生产，2.检修，3.增产，4.减产，5.停产")
        @NotNull(message = "生产状态不能为空")
        private Integer expectedProductionStatus;

        @ApiModelProperty("生产状态详情：【月检】，【季检】，【年检】，【10%-100%】，【10%-100%】")
        private String expectedProductionStatusDetail;

        @ApiModelProperty("生产状态：1.正常生产，2.检修，3.增产，4.减产，5.停产")
        @NotNull(message = "生产状态不能为空")
        private Integer actualProductionStatus;

        @ApiModelProperty("生产状态详情：【月检】，【季检】，【年检】，【10%-100%】，【10%-100%】")
        private String actualProductionStatusDetail;

        @ApiModelProperty("预计生产计划")
        private List<ProductionStatisticsPlanVO> expectedPlanList;

        @ApiModelProperty("实际生产")
        private List<ProductionStatisticsPlanVO> actualList;
    }


    @Data
    @ApiModel("用户生产统计-生产计划详情VO")
    public static class ProductionStatisticsPlanVO {

        @ApiModelProperty("计划类型：1.生产计划，2.调节设备运行计划，3.发电设备运行计划")
        private Integer planType;

        @ApiModelProperty("生产开始时间")
        private String startTime;

        @ApiModelProperty("生产结束时间")
        private String endTime;

        @ApiModelProperty("产品名称")
        private String name;

        @ApiModelProperty("产量（吨）")
        private BigDecimal production;

        @ApiModelProperty("调节设备名称")
        private String adjustDeviceName;

        @ApiModelProperty("发电设备")
        private String powerDeviceName;

        @ApiModelProperty("连续运行时长")
        private BigDecimal continuousOperationDuration;

        @ApiModelProperty("运行功率/发电功率")
        private BigDecimal operatingPower;

    }


}
