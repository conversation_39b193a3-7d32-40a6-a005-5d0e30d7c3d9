package com.fx.green.electricity.shanxi.api.pojo.vo.dictionary;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 字典表 VO
 * 
 * <AUTHOR>
**/
@Data
@ApiModel("字典表")
@JsonInclude(value= JsonInclude.Include.NON_NULL)
public class VppDictionaryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("编号")
    private String value;

    @ApiModelProperty("编号名字")
    private String label;

    @ApiModelProperty("子节点")
    private List<DataInfo> children;

    @Data
    @JsonInclude(value= JsonInclude.Include.NON_NULL)
    public static class DataInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty("编号")
        private String value;

        @ApiModelProperty("编号名字")
        private String label;

        @ApiModelProperty("子节点")
        private List<DataInfos> children;
    }

    @Data
    @JsonInclude(value= JsonInclude.Include.NON_NULL)
    public static class DataInfos implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("编号")
        private String value;

        @ApiModelProperty("编号名字")
        private String label;
    }

}