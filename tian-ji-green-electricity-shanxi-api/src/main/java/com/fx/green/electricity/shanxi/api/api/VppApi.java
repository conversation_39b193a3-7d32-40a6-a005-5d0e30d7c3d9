package com.fx.green.electricity.shanxi.api.api;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.GetUpdateTimeDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.ImportExcelDetailPageListVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 数据维护 Api 接口
 *
 * <AUTHOR>
 **/
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppApi")
public interface VppApi {

    /**
     * 查询实际用电量导入记录
     *
     * @param param 查询参数
     * @return 导入记录
     */
    @PostMapping("vppElectricQuantity/queryImportRecord")
    DataResult<FxPage<ImportExcelDetailPageListVO>> queryImportRecord(@RequestBody VppElectricQuantityDTO.QueryDTO param);

    /**
     * 日前申报数据导入
     *
     * @param vppElectricQuantityDTO 导入参数
     * @return 导入结果
     */
    @PostMapping("vppElectricQuantity/importRecord")
    DataResult<ImportExcelVO> importRecord(@RequestBody VppElectricQuantityDTO vppElectricQuantityDTO);


    /**
     * 获取更新时间
     *
     * @param param 查询参数
     * @return 更新时间
     */
    @PostMapping("vppElectricActual/getUpdateTime")
    DataResult<String> getUpdateTime(@RequestBody GetUpdateTimeDTO param);

    /**
     * 删除日前申报数据
     *
     * @param deleteRecordDTO 删除参数
     * @return 删除结果
     */
    @PostMapping("vppElectricQuantity/deleteRecordData")
    DataResult<Void> deleteRecordData(@RequestBody DeleteRecordDTO deleteRecordDTO);

    /**
     * 下载日前申报数据
     *
     * @param param 查询参数
     * @return 下载结果
     */
    @PostMapping("vppElectricQuantity/downloadRecord")
    DataResult<List<SeElectricDeclareVO>> downloadRecord(@RequestBody CommonDTO.DateDTO param);
}
