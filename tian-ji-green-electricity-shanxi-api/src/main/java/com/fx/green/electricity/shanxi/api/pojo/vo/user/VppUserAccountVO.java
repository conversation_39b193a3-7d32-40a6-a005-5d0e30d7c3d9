package com.fx.green.electricity.shanxi.api.pojo.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 基础信息DTO
 *
 * <AUTHOR>
 */
@Data
public class VppUserAccountVO implements Serializable {

    private static final long serialVersionUID = 8445529111099945039L;
    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("负荷用户户号")
    private String consNo;

    @ApiModelProperty("户号名称")
    private String consName;

    @ApiModelProperty("额定功率")
    private BigDecimal ratedPower;

    @ApiModelProperty("电压等级(1.220Kv 2.110Kv 3.35Kv 4.10Kv 5.6Kv 6.380V 7.220V)")
    private Integer volLevel;

    @ApiModelProperty("聚合容量")
    private BigDecimal ratedCapacity;


    @ApiModelProperty("位置code(到区)")
    private String areaCode;

    @ApiModelProperty("所在区县")
    private String districtAddr;

    @ApiModelProperty("位置code(详细信息)")
    private String areaCodeDetail;

    @ApiModelProperty("详细地址")
    private String deptAddress;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String lat;

    @ApiModelProperty("并网馈线")
    private String conFeeder;

    @ApiModelProperty("并网变电站")
    private String conTransSub;

    @ApiModelProperty("供电单位编码")
    private String powerSupplyOrgNo;

    @ApiModelProperty("供电电压编码")
    private String voltType;

    @ApiModelProperty("行业类型编码")
    private String tradeType;

    @ApiModelProperty("包含资源类型编码")
    private String resourceType;

    @ApiModelProperty("响应时间级别")
    private String responseLevel;

    @ApiModelProperty("用电地址")
    private String elecAddr;


}
