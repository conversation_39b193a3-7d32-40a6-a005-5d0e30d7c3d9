package com.fx.green.electricity.shanxi.api.utils;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.apache.tika.metadata.Metadata;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 基于 Apache Tika 的文件类型检测工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class FileTypeUtil {

    private static final Tika tika = new Tika();

    /**
     * MIME 类型到文件扩展名的映射
     */
    private static final Map<String, String> MIME_TO_EXTENSION = new HashMap<>();

    static {
        // 文档类型
        MIME_TO_EXTENSION.put("application/pdf", "pdf");
        MIME_TO_EXTENSION.put("application/msword", "doc");
        MIME_TO_EXTENSION.put("application/vnd.openxmlformats-officedocument.wordprocessingml.document", "docx");
        MIME_TO_EXTENSION.put("application/vnd.ms-excel", "xls");
        MIME_TO_EXTENSION.put("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "xlsx");
        MIME_TO_EXTENSION.put("text/csv", "csv");
        MIME_TO_EXTENSION.put("text/plain", "txt");
        MIME_TO_EXTENSION.put("text/markdown", "md");

        // 图片类型
        MIME_TO_EXTENSION.put("image/jpeg", "jpg");
        MIME_TO_EXTENSION.put("image/png", "png");
        MIME_TO_EXTENSION.put("image/gif", "gif");
        MIME_TO_EXTENSION.put("image/bmp", "bmp");
        MIME_TO_EXTENSION.put("image/webp", "webp");
    }

    /**
     * 检测文件的真实类型
     *
     * @param file 待检测的文件
     * @return 文件类型扩展名，如果无法检测则返回 null
     */
    public static String detectFileType(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return null;
        }

        try (InputStream inputStream = file.getInputStream()) {
            return detectFileType(inputStream, file.getOriginalFilename());
        } catch (IOException e) {
            log.error("检测文件类型时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检测文件的真实类型
     *
     * @param inputStream 文件输入流
     * @param fileName    文件名（可选，用于辅助检测）
     * @return 文件类型扩展名，如果无法检测则返回 null
     */
    public static String detectFileType(InputStream inputStream, String fileName) {
        try {
            // 使用 Tika 检测 MIME 类型
            Metadata metadata = new Metadata();
            if (StrUtil.isNotBlank(fileName)) {
                metadata.set("resourceName", fileName);
            }

            String mimeType = tika.detect(inputStream, metadata);
            log.debug("检测到的 MIME 类型: {}, 文件名: {}", mimeType, fileName);

            // 将 MIME 类型转换为文件扩展名
            String extension = MIME_TO_EXTENSION.get(mimeType);

            // 如果无法通过 MIME 类型映射，尝试从文件名获取扩展名进行验证
            if (extension == null && StrUtil.isNotBlank(fileName)) {
                String fileExtension = getFileExtension(fileName);
                if (isValidExtensionForMimeType(mimeType, fileExtension)) {
                    extension = fileExtension;
                }
            }

            return extension;
        } catch (IOException e) {
            log.error("使用 Tika 检测文件类型时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证文件类型是否匹配
     *
     * @param file              待验证的文件
     * @param expectedExtension 期望的文件扩展名
     * @return true: 文件类型匹配, false: 文件类型不匹配
     */
    public static boolean validateFileType(MultipartFile file, String expectedExtension) {
        String detectedType = detectFileType(file);
        return StrUtil.isNotBlank(detectedType) && detectedType.equalsIgnoreCase(expectedExtension);
    }

    /**
     * 检查文件是否为 Excel 文件（基于文件内容检测）
     *
     * @param file 待检查的文件
     * @return true: 是 Excel 文件, false: 不是 Excel 文件
     */
    public static boolean isExcelFile(MultipartFile file) {
        String detectedType = detectFileType(file);
        return "xls".equals(detectedType) || "xlsx".equals(detectedType);
    }

    /**
     * 校验excel文件类型是否正确（基于文件名）
     *
     * @param fileName 文件名
     * @return true: 文件名符合 Excel 格式, false: 不符合
     */
    public static boolean checkExcelFile(String fileName) {
        if (CharSequenceUtil.isBlank(fileName)) {
            return false;
        }
        return fileName.toLowerCase().endsWith("xls") || fileName.toLowerCase().endsWith("xlsx");
    }

    /**
     * 校验excel文件类型是否正确（基于文件内容和文件名）
     * 使用 Apache Tika 进行更准确的文件类型检测
     *
     * @param file 待检测的文件
     * @return true: 是有效的 Excel 文件, false: 不是 Excel 文件
     */
    public static boolean checkExcelFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        // 首先检查文件名
        String fileName = file.getOriginalFilename();
        if (!checkExcelFile(fileName)) {
            return false;
        }

        // 然后使用 Tika 检测文件内容
        try (InputStream inputStream = file.getInputStream()) {
            Metadata metadata = new Metadata();
            if (CharSequenceUtil.isNotBlank(fileName)) {
                metadata.set("resourceName", fileName);
            }

            String mimeType = tika.detect(inputStream, metadata);
            log.debug("检测到的 MIME 类型: {}, 文件名: {}", mimeType, fileName);

            // 检查是否为 Excel 文件的 MIME 类型
            return isExcelMimeType(mimeType);
        } catch (IOException e) {
            log.error("检测 Excel 文件类型时发生异常: {}", e.getMessage(), e);
            // 如果检测失败，回退到文件名检查
            return checkExcelFile(fileName);
        }
    }

    /**
     * 判断 MIME 类型是否为 Excel 文件
     *
     * @param mimeType MIME 类型
     * @return true: 是 Excel 文件的 MIME 类型, false: 不是
     */
    private static boolean isExcelMimeType(String mimeType) {
        if (CharSequenceUtil.isBlank(mimeType)) {
            return false;
        }

        return "application/vnd.ms-excel".equals(mimeType) ||
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(mimeType) ||
                // xlsx 文件可能被检测为 zip，这是正常的
                ("application/zip".equals(mimeType));
    }

    /**
     * 检查文件是否为图片文件
     *
     * @param file 待检查的文件
     * @return true: 是图片文件, false: 不是图片文件
     */
    public static boolean isImageFile(MultipartFile file) {
        String detectedType = detectFileType(file);
        return "jpg".equals(detectedType) || "jpeg".equals(detectedType) ||
                "png".equals(detectedType) || "gif".equals(detectedType) ||
                "bmp".equals(detectedType) || "webp".equals(detectedType);
    }

    /**
     * 从文件名中提取扩展名
     *
     * @param fileName 文件名
     * @return 文件扩展名（小写），如果没有扩展名则返回 null
     */
    private static String getFileExtension(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return null;
        }

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }

        return null;
    }

    /**
     * 验证文件扩展名是否与 MIME 类型兼容
     *
     * @param mimeType  MIME 类型
     * @param extension 文件扩展名
     * @return true: 兼容, false: 不兼容
     */
    private static boolean isValidExtensionForMimeType(String mimeType, String extension) {
        if (StrUtil.isBlank(mimeType) || StrUtil.isBlank(extension)) {
            return false;
        }

        // 特殊处理一些常见的情况
        switch (mimeType) {
            case "application/zip":
                // ZIP 文件可能是 xlsx, docx 等 Office 文档
                return "xlsx".equals(extension) || "docx".equals(extension);
            case "text/plain":
                // 纯文本可能是 txt, md, csv 等
                return "txt".equals(extension) || "md".equals(extension) || "csv".equals(extension);
            case "application/octet-stream":
                // 二进制流，可能是各种类型
                return true;
            default:
                return false;
        }
    }
}
