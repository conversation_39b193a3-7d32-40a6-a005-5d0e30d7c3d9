package com.fx.green.electricity.shanxi.api.pojo.vo.electric;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("用户调节能力详情VO")
public class VppBiddingClearVO implements Serializable {

    private static final long serialVersionUID = 4685465953025808843L;
    @ApiModelProperty("日前出清价格")
    private Map<String, BigDecimal> priceMap;

    @ApiModelProperty("日前中标")
    private Map<String, BigDecimal> map;

    @ApiModelProperty("日前中标负荷")
    private List<clearMapVO> clearList;

    @ApiModelProperty("平均负荷")
    private List<AverageLoad> averageLoadList;

    @ApiModelProperty("调节容量")
    private List<AdjustDeclare> adjustDeclareList;

    @ApiModelProperty("中长期数据")
    private List<MediumDataVO> mediumDataList;


    @ApiModelProperty("偏差值")
    private List<Deviation> deviation;


    @Data
    public static class AverageLoad implements Serializable {
        private static final long serialVersionUID = -5641358036477278560L;
        @ApiModelProperty("时段")
        private String period;
        @ApiModelProperty("调控时段")
        private String basePeriod;
        @ApiModelProperty("值")
        private BigDecimal val;
    }

    @Data
    public static class AdjustDeclare implements Serializable {

        private static final long serialVersionUID = 1805925508320000174L;
        @ApiModelProperty("时段")
        private String period;
        @ApiModelProperty("调控时段")
        private String basePeriod;
        @ApiModelProperty("上调能力")
        private BigDecimal upRegulation;
        @ApiModelProperty("下调能力")
        private BigDecimal downRegulation;
        @ApiModelProperty("最大调节容量")
        private BigDecimal maxUpRegulation;
        @ApiModelProperty("最小调节容量")
        private BigDecimal minDownRegulation;
        @ApiModelProperty("用户id")
        private Long userId;
    }

    @Data
    public static class ResolveLoad implements Serializable {
        private static final long serialVersionUID = 4331505568016986337L;
        @ApiModelProperty("时段")
        private String dateDay;
        @ApiModelProperty("时段")
        private String period;
        @ApiModelProperty("调控时段")
        private String basePeriod;
        @ApiModelProperty("值")
        private BigDecimal val;
        @ApiModelProperty("用户id")
        private Long userId;
    }

    @Data
    public static class clearMapVO implements Serializable {
        private static final long serialVersionUID = -1791022575454914052L;

        @ApiModelProperty("时段")
        private String period;

        @ApiModelProperty("值")
        private BigDecimal val;

        @ApiModelProperty("1.正常 2最小负荷")
        private Integer flag;
    }

    @Data
    public static class MediumDataVO implements Serializable {
        private static final long serialVersionUID = -1791022575454914052L;

        @ApiModelProperty("时段")
        private String period;

        @ApiModelProperty("时段")
        private String timeFrame;

        @ApiModelProperty("值")
        private BigDecimal electricity;

        @ApiModelProperty("1.正常 2最小负荷")
        private BigDecimal weightedAvgPrice;
    }


    @Data
    public static class Deviation implements Serializable {

        @ApiModelProperty("时段")
        private String period;

        @ApiModelProperty("时段")
        private String timeFrame;

        @ApiModelProperty("最大偏差")
        private BigDecimal maxDeviation;

        @ApiModelProperty("最小偏差")
        private BigDecimal minDeviation;
    }


}
