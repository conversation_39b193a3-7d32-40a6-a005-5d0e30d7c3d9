package com.fx.green.electricity.shanxi.api.pojo.vo.electric;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("可控负荷分析-饼图VO")
public class PieChartOfUserVO {

    private List<RatioVO> typeRatioList;
    private List<RatioVO> electricRatioList;
    private List<RatioVO> powerList;

    @Data
    @ApiModel("占比VO")
    public static class RatioVO {
        private Long id;
        @ApiModelProperty("名称")
        private String name;
        @ApiModelProperty("占比")
        private BigDecimal ratio;
        @ApiModelProperty("数量or用电量")
        private BigDecimal num;
    }
}
