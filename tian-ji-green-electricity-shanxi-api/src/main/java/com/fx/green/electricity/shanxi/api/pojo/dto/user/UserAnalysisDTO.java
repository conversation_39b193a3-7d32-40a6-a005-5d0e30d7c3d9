package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("查询用户或者户号电量DTO")
public class UserAnalysisDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -1834192911579240712L;

    @ApiModelProperty("统一社会信用代码")
    private String userCode;

    @ApiModelProperty("负荷用户户号")
    private String consNo;

    @ApiModelProperty("数据查询开始时间")
    @NotNull(message = "开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty("数据查询结束时间")
    @NotNull(message = "结束日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
}