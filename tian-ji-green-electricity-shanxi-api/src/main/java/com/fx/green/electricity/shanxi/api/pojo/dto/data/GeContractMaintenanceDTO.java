package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同数据维护
 */
@Data
@ApiModel("合同数据维护")
public class GeContractMaintenanceDTO implements Serializable {
    private static final long serialVersionUID = 941043937853678233L;

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("机组id")
    private Long unitId;
    @ApiModelProperty("合同标的开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date targetStartDate;
    @ApiModelProperty("合同标的结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date targetEndDate;
    @ApiModelProperty("合同价格")
    private BigDecimal contractPrice;
    @ApiModelProperty("创建用户ID")
    private Long createUser;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改用户ID")
    private Long updateUser;
    @ApiModelProperty("修改时间")
    private Date updateTime;
}
