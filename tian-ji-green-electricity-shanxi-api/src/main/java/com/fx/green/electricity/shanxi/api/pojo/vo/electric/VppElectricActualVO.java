package com.fx.green.electricity.shanxi.api.pojo.vo.electric;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("实际电量")
public class VppElectricActualVO implements Serializable {

    private static final long serialVersionUID = -8165506015532354335L;

    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("售电租户ID")
    private Long tenantId;
    @ApiModelProperty("统一社会信用代码")
    private String userCode;
    @ApiModelProperty("电量数据ID")
    private Long electricId;
    @ApiModelProperty("户号")
    private String registered;
    @ApiModelProperty("计量点")
    private String meteringCode;
    @ApiModelProperty("日期")
    private String dateDay;
    @ApiModelProperty("时点")
    private String timeFrame;
    @ApiModelProperty("电量")
    private BigDecimal electricity;
    @ApiModelProperty("负荷用户id")
    private Long userId;
}