package com.fx.green.electricity.shanxi.api.api.user;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserBindCycleDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserBindCycleVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 绑定周期 Api 接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppBindCycleApi")
public interface VppBindCycleApi {

    /**
     * 添加绑定周期
     *
     * @param param 绑定周期DTO
     * @return 成功/失败
     */
    @PostMapping("/vppBindCycle/add")
    DataResult<Void> addVppBindCycle(@RequestBody VppUserBindCycleDTO param);

    /**
     * 查询绑定周期列表
     *
     * @param param 绑定周期id
     * @return 绑定周期列表
     */
    @PostMapping("/vppBindCycle/selectList")
    DataResult<List<VppUserBindCycleVO>> selectList(@RequestBody IdDTO param);

    /**
     * 删除绑定周期
     *
     * @param param 绑定周期id
     * @return 成功/失败
     */
    @PostMapping("/vppBindCycle/delete")
    DataResult<Void> delete(@RequestBody IdDTO param);
}
