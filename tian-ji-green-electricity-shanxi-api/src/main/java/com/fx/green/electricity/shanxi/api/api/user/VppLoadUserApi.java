package com.fx.green.electricity.shanxi.api.api.user;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserInfoVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 负荷用户 Api 接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppLoadUserApi")
public interface VppLoadUserApi {

    /**
     * 修改或保存负荷用户
     *
     * @param param 入参
     * @return 成功/失败
     */
    @PostMapping("/vppLoadUser/saveOrUpdate")
    DataResult<Long> vppLoadUserSaveOrUpdate(@RequestBody VppLoadUserDTO param);

    /**
     * 根据 id 删除
     *
     * @param param 负荷用户ID
     * @return 成功/失败
     */
    @PostMapping("/vppLoadUser/delete")
    DataResult<Void> delete(@RequestBody IdDTO param);

    /**
     * 根据 id 查询负荷用户详情
     *
     * @param param 负荷用户ID
     * @return 负荷用户详情
     */
    @PostMapping("/vppLoadUser/findById")
    DataResult<VppUserInfoVO> findById(@RequestBody IdDTO param);

    /**
     * 分页查询负荷用户
     *
     * @param param 筛选条件
     * @return 负荷用户
     */
    @PostMapping("/vppLoadUser/pageList")
    DataResult<FxPage<VppLoadUserVO>> pageList(@RequestBody VppUserDTO.SearchDTO param);

    /**
     * 查询用户分析
     *
     * @param param 入参
     * @return 用户分析曲线
     */
    @PostMapping("/vppLoadUser/queryUserAnalysis")
    DataResult<VppLoadUserVO.UserAnalysisVO> queryUserAnalysis(@RequestBody VppUserDTO.UserAnalysisDTO param);

    /**
     * 获取用户信息
     *
     * @param vppUserDTO 用户DTO
     * @return 用户信息
     */
    @PostMapping("/vppLoadUser/findUserName")
    DataResult<VppLoadUserVO> findUserName(@RequestBody VppUserDTO vppUserDTO);


    /**
     * 修改openId
     *
     * @param param 用户ID和openId
     * @return 结果
     */
    @PostMapping("/vppLoadUser/updateOpenId")
    DataResult<Void> vppLoadUserUpdateOpenId(@RequestBody VppUserDTO.OpenIdDTO param);

    /**
     * 通过openId获取用户信息
     *
     * @param getAllListDTO 获取所有列表DTO
     * @return 用户列表
     */
    @PostMapping("/vppLoadUser/getAllList")
    DataResult<List<VppLoadUserVO>> getAllList(@RequestBody GetAllListDTO getAllListDTO);

    /**
     * 通过租户id获取用户信息
     *
     * @param getAllListDTO 获取所有列表DTO
     * @return 用户列表
     */
    @PostMapping("/vppLoadUser/getAllUserList")
    DataResult<List<VppLoadUserVO>> getAllUserList(@RequestBody GetAllListDTO getAllListDTO);

    /**
     * 重置密码
     *
     * @param param 用户 DTO
     * @return 成功/失败
     */
    @PostMapping("/vppLoadUser/resetPassword")
    DataResult<Void> resetPassword(VppUserDTO param);


    /**
     * 获取多天的多个用户的绑定周期
     *
     * @param param 查询用户树形结构 DTO
     * @return 用户树形结构列表
     */
    @PostMapping("/vppLoadUser/queryTreeListByDate")
    DataResult<VppLoadUserVO.TreeVO> queryTreeListByDate(QueryDateDTO param);

    /**
     * 查询用户树形结构列表
     *
     * @param param 查询用户树形结构 DTO
     * @return 用户树形结构列表
     */
    @PostMapping("/vppLoadUser/queryTreeList")
    DataResult<VppLoadUserVO.TreeVO> queryTreeList(@RequestBody AdjustDeclareDTO.QueryUserTreeDTO param);

}
