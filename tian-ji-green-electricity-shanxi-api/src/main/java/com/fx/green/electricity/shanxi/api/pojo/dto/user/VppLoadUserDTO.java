package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 基础信息DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VppLoadUserDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 8445529111099945039L;

    @ApiModelProperty("负荷用户id")
    private Long id;

    @NotBlank(message = "负荷用户名称不能为空")
    @ApiModelProperty("负荷用户名称")
    private String name;

    @NotBlank(message = "统一社会信用代码不能为空")
    @ApiModelProperty("统一社会信用代码")
    private String userCode;

    @NotBlank(message = "联系人姓名不能为空")
    @ApiModelProperty("联系人姓名")
    private String contactName;

    @NotBlank(message = "联系人电话不能为空")
    @ApiModelProperty("联系人电话")
    private String contactPhone;

    @ApiModelProperty("微信绑定ID")
    private String openId;

    @ApiModelProperty("红利系数")
    private BigDecimal dividendRatio;

    @NotBlank(message = "位置code(到区)不能为空")
    @ApiModelProperty("位置code(到区)")
    private String areaCode;

    @NotBlank(message = "所在区县不能为空")
    @ApiModelProperty("所在区县")
    private String districtAddr;

    @NotBlank(message = "位置code(详细信息)不能为空")
    @ApiModelProperty("位置code(详细信息)")
    private String areaCodeDetail;

    @NotBlank(message = "详细地址不能为空")
    @ApiModelProperty("详细地址")
    private String deptAddress;

    @NotBlank(message = "经度不能为空")
    @ApiModelProperty("经度")
    private String longitude;

    @NotBlank(message = "纬度不能为空")
    @ApiModelProperty("纬度")
    private String lat;

    @NotBlank(message = "用电类型不能为空")
    @ApiModelProperty("用电类型(1.大工业 2.一般工商业 3.普通工业 4.其他)")
    private Integer electricityType;

    @ApiModelProperty("企业简介")
    private String enterpriseIntroduction;

    @ApiModelProperty("企业照片")
    private String enterprisePhotos;

    @ApiModelProperty("户号信息")
    private List<VppUserAccountDTO> accountList;

}
