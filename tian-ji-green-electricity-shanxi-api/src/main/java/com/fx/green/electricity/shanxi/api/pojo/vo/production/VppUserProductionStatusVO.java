package com.fx.green.electricity.shanxi.api.pojo.vo.production;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 用户生产状态VO
 */
@Data
public class VppUserProductionStatusVO implements Serializable {

    private static final long serialVersionUID = 5091123814975496480L;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("预计生产数据")
    private Map<String, Integer> expectedMap;

    @ApiModelProperty("实际生产数据")
    private Map<String, Integer> actualMap;

}
