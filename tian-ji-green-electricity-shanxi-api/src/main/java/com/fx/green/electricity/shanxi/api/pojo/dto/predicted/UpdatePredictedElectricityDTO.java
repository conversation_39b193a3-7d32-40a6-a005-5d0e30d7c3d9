package com.fx.green.electricity.shanxi.api.pojo.dto.predicted;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("修改编辑预测电量")
public class UpdatePredictedElectricityDTO extends BaseDTO {

    @ApiModelProperty("数据")
    private List<UpdatePredictedElectricityInfo> dataList;

    @Data
    public static class UpdatePredictedElectricityInfo implements Serializable {
        @ApiModelProperty("用戶id")
        private Long userId;

        @ApiModelProperty("名字")
        private String name;

        @ApiModelProperty("预测维度 1.日 2.上旬 3.中旬 4.下旬 5.月")
        private Integer dimension;

        @ApiModelProperty("日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date dateDay;

        @ApiModelProperty("修改预测电量")
        private List<BigDecimal> electricityList;
    }


}
