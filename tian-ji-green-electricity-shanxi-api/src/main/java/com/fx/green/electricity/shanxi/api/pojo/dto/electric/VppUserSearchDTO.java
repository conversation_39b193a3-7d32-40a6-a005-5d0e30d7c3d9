package com.fx.green.electricity.shanxi.api.pojo.dto.electric;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("电力用户")
@ExcelIgnoreUnannotated
public class VppUserSearchDTO extends BaseDTO {

    private static final long serialVersionUID = -2912228744071461838L;

    @ApiModelProperty("时间")
    @JsonFormat(pattern = "yyyy-MM")
    private List<Date> searchTime;
    @ApiModelProperty("搜索条件")
    private String searchValue;
    @ApiModelProperty("绑定月份")
    @ExcelProperty(value = "绑定月份")
    @JsonFormat(pattern = "yyyy-MM")
    private Date dateMonth;
    @ApiModelProperty("用户类型（1. 大工业 2. 一般工商业 3. 普通工业 4.其他）")
    @ExcelProperty(value = "用户类型")
    private Integer type;
    @ApiModelProperty("定价方式（1. 分时约定价格 2. 月度集中竞价+价差 3. 固定价格（低压） 4. 固定价格（战新））")
    private Integer pricingMode;
    @ApiModelProperty("参与交易单元（1. 非新兴 2. 新兴 3. 5G低压）")
    @ExcelProperty(value = "参与交易单元")
    private Integer tradingUnit;
    @ApiModelProperty("月用电规模（1. 10000MWh及以上 2. 5000-10000MWh 3. 1000-5000MWh 4. 1000MW以下 5. 其他）")
    @ExcelProperty(value = "月用电规模")
    private Integer scale;
    @ApiModelProperty("电压等级（1. 220KV及以上 2. 110KV 3. 35KV 4. 10KV 5. 1-10KV）")
    @ExcelProperty(value = "电压等级")
    private Integer voltageClasses;
    @ApiModelProperty("峰谷比（1. 峰时用户 2. 谷时用户）")
    private Integer peakValley;
    @ApiModelProperty("用户分组名称")
    private String groupName;
    @ApiModelProperty("用户分组id")
    private String groupId;
    @ApiModelProperty("用户名称")
    private String name;
    @ApiModelProperty("行政区域名称")
    @Length(max = 50, min = 0, message = "长度必须大于0小于50")
    private String areaName;

}