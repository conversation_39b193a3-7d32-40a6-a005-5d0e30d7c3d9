package com.fx.green.electricity.shanxi.api.pojo.dto.production;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class VppUserProductionStatisticsQueryDTO extends BaseDTO {

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("类型：1.预计生产计划，2.实际生产情况")
    private Integer productionType;

    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "日期不能为空")
    private Date dateDay;

    @ApiModelProperty("1.获取绑定周期用户 2.获取全部用户")
    @NotNull(message = "用户范围不能为空")
    private Integer userType;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("1.已填生产计划，2.未填生产计划，3.已填实际生产，4.未填实际生产")
    private int queryType;

}
