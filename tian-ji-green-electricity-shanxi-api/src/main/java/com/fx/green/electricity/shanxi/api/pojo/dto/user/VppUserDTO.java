package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 基础信息DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VppUserDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 8445529111099945039L;

    @ApiModelProperty("负荷用户id")
    private Long id;

    @NotBlank(message = "负荷用户名称不能为空")
    @ApiModelProperty("负荷用户名称")
    private String name;

    @NotBlank(message = "负荷用户户号不能为空")
    @ApiModelProperty("负荷用户户号")
    private String consNo;

    @NotBlank(message = "统一社会信用代码不能为空")
    @ApiModelProperty("统一社会信用代码")
    private String userCode;

    @NotNull(message = "行业类别不能为空")
    @ApiModelProperty("行业类别(21建筑,22商业,24房地产,25其他,26农业,27电动汽车)")
    private Integer industryCategory;

    @NotNull(message = "负荷用户类型不能为空")
    @ApiModelProperty("负荷用户类型(11水电,12风机,13光伏,14储能,15负荷,16 充电桩用户)")
    private Integer loadType;

    @NotNull(message = "额定功率不能为空")
    @ApiModelProperty("额定功率")
    private BigDecimal ratedPower;

    @NotNull(message = "电压等级不能为空")
    @ApiModelProperty("电压等级(1.220Kv 2.110Kv 3.35Kv 4.10Kv 5.6Kv 6.380V 7.220V)")
    private Integer volLevel;

    @NotNull(message = "聚合容量不能为空")
    @ApiModelProperty("聚合容量")
    private BigDecimal ratedCapacity;

    @NotBlank(message = "位置code不能为空")
    @ApiModelProperty("位置code(到区)")
    private String areaCode;

    @NotBlank(message = "位置code不能为空")
    @ApiModelProperty("位置code(详细信息)")
    private String areaCodeDetail;

    @NotBlank(message = "详细地址不能为空")
    @ApiModelProperty("详细地址")
    private String deptAddress;

    @NotBlank(message = "经度不能为空")
    @ApiModelProperty("经度")
    private String longitude;

    @NotBlank(message = "纬度不能为空")
    @ApiModelProperty("纬度")
    private String lat;

    @NotBlank(message = "并网馈线不能为空")
    @ApiModelProperty("并网馈线")
    private String conFeeder;
    @NotBlank(message = "并网变电站不能为空")
    @ApiModelProperty("并网变电站")
    private String conTransSub;

    @ApiModelProperty("用户绑定周期开始")
    @JsonFormat(pattern = "yyyy-MM")
    private Date bindCycleStart;

    @ApiModelProperty("用户绑定周期结束")
    @JsonFormat(pattern = "yyyy-MM")
    private Date bindCycleEnd;

    @NotBlank(message = "联系人姓名不能为空")
    @ApiModelProperty("联系人姓名")
    private String contactName;

    @NotBlank(message = "联系人电话不能为空")
    @ApiModelProperty("联系人电话")
    private String contactPhone;

    @ApiModelProperty("微信绑定ID")
    private String openId;

    @ApiModelProperty("红利系数")
    private BigDecimal dividendRatio;

    @ApiModelProperty("供电单位编码")
    private String powerSupplyOrgNo;
    @ApiModelProperty("供电电压编码")
    private String voltType;
    @ApiModelProperty("行业类型编码")
    private String tradeType;
    @ApiModelProperty("包含资源类型编码")
    private String resourceType;
    @ApiModelProperty("响应时间级别")
    private String responseLevel;
    @ApiModelProperty("用电地址")
    private String elecAddr;
    @ApiModelProperty("所在区县")
    private String districtAddr;

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("负荷用户列表-分页查询dto")
    public static class SearchDTO extends BaseDTO {
        private static final long serialVersionUID = -3056623365821667422L;

        @ApiModelProperty("负荷用户名称")
        private String name;

        @ApiModelProperty("统一社会信用代码")
        private String userCode;

        @ApiModelProperty("用电类型(1.大工业 2.一般工商业 3.普通工业 4.其他)")
        private Integer electricityType;

        @ApiModelProperty("电压等级(1.220Kv 2.110Kv 3.35Kv 4.10Kv 5.6Kv 6.380V 7.220V)")
        private Integer volLevel;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("查询用户分析DTO")
    public static class UserAnalysisDTO extends BaseDTO {

        private static final long serialVersionUID = -1834192911579240712L;

        @NotBlank(message = "社会信用代码不能为空")
        @ApiModelProperty("租户唯一社会信用代码")
        private String userCode;

        @ApiModelProperty("数据查询开始时间")
        @NotNull(message = "开始日期不能为空")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date startTime;

        @ApiModelProperty("数据查询结束时间")
        @NotNull(message = "结束日期不能为空")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date endTime;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("修改用户openId")
    @AllArgsConstructor
    public static class OpenIdDTO extends BaseDTO {

        private static final long serialVersionUID = -6099105596855874941L;

        @ApiModelProperty("负荷用户id")
        private Long id;

        @ApiModelProperty("微信绑定ID")
        private String openId;
    }
}
