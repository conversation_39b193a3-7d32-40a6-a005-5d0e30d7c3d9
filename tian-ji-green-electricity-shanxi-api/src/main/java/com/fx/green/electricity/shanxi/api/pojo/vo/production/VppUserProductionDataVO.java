package com.fx.green.electricity.shanxi.api.pojo.vo.production;

import com.fx.common.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VppUserProductionDataVO extends BaseVO {

    private static final long serialVersionUID = 5091123814975496480L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("负荷用户id")
    private Long userId;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("是否装表(0“已安装，1：未安装)")
    private Integer isInstall;

    @ApiModelProperty("生产流程")
    private String process;

    @ApiModelProperty("时间安排")
    private String timeSchedule;

    @ApiModelProperty("固定检修计划")
    private String fixedMaintenancePlan;

    @ApiModelProperty("最大运行负荷")
    private BigDecimal maxOperatingLoad;

    @ApiModelProperty("最小运行负荷")
    private BigDecimal minOperatingLoad;

    @ApiModelProperty("调节容量")
    private BigDecimal adjustingCapacity;

    @ApiModelProperty("用户生产数据-沟通人员")
    private List<vppUserProductionDataStaffVO> staffVOList;

    @ApiModelProperty("用户生产数据-主要调节设备/主要生产工艺段")
    private List<VppUserProductionDataAdjustDeviceVO> adjustDeviceVOList;

    @ApiModelProperty("用户生产数据-发电设备VO")
    private List<VppUserProductionDataPowerDeviceVO> powerDeviceVOList;

    @Data
    @ApiModel("用户生产数据-沟通人员VO")
    public static class vppUserProductionDataStaffVO {
        @ApiModelProperty("主键id")
        private Long id;

        @ApiModelProperty("用户生产数据id")
        private Long userProductionDataId;

        @ApiModelProperty("沟通人员姓名")
        private String name;

        @ApiModelProperty("沟通人员联系方式")
        private String contactInformation;

        @ApiModelProperty("职位")
        private String position;
    }

    @Data
    @ApiModel("用户生产数据-主要调节设备/主要生产工艺段VO")
    public static class VppUserProductionDataAdjustDeviceVO {
        @ApiModelProperty("主键id")
        private Long id;

        @ApiModelProperty("用户生产数据id")
        private Long userProductionDataId;

        @ApiModelProperty("设备名称/工艺段名称")
        private String name;

        @ApiModelProperty("额定功率")
        private BigDecimal ratedPower;

        @ApiModelProperty("正常生产功率")
        private BigDecimal normalProductionPower;

        @ApiModelProperty("检修功率")
        private BigDecimal maintenancePower;

        @ApiModelProperty("停产功率")
        private BigDecimal shutdownPower;

        @ApiModelProperty("开机运行时长")
        private BigDecimal startupRuntime;
    }

    @Data
    @ApiModel("用户生产数据-发电设备VO")
    public static class VppUserProductionDataPowerDeviceVO {
        @ApiModelProperty("主键id")
        private Long id;

        @ApiModelProperty("用户生产数据id")
        private Long userProductionDataId;

        @ApiModelProperty("设备名称")
        private String deviceName;

        @ApiModelProperty("额定功率")
        private BigDecimal ratedPower;

        @ApiModelProperty("正常生产功率")
        private BigDecimal normalProductionPower;

        @ApiModelProperty("沟通人员姓名")
        private String staffName;

        @ApiModelProperty("沟通人员联系方式")
        private String contactInformation;
    }

}
