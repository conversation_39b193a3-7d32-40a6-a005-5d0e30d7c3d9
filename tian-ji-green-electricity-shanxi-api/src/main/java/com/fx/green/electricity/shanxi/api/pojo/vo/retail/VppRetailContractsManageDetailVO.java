package com.fx.green.electricity.shanxi.api.pojo.vo.retail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("零售合同管理表")
public class VppRetailContractsManageDetailVO implements Serializable {

    private static final long serialVersionUID = 1544345293083877189L;
    @ApiModelProperty("红利分享系数")
    private BigDecimal dividendSharCoefficient;
    @ApiModelProperty("预估结算电价（元/MWh）")
    private BigDecimal price;

    @ApiModelProperty("时点")
    private List<String> timeFrame;

    @ApiModelProperty("详细时点")
    private List<String> intervalTimeFrame;

    @ApiModelProperty("电量")
    private List<BigDecimal> powerList;
    @ApiModelProperty("价格")
    private List<BigDecimal> priceList;

}