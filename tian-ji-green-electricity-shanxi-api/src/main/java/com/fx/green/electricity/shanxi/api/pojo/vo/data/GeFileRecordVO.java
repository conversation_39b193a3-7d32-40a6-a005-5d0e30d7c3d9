package com.fx.green.electricity.shanxi.api.pojo.vo.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("文件导入记录列表")
public class GeFileRecordVO extends BaseVO {
    private static final long serialVersionUID = 2856550011239403138L;

    @ApiModelProperty("数据日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dataDate;

    @ApiModelProperty("机组id")
    private Long unitId;
    @ApiModelProperty("机组名称")
    private String unitName;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("租户名称")
    private String tenantName;
    @ApiModelProperty("数据状态")
    private Boolean status;
}
