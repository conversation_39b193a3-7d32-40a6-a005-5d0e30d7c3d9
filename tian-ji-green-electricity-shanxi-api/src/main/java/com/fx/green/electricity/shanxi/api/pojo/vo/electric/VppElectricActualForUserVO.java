package com.fx.green.electricity.shanxi.api.pojo.vo.electric;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class VppElectricActualForUserVO {
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("售电租户ID")
    private Long tenantId;
    @ApiModelProperty("统一社会信用代码")
    private String userCode;
    @ApiModelProperty("日期")
    private Date dateDay;
    @ApiModelProperty("时点")
    private String timeFrame;
    @ApiModelProperty("电量")
    private BigDecimal electricity;

}
