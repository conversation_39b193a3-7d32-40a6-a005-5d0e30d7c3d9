package com.fx.green.electricity.shanxi.api.pojo.dto.production;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@Data
public class VppUserProductionStatusQueryDTO implements Serializable {

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty("租户id")
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不能为空")
    private Long userId;
}
