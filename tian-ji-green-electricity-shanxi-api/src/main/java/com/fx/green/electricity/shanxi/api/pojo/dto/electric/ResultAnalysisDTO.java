package com.fx.green.electricity.shanxi.api.pojo.dto.electric;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("调控结果分析DTO")
@EqualsAndHashCode(callSuper = true)
public class ResultAnalysisDTO extends BaseDTO {

    @NotNull(message = "请选择查询时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("开始时间")
    private Date startDate;

    @NotNull(message = "请选择查询时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("结束时间")
    private Date endDate;

    @ApiModelProperty("负荷用户id")
    private Long userId;

    @ApiModelProperty("时段")
    private String timeFrame;


}
