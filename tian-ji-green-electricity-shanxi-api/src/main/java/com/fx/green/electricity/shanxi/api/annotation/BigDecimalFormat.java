package com.fx.green.electricity.shanxi.api.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.math.RoundingMode;

/**
 * BigDecimal 位数控制注解
 * - scale: 小数位数
 * - autoZeroPadding: 是否自动补零
 * - roundingMode: 舍入模式
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface BigDecimalFormat {
    int scale() default 2; // 默认保留 2 位小数

    boolean autoZeroPadding() default true;

    RoundingMode roundingMode() default RoundingMode.HALF_UP; // 默认四舍五入
}