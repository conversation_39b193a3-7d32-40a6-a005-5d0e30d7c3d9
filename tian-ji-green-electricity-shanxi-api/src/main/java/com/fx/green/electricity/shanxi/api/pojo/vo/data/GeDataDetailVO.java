package com.fx.green.electricity.shanxi.api.pojo.vo.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.green.electricity.shanxi.api.annotation.BigDecimalFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 数据展示VO
 */
@Data
@ApiModel("数据展示VO")
public class GeDataDetailVO implements Serializable {
    private static final long serialVersionUID = -3026007513036841116L;

    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date infoDate;
    @ApiModelProperty("日总量")
    @BigDecimalFormat(scale = 3)
    private BigDecimal dayValue;
    @ApiModelProperty("时点")
    private String timePoint;
    @ApiModelProperty("时点值")
    @BigDecimalFormat(scale = 3)
    private BigDecimal value;
}
