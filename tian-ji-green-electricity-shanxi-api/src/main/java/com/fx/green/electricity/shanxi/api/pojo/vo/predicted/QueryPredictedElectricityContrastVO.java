package com.fx.green.electricity.shanxi.api.pojo.vo.predicted;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 预测电量对比VO
 * <AUTHOR>
 **/
@Data
@ApiModel("预测电量对比VO")
public class QueryPredictedElectricityContrastVO {

    @ApiModelProperty("时段")
    private List<String> timeFrame;

    @ApiModelProperty("数据")
    private List<PredictedElectricityInfo> dataList;

    @Data
    public static class PredictedElectricityInfo {


        @ApiModelProperty("用戶id")
        private Long userId;

        @ApiModelProperty("名字")
        private String name;

        @ApiModelProperty("1.虚拟电厂 2.用户")
        private Long type;

        @ApiModelProperty("虚拟电厂预测")
        private List<BigDecimal> predictedElectricityList;

        @ApiModelProperty("修改预测电量")
        private List<BigDecimal> electricityList;

        @ApiModelProperty("实际用电量")
        private List<BigDecimal> realElectricityList;


        @ApiModelProperty("虚拟电厂预测和实际用电量比例")
        private List<BigDecimal> predictedRatioList;


        @ApiModelProperty("修改预测电量和实际用电量比例")
        private List<BigDecimal> electricityRatioList;

    }

}
