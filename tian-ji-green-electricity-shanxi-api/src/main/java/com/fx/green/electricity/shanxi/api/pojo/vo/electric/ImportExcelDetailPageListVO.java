package com.fx.green.electricity.shanxi.api.pojo.vo.electric;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("导入文件结果详情")
public class ImportExcelDetailPageListVO implements Serializable {

    private static final long serialVersionUID = -2083384728435563995L;

    @ApiModelProperty("数据名称")
    private String dataName;

    @ApiModelProperty("文件名称")
    private String filename;

    @ApiModelProperty("状态：0 失败 1 成功")
    private Integer status;

    @ApiModelProperty("状态：1 实际用电量 2 日前申报")
    private Integer type;

    @ApiModelProperty("文件地址")
    private String url;


    @ApiModelProperty("运行日期")
    private String runningDate;


    @ApiModelProperty("备注")
    private String notes;

    @ApiModelProperty("上传日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date importDate;

}
