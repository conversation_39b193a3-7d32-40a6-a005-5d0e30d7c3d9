package com.fx.green.electricity.shanxi.api.pojo.dto.unit;

import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 绿电租户DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("绿电租户DTO")
public class GeTenantDTO extends BaseDTO {
    private static final long serialVersionUID = -4047719807394980773L;

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("租户名称")
    private String tenantName;
    @ApiModelProperty("联系人")
    private String leader;
    @ApiModelProperty("联系方式")
    private String phone;
    @ApiModelProperty("区域编码")
    private String areaCode;
    @ApiModelProperty("地区名称")
    private String areaName;
    @ApiModelProperty("创建用户ID")
    private Long createUser;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改用户ID")
    private Long updateUser;
    @ApiModelProperty("修改时间")
    private Date updateTime;

}


