package com.fx.green.electricity.shanxi.api.pojo.vo.production;

import com.fx.common.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VppUserProductionStatisticsVO extends BaseVO {

    private static final long serialVersionUID = 5091123814975496480L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("负荷用户id")
    private Long userId;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("日期")
    private Date dateDay;

    @ApiModelProperty("类型：1.预计生产计划，2.实际生产情况")
    private Integer type;

    @ApiModelProperty("负荷用户名称")
    private String userName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("生产状态：1.正常生产，2.检修，3.增产，4.减产，5.停产")
    @NotNull(message = "生产状态不能为空")
    private Integer productionStatus;

    @ApiModelProperty("生产状态详情：【月检】，【季检】，【年检】，【10%-100%】，【10%-100%】")
    private String productionStatusDetail;

    @ApiModelProperty("生产计划")
    private List<ProductionStatisticsPlanVO> productionList;

    @ApiModelProperty("调节设备运行计划")
    private List<ProductionStatisticsPlanVO> adjustPlanList;

    @ApiModelProperty("发电设备运行计划")
    private List<ProductionStatisticsPlanVO> powerPlanList;

    @Data
    @ApiModel("用户生产统计-生产计划表VO")
    public static class ProductionStatisticsPlanVO {

        @ApiModelProperty("主键id")
        private Long id;

        @ApiModelProperty("用户生产统计主键id")
        private Long userProductionStatisticsId;

        @ApiModelProperty("计划类型：1.生产计划，2.调节设备运行计划，3.发电设备运行计划")
        private Integer planType;

        @ApiModelProperty("开始时间")
        private Date startTime;

        @ApiModelProperty("结束时间")
        private Date endTime;

        @ApiModelProperty("产品名称")
        private String name;

        @ApiModelProperty("规格")
        private String specifications;

        @ApiModelProperty("产量（吨）")
        private BigDecimal production;

        @ApiModelProperty("设备id")
        private Long deviceId;

        @ApiModelProperty("连续运行时长")
        private BigDecimal continuousOperationDuration;

        @ApiModelProperty("运行功率/发电功率")
        private BigDecimal operatingPower;

    }


}
