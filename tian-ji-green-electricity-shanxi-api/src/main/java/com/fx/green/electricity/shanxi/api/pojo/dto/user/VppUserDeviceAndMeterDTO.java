package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户设备及电表 DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("用户设备及电表管理")
public class VppUserDeviceAndMeterDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("是否记录设备（0.否 1.是）")
    private Integer isRecordDevice;

    @ApiModelProperty("设备列表")
    private List<VppUserDeviceDTO> deviceList;

    @Data
    @ApiModel("用户设备管理")
    public static class VppUserDeviceDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("设备id")
        private Long id;

        @ApiModelProperty("负荷用户id")
        private Long userId;

        @ApiModelProperty("户号id")
        private Long accountId;

        @ApiModelProperty("用户户号")
        private String consNo;

        @ApiModelProperty("设备名称")
        private String deviceName;

        @ApiModelProperty("电压等级(详见VoltageLevelEnum枚举: 1.220Kv 2.110Kv 3.35Kv 4.10Kv 5.6Kv 6.380V 7.220V)")
        private Integer volLevel;

        @ApiModelProperty("设备地区编号")
        private String deviceAreaCode;

        @ApiModelProperty("设备详细地址")
        private String deviceAddressDetail;

        @ApiModelProperty("经度")
        private BigDecimal longitude;

        @ApiModelProperty("纬度")
        private BigDecimal latitude;

        @ApiModelProperty("额定电压（kV）")
        private BigDecimal ratedVoltage;

        @ApiModelProperty("额变电容量/变压器报装容量（kVA）")
        private BigDecimal ratedCapacity;

        @ApiModelProperty("最大可调功率（kW）")
        private BigDecimal maxAdjustPower;

        @ApiModelProperty("最大上调功率（kW）")
        private BigDecimal maxUpPower;

        @ApiModelProperty("最大下调功率（kW）")
        private BigDecimal maxDownPower;

        @ApiModelProperty("租户id")
        private Long tenantId;

        @ApiModelProperty("电表列表")
        private List<VppUserMeterDTO> meterList;

        @Data
        @ApiModel("用户电表管理")
        public static class VppUserMeterDTO implements Serializable {
            private static final long serialVersionUID = 1L;

            @ApiModelProperty("设备id")
            private Long id;

            @ApiModelProperty("负荷用户id")
            private Long userId;

            @ApiModelProperty("户号id")
            private Long accountId;

            @ApiModelProperty("设备id")
            private Long deviceId;

            @ApiModelProperty("用户户号")
            private String consNo;

            @ApiModelProperty("计量点")
            private String meterPoint;

            @ApiModelProperty("电表名称")
            private String meterName;

            @ApiModelProperty("电表表号")
            private String meterNo;

            @ApiModelProperty("租户id")
            private Long tenantId;

        }

    }
}