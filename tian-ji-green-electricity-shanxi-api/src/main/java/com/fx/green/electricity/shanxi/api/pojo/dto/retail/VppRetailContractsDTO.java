package com.fx.green.electricity.shanxi.api.pojo.dto.retail;

import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
*
**/
@Data
@ApiModel("零售合同表")
public class VppRetailContractsDTO extends BaseDTO {

    private static final long serialVersionUID = -5072049082851048405L;
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("运行月份")
    private Date runMonth;
    @ApiModelProperty("数据项")
    private String dataItem;
    @ApiModelProperty("0未导入 1导入")
    private Integer status;
    @ApiModelProperty("导入时间")
    private Date importDate;
    @ApiModelProperty("附件")
    private String annex;


}