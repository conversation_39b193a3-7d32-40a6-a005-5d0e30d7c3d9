package com.fx.green.electricity.shanxi.api.pojo.vo.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 负荷用户 VO
 * 
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VppLoadUserVO extends BaseVO {

    private static final long serialVersionUID = 5091123814975496480L;

    @ApiModelProperty("负荷用户id")
    private Long id;

    @ApiModelProperty("负荷用户名称")
    private String name;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("统一社会信用代码")
    private String userCode;

    @ApiModelProperty("负荷用户户号")
    private List<String> consNo;

    @ApiModelProperty("额定功率")
    private List<BigDecimal> ratedPower;

    @ApiModelProperty("电压等级(1.220Kv 2.110Kv 3.35Kv 4.10Kv 5.6Kv 6.380V 7.220V)")
    private List<String> volLevel;

    @ApiModelProperty("聚合容量")
    private List<BigDecimal> ratedCapacity;

    @ApiModelProperty("并网馈线")
    private List<String> conFeeder;

    @ApiModelProperty("并网变电站")
    private List<String> conTransSub;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @JsonFormat(pattern = "yyyy-MM")
    @ApiModelProperty("用户绑定周期开始")
    private Date bindCycleStart;

    @JsonFormat(pattern = "yyyy-MM")
    @ApiModelProperty("用户绑定周期结束")
    private Date bindCycleEnd;

    @ApiModelProperty("绑定状态(详见BindStatusEnum枚举: 1-绑定中, 2-未绑定)")
    private Integer bindStatus;

    @ApiModelProperty("联系人姓名")
    private String contactName;

    @ApiModelProperty("联系人电话")
    private String contactPhone;

    @ApiModelProperty("微信绑定ID")
    private String openId;

    @ApiModelProperty("红利系数")
    private BigDecimal dividendRatio;

    @ApiModelProperty("用电类型(1.大工业 2.一般工商业 3.普通工业 4.其他)")
    private Integer electricityType;

    @ApiModelProperty("企业简介")
    private String enterpriseIntroduction;

    @ApiModelProperty("企业照片")
    private String enterprisePhotos;

    @ApiModelProperty("户号信息")
    private List<VppUserAccountVO> accountList;

    @Data
    @ApiModel("用户分析VO")
    public static class UserAnalysisVO {

        @ApiModelProperty("时间列表")
        private List<String> dateList;

        @ApiModelProperty("电量列表")
        private List<String> electricityList;

        @ApiModelProperty("A相电流列表")
        private List<String> iaList;

        @ApiModelProperty("B相电流列表")
        private List<String> ibList;

        @ApiModelProperty("C相电流列表")
        private List<String> icList;

        @ApiModelProperty("A相电压列表")
        private List<String> uaList;

        @ApiModelProperty("B相电压列表")
        private List<String> ubList;

        @ApiModelProperty("C相电压列表")
        private List<String> ucList;

        @ApiModelProperty("总有功功率列表")
        private List<String> activePowerMyDeviceList;

        @ApiModelProperty("总无功功率列表")
        private List<String> reactivePowerList;

        @ApiModelProperty("实际用电量列表")
        private List<String> realElectricityList;

        @ApiModelProperty("用采数据")
        private List<String> gatherList;

        @ApiModelProperty("预测用电量总和")
        private BigDecimal electricityTotal;

        @ApiModelProperty("实际用电量总和")
        private BigDecimal realElectricityTotal;

        public void init() {
            this.dateList = new ArrayList<>();
            this.electricityList = new ArrayList<>();
            this.iaList = new ArrayList<>();
            this.ibList = new ArrayList<>();
            this.icList = new ArrayList<>();
            this.uaList = new ArrayList<>();
            this.ubList = new ArrayList<>();
            this.ucList = new ArrayList<>();
            this.activePowerMyDeviceList = new ArrayList<>();
            this.reactivePowerList = new ArrayList<>();
            this.realElectricityList = new ArrayList<>();
            this.gatherList = new ArrayList<>();
        }
    }

    @Data
    @Accessors(chain = true)
    @ApiModel("用户树形结构VO")
    public static class TreeVO {

        @ApiModelProperty("虚拟电厂名称")
        private String name;

        @ApiModelProperty("类型(1-虚拟电厂,2-负荷用户)")
        private Integer type;

        @ApiModelProperty("负荷用户列表")
        private List<TreeUserVO> treeUserVOList;
    }

    @Data
    @ApiModel("用户树形结构VO")
    public static class TreeUserVO {

        @ApiModelProperty("负荷用户id")
        private Long id;

        @ApiModelProperty("负荷用户名称")
        private String name;

        @ApiModelProperty("类型(1-虚拟电厂,2-负荷用户)")
        private Integer type;

        @ApiModelProperty("用户户号")
        private String consNo;

        @ApiModelProperty("社会统一信用代码")
        private String userCode;

        @ApiModelProperty("是否编辑修改预测电量(0. 未修改；1.已修改)")
        private Integer isUpdate;
    }

    @Data
    @ApiModel("数据采集VO")
    public static class UserAnalysisGatherVO {

        @ApiModelProperty("点位字段")
        private String timeFrame;

        @ApiModelProperty("日期字符串")
        private String dateStr;

        @ApiModelProperty("数据(mw)")
        private String electricity;

        @ApiModelProperty("A相电流")
        private String ia;

        @ApiModelProperty("B相电流")
        private String ib;

        @ApiModelProperty("C相电流")
        private String ic;

        @ApiModelProperty("A相电压")
        private String ua;

        @ApiModelProperty("B相电压")
        private String ub;

        @ApiModelProperty("C相电压")
        private String uc;

        @ApiModelProperty("总有功功率")
        private String activePowerMyDevice;

        @ApiModelProperty("总无功功率")
        private String reactivePower;
    }
}
