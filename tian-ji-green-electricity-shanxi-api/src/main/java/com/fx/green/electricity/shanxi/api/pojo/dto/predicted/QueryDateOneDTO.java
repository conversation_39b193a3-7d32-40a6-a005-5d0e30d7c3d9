package com.fx.green.electricity.shanxi.api.pojo.dto.predicted;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("传单天查询数据")
public class QueryDateOneDTO extends BaseDTO {

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("查询时间")
    @NotNull(message = "开始时间不能为空")
    private Date queryDate;


    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不可为空")
    private Long userId;


    @ApiModelProperty("预测维度 1.日 2.上旬 3.中旬 4.下旬 5.月")
    @NotNull(message = "预测维度不可为空")
    private Integer dimension;
}
