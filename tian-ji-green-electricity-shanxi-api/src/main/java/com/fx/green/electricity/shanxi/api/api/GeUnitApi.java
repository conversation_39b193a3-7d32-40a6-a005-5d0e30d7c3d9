package com.fx.green.electricity.shanxi.api.api;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeDeviceSaveDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeTenantDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeUnitBasicDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeTenantVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeUnitBasicVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 机组相关维护api
 *
 * <AUTHOR>
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "geUnitApi")
public interface GeUnitApi {

    /**
     * 新增绿电租户
     *
     * @param param
     */
    @PostMapping("geTenant/insertTenant")
    DataResult<Void> insertTenant(@RequestBody GeTenantDTO param);

    /**
     * 删除绿电租户
     *
     * @param param
     */
    @PostMapping("geTenant/deleteTenant")
    DataResult<Void> deleteTenant(@RequestBody IdDTO param);

    /**
     * 绿电租户分页
     *
     * @param param
     * @return
     */
    @PostMapping("geTenant/tenantList")
    DataResult<List<GeTenantVO>> tenantList(@RequestBody GeTenantDTO param);

    /**
     * 获取所有租户和机组对应信息
     *
     * @return
     */
    @PostMapping("geTenant/getTenantUnitDetail")
    DataResult<List<GeTenantVO>> getTenantUnitDetail();

    /**
     * 新增机组信息
     *
     * @param param
     */
    @PostMapping("geUnitBasic/insertUnitBase")
    DataResult<Void> insertUnitBase(@RequestBody GeUnitBasicDTO param);

    /**
     * 删除机组信息
     *
     * @param param
     */
    @PostMapping("geUnitBasic/deleteUnitBase")
    DataResult<Void> deleteUnitBase(@RequestBody IdDTO param);

    /**
     * 根据租户id查询对应机组
     *
     * @param param
     * @return
     */
    @PostMapping("geUnitBasic/unitBaseList")
    DataResult<List<GeUnitBasicVO>> unitBaseList(@RequestBody IdDTO param);

    /**
     * 新增设备信息
     *
     * @param param
     */
    @PostMapping("geDevice/insertDevice")
    DataResult<Void> insertDevice(@RequestBody GeDeviceSaveDTO param);

    /**
     * 删除设备
     *
     * @param param
     */
    @PostMapping("geDevice/deleteDevice")
    DataResult<Void> deleteDevice(@RequestBody IdDTO param);
}
