package com.fx.green.electricity.shanxi.api.pojo.vo.electric;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 用户实际用电量
 *
 * <AUTHOR>
 **/
@Data
@ApiModel("用户实际用电量VO")
public class FaceElectricityVO {

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("实际用电量")
    private Map<String, BigDecimal> faceElectricityNumber;

    @ApiModelProperty("价格")
    private Map<String, BigDecimal> priceMap;


}
