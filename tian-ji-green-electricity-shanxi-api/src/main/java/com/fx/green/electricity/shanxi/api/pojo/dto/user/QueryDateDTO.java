package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 查询用户树形结构DTO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel("查询用户树形结构 DTO")
public class QueryDateDTO implements Serializable {

    private static final long serialVersionUID = -245253838213709407L;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    @ApiModelProperty("是否是绑定用户")
    private Boolean isBindUser;

    @ApiModelProperty("租户id")
    private Long tenantId;
}
