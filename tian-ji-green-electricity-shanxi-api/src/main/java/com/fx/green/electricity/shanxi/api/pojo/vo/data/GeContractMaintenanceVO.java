package com.fx.green.electricity.shanxi.api.pojo.vo.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.vo.BaseVO;
import com.fx.green.electricity.shanxi.api.annotation.BigDecimalFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("合同数据维护")
public class GeContractMaintenanceVO extends BaseVO {

    private static final long serialVersionUID = 3687152309447536189L;

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("租户名称")
    private String tenantName;
    @ApiModelProperty("机组id")
    private Long unitId;
    @ApiModelProperty("机组名称")
    private String unitName;
    @ApiModelProperty("合同标的开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date targetStartDate;
    @ApiModelProperty("合同标的结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date targetEndDate;
    @ApiModelProperty("合同价格")
    @BigDecimalFormat
    private BigDecimal contractPrice;
}
