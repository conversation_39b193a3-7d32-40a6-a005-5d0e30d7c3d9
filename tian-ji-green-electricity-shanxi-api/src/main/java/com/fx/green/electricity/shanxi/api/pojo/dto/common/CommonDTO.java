package com.fx.green.electricity.shanxi.api.pojo.dto.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@Data
public class CommonDTO {

    @Data
    @ApiModel("时间查询DTO")
    @EqualsAndHashCode(callSuper = true)
    public static class DateDTO extends BaseDTO {
        @NotNull(message = "请选择时间")
        @ApiModelProperty("日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date queryDate;
    }

}
