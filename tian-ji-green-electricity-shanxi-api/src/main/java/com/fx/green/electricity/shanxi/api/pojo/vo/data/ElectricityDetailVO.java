package com.fx.green.electricity.shanxi.api.pojo.vo.data;


import com.fx.common.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("实际用电量详情")
@EqualsAndHashCode(callSuper = true)
public class ElectricityDetailVO extends BaseVO {

    @ApiModelProperty("用户名称")
    private String name;

    @ApiModelProperty("数据状态(1.已维护，2.未维护)")
    private int type;

    @ApiModelProperty("日期")
    private Date dateDay;

    @ApiModelProperty("数据")
    private List<String> dataList;

    @ApiModelProperty("时点")
    private List<String> timeFrame;
}
