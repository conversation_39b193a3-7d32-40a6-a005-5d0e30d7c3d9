package com.fx.green.electricity.shanxi.api.pojo.vo.predicted;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
*
**/
@Data
@ApiModel("")
public class VppPredictedReferenceDateVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("预测日期")
    private Date dateDay;
    @ApiModelProperty("参考日")
    private String referenceDate;
    @ApiModelProperty("租户id")
    private Long tenantId;

}