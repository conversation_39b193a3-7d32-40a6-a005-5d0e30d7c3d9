package com.fx.green.electricity.shanxi.api.pojo.dto.electric;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("电量信息请求参数")
public class VppBatteryInformationDTO extends BaseDTO {
    private static final long serialVersionUID = -6617735536510651733L;

    @ApiModelProperty("用户企业信用代码列表")
    private List<String> userCodeList;

    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private List<Date> searchDate;

    @ApiModelProperty("时间")
    private List<String> searchTime;

    @ApiModelProperty("数据类型：96. 96点；24. 24点")
    private Integer type;

    @ApiModelProperty("调整后负荷：0. 调整前；1. 调整后；2.关口表")
    private Integer adjust;

    @ApiModelProperty(name = "选择时间列表", hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private List<Date> selectDate;

    @ApiModelProperty(name = "选择时间列表", hidden = true)
    @JsonFormat(pattern = "yyyy-MM")
    private List<Date> selectTime;

    @ApiModelProperty("交易序列（交易单元）")
    private Integer tradingUnit;

    @ApiModelProperty("用户搜索条件")
    private VppUserSearchDTO userSearch;

    @ApiModelProperty("从最早实际用电量日期开始更新：0:否；1: 是")
    private Integer updateFromEarliestDate;
}
