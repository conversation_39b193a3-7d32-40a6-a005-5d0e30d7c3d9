package com.fx.green.electricity.shanxi.api.pojo.dto.retail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("获取零售合同详情")
public class RetailContractsManageDetailDTO {

    @JsonFormat(pattern = "yyyy-MM")
    private Date signingMonth;
    @ApiModelProperty("retailContractsId")
    private Long retailContractsId;
    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("名字")
    private String name;
}
