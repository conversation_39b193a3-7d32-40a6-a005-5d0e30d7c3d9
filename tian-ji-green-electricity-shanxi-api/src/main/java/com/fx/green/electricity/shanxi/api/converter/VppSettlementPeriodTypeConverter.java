package com.fx.green.electricity.shanxi.api.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.fx.green.electricity.shanxi.api.enums.VppSettlementPeriodType;

/**
 * 用户结算 - 类型
 *
 * <AUTHOR>
 */
public class VppSettlementPeriodTypeConverter implements Converter<Integer> {

    @Override
    public Class supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (value.length() == 0) {
            return null;
        }
        VppSettlementPeriodType seSettlementPeriodType = VppSettlementPeriodType.getSeSettlementPeriodTypeByValue(value);
        if (seSettlementPeriodType == null) {
            return null;
        }
        return seSettlementPeriodType.getCode();
    }

    @Override
    public CellData convertToExcelData(Integer i, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }
}
