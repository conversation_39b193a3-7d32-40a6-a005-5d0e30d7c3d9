package com.fx.green.electricity.shanxi.api.pojo.dto.unit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 机组信息DTO
 */
@Data
@ApiModel("机组信息DTO")
public class GeUnitBasicDTO implements Serializable {
    private static final long serialVersionUID = -6274396898301944175L;
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("机组名称")
    private String unitName;
    @ApiModelProperty("装机容量")
    private BigDecimal capacity;
    @ApiModelProperty("机组类型")
    private Integer type;
    @ApiModelProperty("创建用户ID")
    private Long createUser;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改用户ID")
    private Long updateUser;
    @ApiModelProperty("修改时间")
    private Date updateTime;
}
