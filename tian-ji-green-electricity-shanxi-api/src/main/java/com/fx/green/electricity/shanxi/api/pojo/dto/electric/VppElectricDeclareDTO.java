package com.fx.green.electricity.shanxi.api.pojo.dto.electric;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("申报电量")
@ExcelIgnoreUnannotated
public class VppElectricDeclareDTO extends BaseDTO {


    private static final long serialVersionUID = -2770882990343710872L;

    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("电量数据id")
    private Long electricId;
    @ApiModelProperty("时间")
    private Date dateDay;
    @ApiModelProperty("时段")
    @ExcelProperty("时段")
    private String timeFrame;
    @ApiModelProperty("电量")
    @ExcelProperty("电量(MWh)")
    private BigDecimal electricity;


}