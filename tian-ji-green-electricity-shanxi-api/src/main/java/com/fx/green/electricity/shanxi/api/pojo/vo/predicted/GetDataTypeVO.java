package com.fx.green.electricity.shanxi.api.pojo.vo.predicted;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("调节能力分析VO")
public class GetDataTypeVO {

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户名字")
    private String name;

    @ApiModelProperty("典型曲线天数")
    private List<String> typicalCurveDayList;


    @ApiModelProperty("预测天数")
    private List<String> predictionDayList;


}
