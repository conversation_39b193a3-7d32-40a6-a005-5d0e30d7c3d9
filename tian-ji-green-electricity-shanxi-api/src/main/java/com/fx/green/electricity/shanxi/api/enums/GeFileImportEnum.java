package com.fx.green.electricity.shanxi.api.enums;

import lombok.Getter;

/**
 * 文件导入类型枚举
 */
@Getter
public enum GeFileImportEnum {
    unit_real_electricity(1, "机组实际发电曲线"),
    unit_node_price(2, "机组节点价格"),
    unit_power_predict(3, "机组功率预测"),
    front_day_report(4, "日前申报"),
    tenant_real_electricity(5, "实际用电量");

    private final Integer fileType;
    private final String fileName;

    GeFileImportEnum(Integer fileType, String fileName) {
        this.fileType = fileType;
        this.fileName = fileName;
    }

    public Integer getFileType() {
        return fileType;
    }

    public String getFileName() {
        return fileName;
    }
}
