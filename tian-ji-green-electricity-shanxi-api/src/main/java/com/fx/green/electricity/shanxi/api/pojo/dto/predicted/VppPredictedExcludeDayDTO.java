package com.fx.green.electricity.shanxi.api.pojo.dto.predicted;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 **/
@Data
@ApiModel("关口表数据存在问题的日期")
public class VppPredictedExcludeDayDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户数据")
    private List<DataInfo> list;

    @Data
    public static class DataInfo implements Serializable {
        @ApiModelProperty("用户id")
        private Long userId;
        @ApiModelProperty("用户名字")
        private String userName;
        @ApiModelProperty("统一社会信用代码")
        private String userCode;
        @ApiModelProperty("日期")
        private Date dateDay;
        @ApiModelProperty("租户id")
        private Long tenantId;
    }

}