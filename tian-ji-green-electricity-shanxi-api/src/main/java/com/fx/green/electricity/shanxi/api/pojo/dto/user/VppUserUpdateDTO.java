package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VppUserUpdateDTO extends BaseDTO {


    private static final long serialVersionUID = -7089624879920216575L;
    @ApiModelProperty(value = "新密码")
    @NotNull(message = "新密码不可为空")
    private String password;

    @ApiModelProperty(value = "旧密码")
    @NotNull(message = "旧密码不可为空")
    private String newPassword;

}
