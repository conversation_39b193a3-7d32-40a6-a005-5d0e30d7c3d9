package com.fx.green.electricity.shanxi.api.pojo.dto.predicted;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 获取用户生产状态日历DTO
 **/
@Data
@ApiModel("获取用户生产状态日历DTO")
public class QueryUserProductionDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不可为空")
    private Long userId;

    @ApiModelProperty("查询日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "日期不可为空")
    private Date dateDay;

}