package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 机组实际发电曲线、功率预测、节点价格导入DTO
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("机组导入DTO")
@Data
public class GeUnitImportDTO extends TimeBaseDTO {
    private static final long serialVersionUID = -2158219924769609783L;

    @ApiModelProperty("机组名称")
    private String unitName;
    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date infoDate;
}
