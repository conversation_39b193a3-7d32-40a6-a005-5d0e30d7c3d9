package com.fx.green.electricity.shanxi.api.pojo.dto.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 售电公司代理用户表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("售电公司代理用户表")
public class SellTenantProxyUserImportDTO extends TimeBaseDTO {
    private static final long serialVersionUID = -8522238645650628866L;

    @ApiModelProperty("市场成员名称")
    private String marketUserName;
    @ApiModelProperty("户号")
    private String userCode;
    @ApiModelProperty("计量点")
    private String computePoint;
    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date infoDate;
    @ApiModelProperty("当前日期是否签约")
    private String isAgency;
    @ApiModelProperty("所属交易单元名称")
    private String tradingUnitName;
    @ApiModelProperty("日合计值")
    private BigDecimal daySum;
}
