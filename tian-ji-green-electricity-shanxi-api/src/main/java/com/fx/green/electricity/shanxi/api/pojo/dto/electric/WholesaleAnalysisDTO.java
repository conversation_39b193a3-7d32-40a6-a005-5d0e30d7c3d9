package com.fx.green.electricity.shanxi.api.pojo.dto.electric;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 批发市场分析DTO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel("批发市场分析DTO")
@EqualsAndHashCode(callSuper = true)
public class WholesaleAnalysisDTO extends BaseDTO {

    private static final long serialVersionUID = 826157579858065192L;
    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty("用户id")
    private Long userId;


    @ApiModelProperty("0全部时段 1调控时段 2非调控时段")
    private Integer type;

}
