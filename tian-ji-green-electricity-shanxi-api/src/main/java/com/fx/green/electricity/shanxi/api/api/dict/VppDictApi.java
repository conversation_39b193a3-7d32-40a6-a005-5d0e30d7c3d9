package com.fx.green.electricity.shanxi.api.api.dict;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.GetVppDictionaryDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.dictionary.VppDictionaryVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 字典管理 Api 接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppDictApi")
public interface VppDictApi {

    /**
     * 获取字典信息
     *
     * @param param 获取字典信息 DTO
     * @return 字典信息列表
     */
    @PostMapping("/vppDict/getDictionary")
    DataResult<List<VppDictionaryVO>> getDictionary(@RequestBody GetVppDictionaryDTO param);

}
