package com.fx.green.electricity.shanxi.api.pojo.dto.electric;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import com.fx.green.electricity.shanxi.api.converter.VppElectricQuantityTypeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("电量数据")
public class VppElectricQuantityDTO extends BaseDTO {

    private static final long serialVersionUID = 7986047323875189252L;

    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("售电租户ID")
    private Long tenantId;
    @ApiModelProperty("数据类型 （1. 日前申报电量（普通）；2. 实际用电量）;3. 日前申报电量（战新）；")
    @ExcelProperty(value = "数据类型", converter = VppElectricQuantityTypeConverter.class)
    private Integer type;
    @ApiModelProperty("运行日期")
    private Date runningDate;
    @ApiModelProperty("上传时间")
    private Date uploadTime;
    @ApiModelProperty("上传状态 （0. 未上传；1. 已上传）")
    private Boolean status;
    @ApiModelProperty("电量总和")
    private BigDecimal sumElectricity;
    @ApiModelProperty("电量最大值")
    private BigDecimal maxElectricity;
    @ApiModelProperty("电量最小值")
    private BigDecimal minElectricity;
    @ApiModelProperty("电量平均值")
    private BigDecimal avgElectricity;
    @ApiModelProperty("上传日前申报电量")
    private List<VppElectricDeclareDTO> electricDeclareList;
    @ApiModelProperty("上传实际申报电量")
    private List<VppElectricActualDTO> electricActualList;
    @ApiModelProperty("文件地址")
    private String url;


    @Data
    @EqualsAndHashCode(callSuper = true)
    @ApiModel("查询用电量导入记录DTO")
    public static class QueryDTO extends BaseDTO{

        @ApiModelProperty("数据查询开始时间")
        @NotNull(message = "开始日期不能为空")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date startTime;

        @ApiModelProperty("数据查询结束时间")
        @NotNull(message = "结束日期不能为空")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date endTime;

        @ApiModelProperty("导入状态 (0-否，1-是)")
        private Integer status;


    }
}