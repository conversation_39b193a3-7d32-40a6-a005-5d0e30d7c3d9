package com.fx.green.electricity.shanxi.api.pojo.vo.unit;

import com.fx.common.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 机组信息VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("机组信息VO")
public class GeUnitBasicVO extends BaseVO {
    private static final long serialVersionUID = -3039994678897855723L;

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("机组名称")
    private String unitName;
    @ApiModelProperty("装机容量")
    private BigDecimal capacity;
    @ApiModelProperty("机组类型")
    private Integer type;
    @ApiModelProperty("设备信息列表")
    private List<GeDeviceVO> deviceList;
}
