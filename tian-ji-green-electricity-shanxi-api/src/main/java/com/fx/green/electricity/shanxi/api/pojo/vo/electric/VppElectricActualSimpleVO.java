package com.fx.green.electricity.shanxi.api.pojo.vo.electric;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("实际电量数据")
public class VppElectricActualSimpleVO implements Serializable {

    private static final long serialVersionUID = 5264394490047697599L;

    @ApiModelProperty("日期")
    private String dateDay;
    @ApiModelProperty("时点")
    private String timeFrame;
    @ApiModelProperty("用户编码")
    private String userCode;
    @ApiModelProperty("电量")
    private BigDecimal electricity;
    @ApiModelProperty("最大电量")
    private BigDecimal maxElectricity;
    @ApiModelProperty("最小电量")
    private BigDecimal minElectricity;
    @ApiModelProperty("平均值")
    private BigDecimal avgElectricity;

}
