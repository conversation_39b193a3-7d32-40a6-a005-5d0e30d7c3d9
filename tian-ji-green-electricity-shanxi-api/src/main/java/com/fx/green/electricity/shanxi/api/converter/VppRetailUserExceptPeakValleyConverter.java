package com.fx.green.electricity.shanxi.api.converter;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * 格式化峰谷比
 *
 * <AUTHOR>
 **/
public class VppRetailUserExceptPeakValleyConverter implements Converter<Integer> {
    @Override
    public Class<Integer> supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    @Override
    public CellData<Integer> convertToExcelData(Integer data, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (ObjectUtil.isNotNull(data)) {
            if (data == 1) {
                return new CellData<>("峰时用户");
            } else {
                return new CellData<>("谷时用户");
            }
        } else {
            return new CellData<>();
        }


    }
}

