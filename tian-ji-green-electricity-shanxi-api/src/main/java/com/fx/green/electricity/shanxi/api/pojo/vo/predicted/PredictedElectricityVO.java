package com.fx.green.electricity.shanxi.api.pojo.vo.predicted;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 预测用电量返回数据VO
 *
 * <AUTHOR>
 */
@Data
public class PredictedElectricityVO implements Serializable {

    private static final long serialVersionUID = -6179139832538066374L;

    @ApiModelProperty("预测日期")
    private Date dateDay;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户名字")
    private String name;

    @ApiModelProperty("时点")
    private String timeFrame;

    @ApiModelProperty("预测电量")
    private BigDecimal electricity;

    @ApiModelProperty("预测维度 1.日 2.旬 3.月")
    private Integer dimension;

}
