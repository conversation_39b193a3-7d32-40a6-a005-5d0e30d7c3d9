package com.fx.green.electricity.shanxi.api.pojo.vo.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("即将失效的用户")
public class ExpireUserVO {

    @ApiModelProperty("虚拟电厂Id")
    private String name;

    @ApiModelProperty("失效日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireDate;
}
