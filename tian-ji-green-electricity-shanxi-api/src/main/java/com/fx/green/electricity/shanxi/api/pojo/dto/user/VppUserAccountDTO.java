package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 基础信息DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VppUserAccountDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 8445529111099945039L;

    @ApiModelProperty("主键id")
    private Long id;

    @NotBlank(message = "负荷用户户号不能为空")
    @ApiModelProperty("负荷用户户号")
    private String consNo;

    @NotBlank(message = "户号名称不能为空")
    @ApiModelProperty("户号名称")
    private String consName;

    @NotNull(message = "额定功率不能为空")
    @ApiModelProperty("额定功率")
    private BigDecimal ratedPower;

    @NotNull(message = "电压等级不能为空")
    @ApiModelProperty("电压等级(1.220Kv 2.110Kv 3.35Kv 4.10Kv 5.6Kv 6.380V 7.220V)")
    private Integer volLevel;

    @NotNull(message = "聚合容量不能为空")
    @ApiModelProperty("聚合容量")
    private BigDecimal ratedCapacity;

    @NotBlank(message = "位置code(到区)不能为空")
    @ApiModelProperty("位置code(到区)")
    private String areaCode;

    @NotBlank(message = "所在区县不能为空")
    @ApiModelProperty("所在区县")
    private String districtAddr;

    @NotBlank(message = "位置code(详细信息)不能为空")
    @ApiModelProperty("位置code(详细信息)")
    private String areaCodeDetail;

    @NotBlank(message = "详细地址不能为空")
    @ApiModelProperty("详细地址")
    private String deptAddress;

    @NotBlank(message = "经度不能为空")
    @ApiModelProperty("经度")
    private String longitude;

    @NotBlank(message = "纬度不能为空")
    @ApiModelProperty("纬度")
    private String lat;

    @NotBlank(message = "并网馈线不能为空")
    @ApiModelProperty("并网馈线")
    private String conFeeder;

    @NotBlank(message = "并网变电站不能为空")
    @ApiModelProperty("并网变电站")
    private String conTransSub;

    @ApiModelProperty("供电单位编码")
    @NotBlank(message = "供电单位编码不能为空")
    private String powerSupplyOrgNo;

    @ApiModelProperty("供电电压编码")
    @NotBlank(message = "供电电压编码不能为空")
    private String voltType;

    @ApiModelProperty("行业类型编码")
    @NotBlank(message = "行业类型编码不能为空")
    private String tradeType;

    @NotBlank(message = "资源类型编码不能为空")
    @ApiModelProperty("包含资源类型编码")
    private String resourceType;

    @NotBlank(message = "响应时间级别不能为空")
    @ApiModelProperty("响应时间级别")
    private String responseLevel;

    @NotBlank(message = "用电地址不能为空")
    @ApiModelProperty("用电地址")
    private String elecAddr;

}
