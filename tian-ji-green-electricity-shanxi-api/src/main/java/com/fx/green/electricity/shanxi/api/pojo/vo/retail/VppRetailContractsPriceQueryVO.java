package com.fx.green.electricity.shanxi.api.pojo.vo.retail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
*
**/
@Data
@ApiModel("2025零售合同导入使用价格")
public class VppRetailContractsPriceQueryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("零售合约月份")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signingMonth;

}