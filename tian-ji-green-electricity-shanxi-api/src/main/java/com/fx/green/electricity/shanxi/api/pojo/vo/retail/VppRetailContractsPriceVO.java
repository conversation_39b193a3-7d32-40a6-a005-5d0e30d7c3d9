package com.fx.green.electricity.shanxi.api.pojo.vo.retail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 **/
@Data
@ApiModel("2025零售合同导入使用价格")
@ExcelIgnoreUnannotated
public class VppRetailContractsPriceVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("零售合约月份")
    @ExcelProperty(value = "零售合约月份", index = 0)
    private Date signingMonth;

    @ApiModelProperty("时段")
    @ExcelProperty(value = "时段", index = 1)
    private String timeFrame;

    @ExcelProperty(value = "价格类型", index = 2)
    @ApiModelProperty("1.  (分时)年、月及旬6:4加权出清电价 2.（分时）年、月、旬及日前现货加权出清电价")
    private Integer type;

    @ApiModelProperty("价格")
    @ExcelProperty(value = "价格", index = 3)
    private BigDecimal price;

    @ApiModelProperty("1.系统预算 2.零售商城出清")
    private Integer dataSources;

    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改时间")
    private Date updateTime;

}