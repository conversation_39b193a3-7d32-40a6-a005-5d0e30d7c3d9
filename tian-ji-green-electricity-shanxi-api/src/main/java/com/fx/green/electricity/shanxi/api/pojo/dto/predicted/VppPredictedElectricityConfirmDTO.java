package com.fx.green.electricity.shanxi.api.pojo.dto.predicted;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 編輯修改预测电量
 * <AUTHOR>
 **/
@Data
@ApiModel("編輯修改预测电量")
public class VppPredictedElectricityConfirmDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用戶ID")
    private Long userId;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("预测维度 1.日 2.上旬 3.中旬 4.下旬 5.月")
    private Integer dimension;

    @ApiModelProperty("查询日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateDay;


}