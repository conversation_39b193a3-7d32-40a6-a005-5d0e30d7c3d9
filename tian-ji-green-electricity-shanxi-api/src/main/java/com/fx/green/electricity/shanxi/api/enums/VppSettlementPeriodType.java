package com.fx.green.electricity.shanxi.api.enums;

import lombok.Getter;

/**
 * 用户结算 - 定价方式
 *
 * <AUTHOR>
 **/
@Getter
public enum VppSettlementPeriodType {
    /**
     * 定价方式
     */
    FIRST_OF_A_MONTH(1, "上旬 "),
    MIDDLE_OF_A_MONTH(2, "中旬"),
    END_OF_A_MONTH(3, "下旬"),
    MONTH(4, "按月");

    private final int code;
    private final String value;

    VppSettlementPeriodType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static VppSettlementPeriodType getSeSettlementPeriodTypeByValue(String value) {
        for (VppSettlementPeriodType periodType : VppSettlementPeriodType.values()) {
            if (periodType.value.equals(value)) {
                return periodType;
            }
        }
        return null;
    }
}
