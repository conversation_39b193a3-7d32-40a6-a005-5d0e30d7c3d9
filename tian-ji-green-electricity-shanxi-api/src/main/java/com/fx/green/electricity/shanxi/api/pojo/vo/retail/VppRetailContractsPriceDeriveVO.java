package com.fx.green.electricity.shanxi.api.pojo.vo.retail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 **/
@Data
@ApiModel("2025零售合同导入使用价格")
@ExcelIgnoreUnannotated
public class VppRetailContractsPriceDeriveVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "月份", index = 0)
    private String signingMonth;

    @ExcelProperty(value = "时点", index = 1)
    private String timeFrame;

    @ExcelProperty(value = "价格类型", index = 2)
    private String type;

    @ExcelProperty(value = "价格", index = 3)
    private BigDecimal price;

}