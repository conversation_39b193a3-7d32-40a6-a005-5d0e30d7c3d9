package com.fx.green.electricity.shanxi.api.pojo.vo.predicted;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 各旬的天数
 *
 * <AUTHOR>
 **/
@Data
@ApiModel("各旬的天数")
public class ExcessMLDeclareDateNumVO implements Serializable {

    private static final long serialVersionUID = -9166979302608571839L;
    @ApiModelProperty("全部天数")
    private Integer totalDays;
    @ApiModelProperty("上旬天数")
    private Integer firstDecadeDays;
    @ApiModelProperty("中旬天数")
    private Integer secondDecadeDays;
    @ApiModelProperty("下旬天数")
    private Integer thirdDecadeDays;

    @ApiModelProperty("上旬天数对应List")
    private List<String> firstDecadeRange;
    @ApiModelProperty("中旬天数对应List")
    private List<String> secondDecadeRange;
    @ApiModelProperty("下旬天数对应List")
    private List<String> thirdDecadeRange;

}
