package com.fx.green.electricity.shanxi.api.pojo.vo.predicted;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("用户或者户号电量VO")
public class UserAnalysisVO {
    @ApiModelProperty("时间列表")
    private List<String> dateList;
    @ApiModelProperty("预测电量列表")
    private List<String> electricityList;
    @ApiModelProperty("实际用电量列表")
    private List<String> realElectricityList;
    @ApiModelProperty("预测用电量总和")
    private BigDecimal electricityTotal;
    @ApiModelProperty("实际用电量总和")
    private BigDecimal realElectricityTotal;

    public void init() {
        this.dateList = new ArrayList<>();
        this.electricityList = new ArrayList<>();
        this.realElectricityList = new ArrayList<>();
    }
}