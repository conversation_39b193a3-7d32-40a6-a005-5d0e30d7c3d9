package com.fx.green.electricity.shanxi.api.pojo.dto.unit;

import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 设备信息DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("设备信息DTO")
public class GeDeviceDTO extends BaseDTO {
    private static final long serialVersionUID = 4802101084838943949L;


    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("租户id")
    private Long tenantId;
    @ApiModelProperty("机组id")
    private Long unitId;
    @ApiModelProperty("设备名称")
    private String deviceName;
    @ApiModelProperty("设备编号")
    private String deviceCode;
    @ApiModelProperty("详细地址")
    private String address;
    @ApiModelProperty("经纬度")
    private String coordinate;
    @ApiModelProperty("创建用户ID")
    private Long createUser;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改用户ID")
    private Long updateUser;
    @ApiModelProperty("修改时间")
    private Date updateTime;
}
