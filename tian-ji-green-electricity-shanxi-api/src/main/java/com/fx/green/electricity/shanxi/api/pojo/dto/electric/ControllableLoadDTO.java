package com.fx.green.electricity.shanxi.api.pojo.dto.electric;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 可控负荷DTO
 * <AUTHOR>
 */
@Data
@ApiModel("可控负荷DTO")
@EqualsAndHashCode(callSuper = true)
public class ControllableLoadDTO extends BaseDTO {

    @ApiModelProperty("数据查询开始时间")
    @NotNull(message = "开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty("数据查询结束时间")
    @NotNull(message = "结束日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @NotNull(message = "请选择数据展现形式")
    @ApiModelProperty("数据转换(1:96点,2:24点)")
    private Integer convert;

    @NotNull(message = "请选择预测来源")
    @ApiModelProperty("预测来源(1:关口表,2:实际用电量中位数)")
    private Integer forecastSource;

    @ApiModelProperty("用户id")
    private Long userId;

}
