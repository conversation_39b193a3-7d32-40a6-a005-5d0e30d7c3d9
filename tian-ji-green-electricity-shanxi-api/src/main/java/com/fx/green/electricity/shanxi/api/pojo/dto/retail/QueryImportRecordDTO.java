package com.fx.green.electricity.shanxi.api.pojo.dto.retail;

import com.fx.common.dto.BaseDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 **/
@Data
@ApiModel("导入零售合同DTO")
public class QueryImportRecordDTO extends BaseDTO {

    private static final long serialVersionUID = -1088622904323141299L;


    @ApiModelProperty("文件地址")
    private String url;
    @ApiModelProperty("文件名称")
    private String name;
    @ApiModelProperty("id")
    private Long retailContractsId;
    @ApiModelProperty("解析数据")
    private List<VppRetailContractsManageVO> list;


}