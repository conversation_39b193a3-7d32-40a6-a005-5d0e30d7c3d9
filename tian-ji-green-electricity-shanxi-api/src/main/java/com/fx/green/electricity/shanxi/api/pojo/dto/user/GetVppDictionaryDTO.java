package com.fx.green.electricity.shanxi.api.pojo.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取字典表 DTO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel("获取字典表")
public class GetVppDictionaryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("类型1.VPP资源类型 2.VPP电厂类型 3.VPP市场类型 4.VPP机组种类 5.VPP供电单位编码 6VPP 行业类型编码  7.VPP 分路类型  8.VPP 设备类型 9.VPP 供电电压 10.VPP 响应级别  11.VPP 投运状态  12.VPP 运行状态  13.VPP 交易品种 ")
    private String type;

}