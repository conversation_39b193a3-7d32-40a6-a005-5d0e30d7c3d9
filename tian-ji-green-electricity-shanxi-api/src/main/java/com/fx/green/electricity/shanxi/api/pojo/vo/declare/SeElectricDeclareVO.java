package com.fx.green.electricity.shanxi.api.pojo.vo.declare;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("现货数据导出")
public class SeElectricDeclareVO implements Serializable {

    @ApiModelProperty("时段")
    @ExcelProperty(value = "时段", index = 0)
    private String timeFrame;
    @ApiModelProperty("电量")
    @ExcelProperty(value = "电量", index = 1)
    private BigDecimal electricity;

}