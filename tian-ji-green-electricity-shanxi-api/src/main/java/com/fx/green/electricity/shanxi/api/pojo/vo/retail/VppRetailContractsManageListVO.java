package com.fx.green.electricity.shanxi.api.pojo.vo.retail;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("零售合同管理表")
public class VppRetailContractsManageListVO implements Serializable {

    private static final long serialVersionUID = -4116906849119135378L;
        @ExcelProperty(value = "用户名称",index =0)
    @ApiModelProperty("用户名字")
    private String name;

    @ApiModelProperty("签约月份")
    @ExcelProperty(value = "签约月份",index =1)
    private String month;

    @ApiModelProperty("签约月份")
    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM")
    private Date signingMonth;

    @ApiModelProperty("商品名称")
    @ExcelProperty(value = "商品名称",index =2)
    private String tradeName;

    @ApiModelProperty("红利分享系数")
    @ExcelProperty(value = "红利系数",index =3)
    private BigDecimal dividendSharCoefficient;

    @ExcelProperty(value = "预估结算电价（元/MWh）",index =4)
    @ApiModelProperty("预估结算电价（元/MWh）")
    private BigDecimal price;

    @ExcelIgnore
    @ApiModelProperty("零售合同表对应月份id")
    private Long retailContractsId;
    @ExcelIgnore
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("价差")
    private BigDecimal priceDifference;



}